.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  
  // 全屏模式样式
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  }

  .ant-card-head {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 16px 16px 0 0;
  }

  .ant-card-head-title {
    font-weight: 600;
    font-size: 16px;
  }
}

.metric {
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
  }
}

.metricLabel {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.metricValue {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-top: 8px;
}

.timestamp {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 16px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

// Git风格文件对比容器
.fileCompareContainer {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: white;
  
  .ant-list-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s;
    
    &:hover {
      background-color: #fafafa;
    }
    
    &:last-child {
      border-bottom: none;
    }
  }
}

// 文件列表项
.fileItem {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  
  &:hover {
    background: #f5f5f5;
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  .ant-list-item-meta {
    align-items: flex-start;
  }
  
  .ant-list-item-meta-avatar {
    margin-right: 12px;
    margin-top: 4px;
  }
  
  .ant-list-item-meta-content {
    flex: 1;
  }
  
  .ant-list-item-meta-title {
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .ant-list-item-meta-description {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    line-height: 1.3;
  }
  
  .ant-list-item-action {
    margin-left: 16px;
    
    li {
      padding: 0 4px;
    }
  }
}

// 文件状态图标
.fileStatusIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #f0f0f0;
  color: #666;
  font-size: 14px;
}

// Git状态标签样式增强
.gitStatusTag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  
  .anticon {
    margin-right: 4px;
  }
  
  // 不同状态的样式
  &.synced {
    background: linear-gradient(135deg, #b7eb8f 0%, #52c41a 100%);
    color: white;
  }
  
  &.modified {
    background: linear-gradient(135deg, #ffd666 0%, #fa8c16 100%);
    color: white;
  }
  
  &.added {
    background: linear-gradient(135deg, #91d5ff 0%, #1890ff 100%);
    color: white;
  }
  
  &.deleted {
    background: linear-gradient(135deg, #ffadd2 0%, #f5222d 100%);
    color: white;
  }
  
  &.missing {
    background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
    color: white;
  }
  
  &.conflict {
    background: linear-gradient(135deg, #d3adf7 0%, #722ed1 100%);
    color: white;
  }
}

// 文件树容器保持原样（兼容性）
.fileTreeContainer {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: white;
}

.fileTree {
  .ant-tree-node-content-wrapper {
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.ant-tree-node-selected {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
    }
  }
  
  .ant-tree-title {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .status-tag {
      margin-left: auto;
    }
  }
  
  .ant-tree-iconEle {
    margin-right: 8px;
  }
  
  .ant-tree-indent-unit {
    width: 20px;
  }
}

.fullscreenTree {
  .ant-tree-node-content-wrapper {
    padding: 6px 12px;
    border-radius: 6px;
    margin: 2px 0;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.ant-tree-node-selected {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
    }
  }
  
  .ant-tree-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    
    .status-tag {
      margin-left: auto;
    }
    
    .file-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .file-name {
        font-weight: 500;
      }
      
      .file-path {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
  
  .ant-tree-iconEle {
    margin-right: 12px;
    font-size: 16px;
  }
  
  .ant-tree-indent-unit {
    width: 24px;
  }
  
  .ant-tree-switcher {
    width: 24px;
    height: 28px;
    line-height: 28px;
  }
}

.treeTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  .ant-tag {
    margin-left: 8px;
  }
}

.statusTag {
  margin-left: 8px;
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .title {
    font-size: 24px;
  }
  
  .metric {
    padding: 12px;
  }
  
  .metricValue {
    font-size: 16px;
  }
  
  .fileItem {
    padding: 8px 12px;
    
    .ant-list-item-action {
      margin-left: 8px;
      
      li {
        padding: 0 2px;
      }
    }
  }
}

@media (max-width: 576px) {
  .title {
    font-size: 20px;
  }
  
  .card {
    margin-bottom: 16px;
  }
  
  .fileItem {
    .ant-list-item-meta-title {
      font-size: 13px;
    }
    
    .ant-list-item-meta-description {
      font-size: 11px;
    }
  }
}

// 全屏模式样式
.fullscreenDrawer {
  .ant-drawer-body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
}

// 进度条自定义样式
.ant-progress-line {
  .ant-progress-bg {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
}

// 表格样式优化
.ant-table {
  .ant-table-thead > tr > th {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-weight: 600;
  }
  
  .ant-table-tbody > tr:hover > td {
    background: #e6f7ff;
  }
}

// 按钮样式优化
.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  
  &:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

// 标签样式优化
.ant-tag {
  border-radius: 12px;
  font-weight: 500;
  border: none;
  
  &.ant-tag-green {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
  }
  
  &.ant-tag-red {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
  }
  
  &.ant-tag-orange {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
  }
  
  &.ant-tag-blue {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
  }
  
  &.ant-tag-purple {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
    color: white;
  }
}

// 列表组件样式优化
.ant-list {
  .ant-list-item {
    border-bottom: 1px solid #f0f0f0;
    
    &:hover {
      background: rgba(24, 144, 255, 0.05);
    }
  }
  
  .ant-pagination {
    margin-top: 16px;
    text-align: center;
  }
}

// 差异详情模态框样式
.diffModal {
  .ant-modal-body {
    background: #fafafa;
  }
  
  .diffContent {
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    max-height: 400px;
    overflow: auto;
  }
}

// Ant Design 组件样式覆盖
.ant-card-extra {
  .ant-space {
    flex-wrap: wrap;
  }
}

.ant-drawer-wrapper-body {
  .ant-drawer-body {
    padding: 0;
  }
}

// 自定义滚动条
.fileCompareContainer::-webkit-scrollbar,
.fileTreeContainer::-webkit-scrollbar {
  width: 6px;
}

.fileCompareContainer::-webkit-scrollbar-track,
.fileTreeContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.fileCompareContainer::-webkit-scrollbar-thumb,
.fileTreeContainer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.fileCompareContainer::-webkit-scrollbar-thumb:hover,
.fileTreeContainer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} 