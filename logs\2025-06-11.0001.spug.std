2025-06-11 18:09:41  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 18:09:41  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 nb_log模块导入成功
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - 

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：   https://funboost.readthedocs.io/zh-cn/latest/  
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - 18:09:41  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/Documents/GitHub/spug 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录  的funboost_config.py文件，
    如果没有 /funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/Documents/GitHub/spug 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/Documents/GitHub/spug/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    

2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001E8D8A95890>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\pkey.py:82: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.
  "cipher": algorithms.TripleDES,
C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:253: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.
  "class": algorithms.TripleDES,
2025-06-11 18:09:42 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 18:09:42 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
funboost模块导入成功
2025-06-11 20:29:00  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 20:29:00  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 20:29:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:29:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:29:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:29:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
=== 检查远程文件夹 ===
远程文件夹总数: 2
ID: 2
名称: GPU-P800
路径: \\***********\devicetest\00.固件与驱动\7.GPU\昆仑芯\GPU-P800 8-GPU-96GB
用户名: None
是否激活: True
创建时间: 2025-06-11 16:17:20.603158
--------------------------------------------------
ID: 1
名称: 测试-昆仑芯GPU-P800
路径: \\***********\devicetest\00.固件与驱动\7.GPU\昆仑芯\GPU-P800 8-GPU-96GB
用户名: devicetest
是否激活: True
创建时间: 2025-06-11 16:07:23.431986
--------------------------------------------------

=== 检查缓存数据 ===
缓存记录总数: 83
ID: 83
检查失败: 'RemoteFolderCache' object has no attribute 'folder_id'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\check_database.py", line 36, in check_database
    print(f"文件夹ID: {cache.folder_id}")
                       ^^^^^^^^^^^^^^^
AttributeError: 'RemoteFolderCache' object has no attribute 'folder_id'
2025-06-11 20:29:25  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 20:29:25  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 20:29:25 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:29:25 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:29:26 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:29:26 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
=== 检查远程文件夹 ===
远程文件夹总数: 2
ID: 2
名称: GPU-P800
路径: \\***********\devicetest\00.固件与驱动\7.GPU\昆仑芯\GPU-P800 8-GPU-96GB
用户名: None
是否激活: True
创建时间: 2025-06-11 16:17:20.603158
--------------------------------------------------
ID: 1
名称: 测试-昆仑芯GPU-P800
路径: \\***********\devicetest\00.固件与驱动\7.GPU\昆仑芯\GPU-P800 8-GPU-96GB
用户名: devicetest
是否激活: True
创建时间: 2025-06-11 16:07:23.431986
--------------------------------------------------

=== 检查缓存数据 ===
缓存记录总数: 83
ID: 83
文件夹ID: 2
文件夹名称: GPU-P800
路径: XDR/*******
缓存时间: 2025-06-11 17:30:36.477266
是否有效: True
文件数量: 1
------------------------------
ID: 82
文件夹ID: 2
文件夹名称: GPU-P800
路径: XDR/*******
缓存时间: 2025-06-11 17:30:35.488112
是否有效: True
文件数量: 1
------------------------------
ID: 81
文件夹ID: 2
文件夹名称: GPU-P800
路径: XDR
缓存时间: 2025-06-11 17:30:34.530672
是否有效: True
文件数量: 2
------------------------------
ID: 80
文件夹ID: 2
文件夹名称: GPU-P800
路径: XCCL/*******
缓存时间: 2025-06-11 17:30:33.441783
是否有效: True
文件数量: 1
------------------------------
ID: 79
文件夹ID: 2
文件夹名称: GPU-P800
路径: XCCL
缓存时间: 2025-06-11 17:30:32.569863
是否有效: True
文件数量: 3
------------------------------
2025-06-11 20:32:00  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 20:32:00  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 20:32:00 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:32:00 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:32:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:32:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
=== 测试远程文件夹预加载功能 ===

1. 检查数据库中的远程文件夹...
   找到 2 个有效的远程文件夹配置
   ✓ 使用文件夹: GPU-P800 (ID: 2)
   路径: \\***********\devicetest\00.固件与驱动\7.GPU\昆仑芯\GPU-P800 8-GPU-96GB

2. 检查现有缓存状态...
   找到 73 个缓存记录
   - 路径: 'XDR/*******' | 文件数: 1 | 过期: True
   - 路径: 'XDR/*******' | 文件数: 1 | 过期: True
   - 路径: 'XDR' | 文件数: 2 | 过期: True
   - 路径: 'XCCL/*******' | 文件数: 1 | 过期: True
   - 路径: 'XCCL' | 文件数: 3 | 过期: True

3. 测试获取缓存文件...
2025-06-11 20:32:01 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:68" - __init__ - ERROR - funboost初始化失败: Field 'broker_kind' defined on a base class was overridden by a non-annotated attribute. All field definitions, including overrides, require a type annotation.

For further information visit https://errors.pydantic.dev/2.11/u/model-field-overridden
   ✓ 成功获取根目录文件列表: 33 个文件
   来源: 实时获取
   前几个文件/文件夹:
   - .mozilla (folder)
   - .bash_logout (unknown)
   - .bash_profile (unknown)
   - .bashrc (unknown)
   - .cache (folder)

4. 测试缓存服务基本功能...
2025-06-11 20:32:17 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:17 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:22 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:22 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
   ✓ 刷新所有缓存: 成功
   ✓ 缓存服务状态检查: 不可用

=== 测试结果总结 ===
✓ 远程文件夹缓存服务可正常导入和初始化
✓ 数据库中存在有效的远程文件夹配置
✓ 缓存记录存在且可以正常读取
✓ 可以通过缓存服务获取文件列表
✅ 预加载功能基本正常，应该能够正常处理前端请求

=== 前端API调用建议 ===
现在你可以通过以下方式访问预加载的文件数据：
1. GET /api/file/remote/files/?folder_id=1&path=
2. POST /api/file/remote/cache/ (触发预加载)
3. 前端应该能够正常显示缓存的文件列表

2025-06-11 20:38:12  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 20:38:12  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 20:38:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:38:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:38:12 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:38:12 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
=== 测试新添加的缓存方法 ===

✓ 使用文件夹: GPU-P800 (ID: 2)

1. 测试 get_cache_status 方法...
2025-06-11 20:38:12 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:68" - __init__ - ERROR - funboost初始化失败: Field 'broker_kind' defined on a base class was overridden by a non-annotated attribute. All field definitions, including overrides, require a type annotation.

For further information visit https://errors.pydantic.dev/2.11/u/model-field-overridden
   ✓ 成功获取缓存状态
   - 总文件夹数: 2
   - 有缓存的文件夹数: 2
   - 总缓存条目数: 83
   - 缓存大小: 0.01 MB

2. 测试 refresh_folder_cache 方法（非递归）...
   ✓ 非递归刷新结果: 成功

3. 测试 cache_entire_file_tree 方法...
   正在启动文件树缓存（这可能需要一些时间）...
2025-06-11 20:38:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:270" - cache_entire_file_tree - INFO - 开始缓存整个文件树: GPU-P800 (ID: 2)
