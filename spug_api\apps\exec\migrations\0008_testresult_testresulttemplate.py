# Generated by Django 2.2.28 on 2025-06-18 14:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
        ('exec', '0007_add_step_interval'),
    ]

    operations = [
        migrations.CreateModel(
            name='TestResultTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='模板名称', max_length=100)),
                ('description', models.TextField(blank=True, help_text='模板描述', null=True)),
                ('category', models.CharField(choices=[('gpu', 'GPU性能测试'), ('cpu', 'CPU性能测试'), ('memory', '内存测试'), ('network', '网络测试'), ('storage', '存储测试'), ('custom', '自定义测试')], default='custom', max_length=50)),
                ('expected_metrics', models.TextField(default='[]', help_text='期望的指标列表JSON')),
                ('extraction_patterns', models.TextField(default='[]', help_text='提取模式配置JSON')),
                ('validation_rules', models.TextField(default='{}', help_text='验证规则JSON')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='account.User')),
            ],
            options={
                'verbose_name': '测试结果模板',
                'verbose_name_plural': '测试结果模板',
                'db_table': 'exec_test_result_templates',
            },
        ),
        migrations.CreateModel(
            name='TestResult',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_name', models.CharField(help_text='测试计划名称', max_length=200)),
                ('task_name', models.CharField(blank=True, help_text='关联任务名称', max_length=200, null=True)),
                ('metrics', models.TextField(default='{}', help_text='性能指标数据JSON')),
                ('raw_log', models.TextField(blank=True, help_text='原始日志内容', null=True)),
                ('extraction_time', models.DateTimeField(auto_now_add=True, help_text='结果提取时间')),
                ('total_metrics', models.IntegerField(default=0, help_text='总指标数量')),
                ('confirmed_metrics', models.IntegerField(default=0, help_text='已确认指标数量')),
                ('ai_confidence', models.FloatField(default=0.0, help_text='AI识别平均置信度')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_test_results', to='account.User')),
                ('execution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='test_results', to='exec.ExecHistory')),
            ],
            options={
                'verbose_name': '测试结果',
                'verbose_name_plural': '测试结果',
                'db_table': 'exec_test_results',
                'ordering': ['-created_at'],
            },
        ),
    ]
