2025-06-30 15:48:29 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\runserver.py", line 60, in execute
    super().execute(*args, **options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py", line 59, in handle
    super().handle(*args, **options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\runserver.py", line 95, in handle
    self.run(**options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\runserver.py", line 102, in run
    autoreload.run_with_reloader(self.inner_run, **options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py", line 598, in run_with_reloader
    start_django(reloader, main_func, *args, **kwargs)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py", line 583, in start_django
    reloader.run(django_main_thread)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py", line 301, in run
    self.run_loop()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py", line 307, in run_loop
    next(ticker)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py", line 347, in tick
    for filepath, mtime in self.snapshot_files():
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py", line 363, in snapshot_files
    for file in self.watched_files():
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py", line 267, in watched_files
    yield from directory.glob(pattern)
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\pathlib.py", line 953, in glob
    for p in selector.select_from(self):
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\pathlib.py", line 407, in _select_from
    for starting_point in self._iterate_directories(parent_path, is_dir, scandir):
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\pathlib.py", line 397, in _iterate_directories
    for p in self._iterate_directories(path, is_dir, scandir):
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\pathlib.py", line 386, in _iterate_directories
    with scandir(parent_path) as scandir_it:
         ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\pathlib.py", line 938, in _scandir
    return os.scandir(self)
           ^^^^^^^^^^^^^^^^
OSError: [WinError 1450] 系统资源不足，无法完成请求的服务。: 'C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\locale\\en_AU'
2025-06-30 16:37:17 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\check_user_permissions.py", line 141, in <module>
EOFError: EOF when reading a line

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\check_user_permissions.py", line 148, in <module>
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\traceback.py", line 180, in print_exc
    print_exception(*sys.exc_info(), limit=limit, file=file, chain=chain)
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\traceback.py", line 125, in print_exception
    te.print(file=file, chain=chain)
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\traceback.py", line 1022, in print
    print(line, file=file, end="")
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\monkey_sys_std.py", line 77, in monkey_sys_stderr
    stderr_raw(msg)
OSError: [Errno 22] Invalid argument
2025-06-30 16:37:17 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\makemigrations.py", line 164, in handle
    changes = autodetector.changes(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 43, in changes
    changes = self._detect_changes(convert_apps, graph)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 185, in _detect_changes
    self.generate_added_fields()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 850, in generate_added_fields
    self._generate_added_field(app_label, model_name, field_name)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 871, in _generate_added_field
    field.default = self.questioner.ask_not_null_addition(field_name, model_name)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 146, in ask_not_null_addition
    choice = self._choice_input(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 98, in _choice_input
    result = input("Select an option: ")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
EOFError: EOF when reading a line
2025-06-30 16:37:17 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\makemigrations.py", line 164, in handle
    changes = autodetector.changes(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 43, in changes
    changes = self._detect_changes(convert_apps, graph)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 185, in _detect_changes
    self.generate_added_fields()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 850, in generate_added_fields
    self._generate_added_field(app_label, model_name, field_name)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 871, in _generate_added_field
    field.default = self.questioner.ask_not_null_addition(field_name, model_name)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 146, in ask_not_null_addition
    choice = self._choice_input(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 98, in _choice_input
    result = input("Select an option: ")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
EOFError: EOF when reading a line
2025-06-30 16:37:17 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\makemigrations.py", line 164, in handle
    changes = autodetector.changes(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 43, in changes
    changes = self._detect_changes(convert_apps, graph)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 185, in _detect_changes
    self.generate_added_fields()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 850, in generate_added_fields
    self._generate_added_field(app_label, model_name, field_name)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 871, in _generate_added_field
    field.default = self.questioner.ask_not_null_addition(field_name, model_name)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 146, in ask_not_null_addition
    choice = self._choice_input(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 98, in _choice_input
    result = input("Select an option: ")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
EOFError: EOF when reading a line
2025-06-30 16:37:17 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\makemigrations.py", line 164, in handle
    changes = autodetector.changes(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 43, in changes
    changes = self._detect_changes(convert_apps, graph)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 185, in _detect_changes
    self.generate_added_fields()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 850, in generate_added_fields
    self._generate_added_field(app_label, model_name, field_name)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 871, in _generate_added_field
    field.default = self.questioner.ask_not_null_addition(field_name, model_name)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 146, in ask_not_null_addition
    choice = self._choice_input(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 98, in _choice_input
    result = input("Select an option: ")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
EOFError: EOF when reading a line
2025-06-30 16:37:17 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\makemigrations.py", line 164, in handle
    changes = autodetector.changes(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 43, in changes
    changes = self._detect_changes(convert_apps, graph)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 185, in _detect_changes
    self.generate_added_fields()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 850, in generate_added_fields
    self._generate_added_field(app_label, model_name, field_name)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 871, in _generate_added_field
    field.default = self.questioner.ask_not_null_addition(field_name, model_name)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 146, in ask_not_null_addition
    choice = self._choice_input(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 98, in _choice_input
    result = input("Select an option: ")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
EOFError: EOF when reading a line
2025-06-30 16:37:17 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\makemigrations.py", line 164, in handle
    changes = autodetector.changes(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 43, in changes
    changes = self._detect_changes(convert_apps, graph)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 185, in _detect_changes
    self.generate_added_fields()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 850, in generate_added_fields
    self._generate_added_field(app_label, model_name, field_name)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 871, in _generate_added_field
    field.default = self.questioner.ask_not_null_addition(field_name, model_name)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 146, in ask_not_null_addition
    choice = self._choice_input(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 98, in _choice_input
    result = input("Select an option: ")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
EOFError: EOF when reading a line
