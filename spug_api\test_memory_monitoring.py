#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试内存监控功能
"""

import sys
import os
import subprocess
import psutil

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_memory_calculation():
    """测试内存计算逻辑"""
    print("=" * 60)
    print("内存监控测试")
    print("=" * 60)
    
    # 方法1：使用free命令
    print("\n--- 方法1：free命令 ---")
    try:
        free_output = subprocess.check_output(['free', '-m']).decode()
        print("Free命令输出:")
        print(free_output)
        
        for line in free_output.split('\n'):
            if line.startswith('Mem:'):
                parts = line.split()
                print(f"解析结果: {parts}")
                
                total = float(parts[1])
                used = float(parts[2])
                free = float(parts[3])
                shared = float(parts[4])
                buff_cache = float(parts[5])
                available = float(parts[6])
                
                print(f"总内存: {total} MB")
                print(f"已使用: {used} MB")
                print(f"空闲: {free} MB")
                print(f"共享: {shared} MB")
                print(f"缓存/缓冲: {buff_cache} MB")
                print(f"可用: {available} MB")
                
                # 不同的计算方式
                method1 = round(used / total * 100, 1)
                method2 = round((total - available) / total * 100, 1)
                method3 = round((used - shared - buff_cache) / total * 100, 1) if (used - shared - buff_cache) > 0 else 0
                
                print(f"\n计算方式1 (used/total): {method1}%")
                print(f"计算方式2 ((total-available)/total): {method2}%")
                print(f"计算方式3 ((used-shared-buff_cache)/total): {method3}%")
                
                # 当前代码使用的方式
                current_method = round((total - available) / total * 100, 1)
                print(f"当前代码使用: {current_method}%")
                
    except Exception as e:
        print(f"Free命令失败: {e}")
    
    # 方法2：使用psutil
    print("\n--- 方法2：psutil ---")
    try:
        memory = psutil.virtual_memory()
        print(f"总内存: {memory.total / (1024*1024*1024):.1f} GB")
        print(f"可用内存: {memory.available / (1024*1024*1024):.1f} GB")
        print(f"已使用: {memory.used / (1024*1024*1024):.1f} GB")
        print(f"使用率: {memory.percent}%")
        print(f"空闲: {memory.free / (1024*1024*1024):.1f} GB")
        
        # 详细信息
        print(f"\n详细信息:")
        print(f"  - total: {memory.total}")
        print(f"  - available: {memory.available}")
        print(f"  - percent: {memory.percent}")
        print(f"  - used: {memory.used}")
        print(f"  - free: {memory.free}")
        if hasattr(memory, 'active'):
            print(f"  - active: {memory.active}")
        if hasattr(memory, 'inactive'):
            print(f"  - inactive: {memory.inactive}")
        if hasattr(memory, 'buffers'):
            print(f"  - buffers: {memory.buffers}")
        if hasattr(memory, 'cached'):
            print(f"  - cached: {memory.cached}")
            
    except Exception as e:
        print(f"psutil失败: {e}")
    
    # 方法3：读取/proc/meminfo
    print("\n--- 方法3：/proc/meminfo ---")
    try:
        with open('/proc/meminfo', 'r') as f:
            meminfo = f.read()
        
        lines = meminfo.split('\n')
        mem_data = {}
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                value = value.strip()
                if 'kB' in value:
                    mem_data[key] = int(value.replace('kB', '').strip())
        
        total_kb = mem_data.get('MemTotal', 0)
        free_kb = mem_data.get('MemFree', 0)
        available_kb = mem_data.get('MemAvailable', 0)
        buffers_kb = mem_data.get('Buffers', 0)
        cached_kb = mem_data.get('Cached', 0)
        
        total_mb = total_kb / 1024
        available_mb = available_kb / 1024
        used_mb = (total_kb - available_kb) / 1024
        
        print(f"总内存: {total_mb:.1f} MB")
        print(f"可用内存: {available_mb:.1f} MB")
        print(f"已使用: {used_mb:.1f} MB")
        print(f"使用率: {(used_mb / total_mb * 100):.1f}%")
        
    except Exception as e:
        print(f"读取/proc/meminfo失败: {e}")

def test_current_implementation():
    """测试当前实现"""
    print("\n" + "=" * 60)
    print("测试当前实现")
    print("=" * 60)
    
    # 模拟当前代码的逻辑
    memory_info = {}
    try:
        free_output = subprocess.check_output(['free', '-m']).decode()
        for line in free_output.split('\n'):
            if line.startswith('Mem:'):
                parts = line.split()
                total = float(parts[1])
                available = float(parts[6])
                memory_info['percent'] = round((total - available) / total * 100, 1)
                break
        print(f"当前实现结果: {memory_info['percent']}%")
    except:
        memory = psutil.virtual_memory()
        memory_info['percent'] = round(memory.percent, 1)
        print(f"psutil备用结果: {memory_info['percent']}%")

if __name__ == "__main__":
    test_memory_calculation()
    test_current_implementation()
