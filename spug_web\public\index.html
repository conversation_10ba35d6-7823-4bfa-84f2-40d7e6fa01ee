<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="H3C异构运维平台 - 企业级自动化运维解决方案"
    />
    <link rel="apple-touch-icon" href="logo.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>H3C异构运维平台</title>
    
    <!-- 完整的 Process 和 Global polyfill -->
    <script>
      // 设置全局变量
      if (typeof global === 'undefined') {
        window.global = window;
      }
      if (typeof globalThis === 'undefined') {
        window.globalThis = window;
      }
      
      // 设置process对象
      window.process = {
        env: {
          NODE_ENV: 'development'
        },
        browser: true,
        version: '',
        versions: { 
          node: '',
          v8: '',
          uv: '',
          zlib: '',
          modules: ''
        },
        nextTick: function(callback) {
          setTimeout(callback, 0);
        },
        platform: 'browser',
        argv: [],
        pid: 1,
        title: 'browser',
        stderr: {},
        stdout: {},
        stdin: {}
      };
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <!-- Stagewise工具栏 - 仅在开发模式下加载 -->
    <script src="%PUBLIC_URL%/stagewise-init.js"></script>
  </body>
</html>
