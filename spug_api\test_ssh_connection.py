#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试SSH连接到**********
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

django.setup()

from apps.host.models import Host

def test_ssh_connection():
    """测试SSH连接"""
    print("=" * 60)
    print("测试SSH连接到**********")
    print("=" * 60)
    
    try:
        # 获取主机配置
        host_obj = Host.objects.filter(hostname='**********').first()
        if not host_obj:
            print("❌ 未找到主机 ********** 的配置")
            return False
        
        print(f"✅ 找到主机配置:")
        print(f"   名称: {host_obj.name}")
        print(f"   主机名: {host_obj.hostname}")
        print(f"   端口: {host_obj.port}")
        print(f"   用户名: {host_obj.username}")
        print(f"   已验证: {host_obj.is_verified}")
        print(f"   有私钥: {'是' if host_obj.pkey else '否'}")
        
        # 测试SSH连接
        print("\n正在测试SSH连接...")
        ssh = host_obj.get_ssh()
        client = ssh.get_client()
        print("✅ SSH连接成功")
        
        # 测试执行命令
        print("\n正在测试命令执行...")
        exit_code, output = ssh.exec_command_raw('ls -la /HDD_Raid/SVN_MODEL_REPO')
        print(f"命令退出码: {exit_code}")
        print(f"命令输出长度: {len(output)} 字符")
        
        if exit_code == 0:
            print("✅ 命令执行成功")
            print("输出内容:")
            print("-" * 40)
            print(output[:500])  # 只显示前500个字符
            print("-" * 40)
            
            # 解析输出
            lines = output.strip().split('\n')
            directories = []
            for line in lines:
                if not line.strip() or line.startswith('total'):
                    continue
                    
                parts = line.split()
                if len(parts) >= 9:
                    permissions = parts[0]
                    name = ' '.join(parts[8:])
                    
                    if name in ['.', '..']:
                        continue
                        
                    if permissions.startswith('d'):
                        directories.append(name)
            
            print(f"\n找到的目录: {directories}")
            return True
        else:
            print(f"❌ 命令执行失败: {output}")
            return False
            
    except Exception as e:
        print(f"❌ SSH连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_method():
    """测试API方法"""
    print("\n" + "=" * 60)
    print("测试API方法")
    print("=" * 60)
    
    try:
        from apps.model_storage.views import LazyLoadTreeView
        
        view = LazyLoadTreeView()
        
        # 测试获取根目录结构
        print("正在测试 get_local_root_structure...")
        children = view.get_local_root_structure()
        
        print(f"返回结果类型: {type(children)}")
        print(f"返回结果长度: {len(children)}")
        
        for i, child in enumerate(children):
            print(f"项目 {i+1}: {child}")
            
        return len(children) > 0 and not any('error' in str(child) for child in children)
        
    except Exception as e:
        print(f"❌ API方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("SSH连接测试")
    print("=" * 60)
    
    tests = [
        ("SSH连接测试", test_ssh_connection),
        ("API方法测试", test_api_method),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
