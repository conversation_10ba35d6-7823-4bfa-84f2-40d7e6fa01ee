/* 拖拽排序样式 */
.rowDragging {
  background: #fafafa !important;
  border: 1px dashed #1890ff !important;
}

.rowDragging td {
  padding: 16px !important;
  visibility: visible !important;
}

.dragHandle {
  cursor: grab;
  color: #999;
  font-size: 16px;
  transition: color 0.3s;
}

.dragHandle:hover {
  color: #1890ff;
}

.dragHandle:active {
  cursor: grabbing;
}

/* 拖拽时的边框提示 */
:global(.ant-table-tbody > tr.drag-over-up td) {
  border-top: 2px dashed #1890ff;
}

:global(.ant-table-tbody > tr.drag-over-down td) {
  border-bottom: 2px dashed #1890ff;
}

:global(.ant-table-tbody > tr.drag-over-gap td) {
  border-bottom: 2px dashed #1890ff;
}

/* 步骤序号样式 */
.stepNumber {
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.stepNumberEnabled {
  background-color: #1890ff;
}

.stepNumberDisabled {
  background-color: #ccc;
}

/* 紧凑布局样式 */
.compactTable {
  font-size: 13px;
}

.compactTable :global(.ant-table-thead > tr > th) {
  padding: 8px 12px !important;
  font-size: 13px;
  font-weight: 600;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.compactTable :global(.ant-table-tbody > tr > td) {
  padding: 6px 12px !important;
  vertical-align: top;
  border-bottom: 1px solid #f0f0f0;
}

.compactTable :global(.ant-table-tbody > tr:hover > td) {
  background: #f9f9f9;
}

/* 紧凑的输入框样式 */
.compactInput {
  border-radius: 4px;
  font-size: 13px;
}

.compactInput :global(.ant-input) {
  padding: 4px 8px;
  font-size: 13px;
  line-height: 1.4;
}

.compactTextarea {
  border-radius: 4px;
  font-size: 12px;
}

.compactTextarea :global(.ant-input) {
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.3;
  min-height: 60px;
}

/* 文件列表紧凑样式 */
.fileList {
  max-height: 120px;
  overflow-y: auto;
}

.fileItem {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
  padding: 3px 6px;
  background: #f6f8fa;
  border-radius: 3px;
  font-size: 11px;
}

.fileItemContent {
  flex: 1;
  min-width: 0;
}

.fileName {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filePath {
  font-size: 10px;
  color: #666;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.targetPathInput {
  font-size: 10px !important;
  padding: 2px 4px !important;
  height: auto !important;
  min-height: 20px !important;
  line-height: 1.2 !important;
}

/* 操作按钮紧凑样式 */
.compactButton {
  padding: 2px 6px;
  font-size: 11px;
  height: auto;
  line-height: 1.2;
}

/* 开关紧凑样式 */
.compactSwitch {
  transform: scale(0.8);
}

/* 命令输入框紧凑样式 */
.commandTextarea {
  border-radius: 4px;
  font-size: 12px;
}

.commandTextarea :global(.ant-input) {
  padding: 6px 8px;
  font-size: 12px;
  line-height: 1.3;
  font-family: 'Courier New', monospace;
  min-height: 50px;
  resize: vertical;
} 