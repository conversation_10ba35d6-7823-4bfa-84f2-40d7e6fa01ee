/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { observer } from 'mobx-react';
import { LockOutlined, FileOutlined } from '@ant-design/icons';
import { Table, Modal, Tooltip, message, Tag, Space } from 'antd';
import { Action } from 'components';
import ComForm from './Form';
import { http, hasPermission } from 'libs';
import store from './store';

@observer
class TableView extends React.Component {
  lockIcon = <Tooltip title="私有配置应用专用，不会被其他应用获取到">
    <LockOutlined style={{marginRight: 5}}/>
  </Tooltip>;

  // 渲染Value列，支持多文件显示
  renderValue = (value, record) => {
    if (store.type === 'task' && value) {
      // 任务类型显示文件列表
      const files = value.split(',').filter(f => f.trim());
      if (files.length === 0) {
        return <span style={{ color: '#999' }}>未选择文件</span>;
      }
      
      if (files.length === 1) {
        // 单个文件
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <FileOutlined style={{ color: '#52c41a', marginRight: 4 }} />
            <span>{files[0]}</span>
          </div>
        );
      }
      
      // 多个文件
      return (
        <div>
          <div style={{ 
            fontSize: '12px', 
            color: '#666', 
            marginBottom: '4px',
            display: 'flex',
            alignItems: 'center'
          }}>
            <FileOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
            配套文件 ({files.length}个):
          </div>
          <div style={{ maxHeight: '120px', overflowY: 'auto' }}>
            {files.map((file, index) => (
              <Tag 
                key={index}
                style={{ 
                  fontSize: '11px', 
                  marginBottom: '2px',
                  padding: '1px 6px',
                  backgroundColor: '#e6f7ff',
                  borderColor: '#91d5ff',
                  borderRadius: '3px',
                  display: 'inline-block',
                  marginRight: '4px',
                  maxWidth: '200px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={file}
              >
                {file}
              </Tag>
            ))}
          </div>
        </div>
      );
    }
    
    // 非任务类型的普通显示
    return value || <span style={{ color: '#999' }}>--</span>;
  };

  columns = [{
    title: store.type === 'task' ? '版本号' : 'Key',
    key: 'key',
    width: store.type === 'task' ? 120 : 200,
    render: info => {
      let prefix = (store.type === 'app' && info.is_public === false) ? this.lockIcon : null;
      let content = info.desc ? <span style={{color: '#1890ff'}}>{info.key}</span> : info.key;
      return <React.Fragment>
        {prefix}
        <Tooltip title={info.desc}>{content}</Tooltip>
      </React.Fragment>
    }
  }, {
    title: store.type === 'task' ? '配套文件' : 'Value',
    dataIndex: 'value',
    render: this.renderValue,
  }, {
    title: '修改人',
    width: 120,
    dataIndex: 'update_user'
  }, {
    title: '修改时间',
    width: 180,
    dataIndex: 'updated_at'
  }, {
    title: '操作',
    width: 120,
    className: hasPermission(`config.${store.type}.edit_config`) ? null : 'none',
    render: info => (
      <Action>
        <Action.Button auth={`config.${store.type}.edit_config`} onClick={() => store.showForm(info)}>编辑</Action.Button>
        <Action.Button
          danger
          auth={`config.${store.type}.edit_config`}
          onClick={() => this.handleDelete(info)}>删除</Action.Button>
      </Action>
    )
  }];

  handleDelete = (text) => {
    const envName = store.type === 'task' ? store.package.name : store.env.name;
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除【${envName}】${store.type === 'task' ? '配套' : '环境'}下的${store.type === 'task' ? '版本配套' : '配置'}【${text['key']}】?`,
      onOk: () => {
        return http.delete('/api/config/', {params: {id: text.id}})
          .then(() => {
            message.success('删除成功');
            store.fetchRecords()
          })
      }
    })
  };

  render() {
    let data = store.records;
    if (store.f_name) {
      data = data.filter(item => item['key'].toLowerCase().includes(store.f_name.toLowerCase()))
    }
    return (
      <React.Fragment>
        <Table
          size="small"
          rowKey="id"
          loading={store.isFetching}
          dataSource={data}
          pagination={{
            showSizeChanger: true,
            showLessItems: true,
            hideOnSinglePage: true,
            showTotal: total => `共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          columns={this.columns}/>
        {store.formVisible && <ComForm/>}
      </React.Fragment>
    )
  }
}

export default TableView
