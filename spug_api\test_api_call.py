#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试API调用
"""

import requests
import json

def test_server_metrics_api():
    """测试服务器指标API"""
    
    # API端点
    base_url = "http://localhost:8000"  # 假设spug运行在8000端口
    api_url = f"{base_url}/model-storage/server-metrics/"
    
    print("测试服务器指标API")
    print("=" * 50)
    print(f"API URL: {api_url}")
    
    try:
        # 不带参数的请求（应该使用默认主机）
        print("\n1. 测试默认主机请求...")
        response = requests.get(api_url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
        
        # 带主机参数的请求
        print("\n2. 测试指定主机请求...")
        params = {'host': '**********'}
        response = requests.get(api_url, params=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查内存使用率是否合理
            if 'data' in data and 'memory_usage' in data['data']:
                memory_usage = data['data']['memory_usage']
                print(f"\n内存使用率: {memory_usage}%")
                if memory_usage > 50:
                    print("⚠️  内存使用率偏高，可能仍在使用本地指标")
                else:
                    print("✅ 内存使用率正常，可能已使用远程指标")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保spug服务正在运行")
        print("提示: 可以运行 'python manage.py runserver' 启动服务")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_with_curl():
    """使用curl测试（如果requests失败）"""
    import subprocess
    
    print("\n" + "=" * 50)
    print("使用curl测试")
    print("=" * 50)
    
    api_url = "http://localhost:8000/model-storage/server-metrics/?host=**********"
    
    try:
        result = subprocess.run([
            'curl', '-s', '-w', '\\nHTTP_CODE:%{http_code}\\n', api_url
        ], capture_output=True, text=True, timeout=10)
        
        print(f"curl输出:")
        print(result.stdout)
        
        if result.stderr:
            print(f"curl错误:")
            print(result.stderr)
            
    except FileNotFoundError:
        print("curl命令不可用")
    except Exception as e:
        print(f"curl测试失败: {e}")

if __name__ == "__main__":
    test_server_metrics_api()
    test_with_curl()
