#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试文件工具类的跨平台兼容性
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否成功"""
    try:
        print("测试导入 file_utils...")
        from apps.model_storage.file_utils import (
            safe_file_ops, network_io_tracker, FileOperationError,
            file_lock_manager, HAS_FCNTL, HAS_MSVCRT
        )
        print("✅ file_utils 导入成功")
        
        print(f"系统信息:")
        print(f"  - 操作系统: {os.name}")
        print(f"  - 支持 fcntl: {HAS_FCNTL}")
        print(f"  - 支持 msvcrt: {HAS_MSVCRT}")
        
        return True
    except Exception as e:
        print(f"❌ file_utils 导入失败: {e}")
        return False

def test_exceptions():
    """测试异常模块导入"""
    try:
        print("测试导入 exceptions...")
        from apps.model_storage.exceptions import (
            ModelStorageException, NetworkException, DatabaseException,
            FileSystemException, log_exception
        )
        print("✅ exceptions 导入成功")
        return True
    except Exception as e:
        print(f"❌ exceptions 导入失败: {e}")
        return False

def test_config():
    """测试配置模块导入"""
    try:
        print("测试导入 config...")
        from apps.model_storage.config import (
            network_config, database_config, security_config
        )
        print("✅ config 导入成功")
        return True
    except Exception as e:
        print(f"❌ config 导入失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    try:
        print("测试文件操作...")
        from apps.model_storage.file_utils import safe_file_ops
        
        # 测试临时目录创建
        temp_dir = safe_file_ops.temp_dir
        print(f"临时目录: {temp_dir}")
        
        # 测试文件路径生成
        test_file = safe_file_ops.get_safe_file_path("test.json", "unittest")
        print(f"测试文件路径: {test_file}")
        
        # 测试权限检查
        parent_dir = os.path.dirname(test_file)
        can_write = safe_file_ops.check_file_permissions(parent_dir, 'write')
        print(f"写入权限: {can_write}")
        
        print("✅ 文件操作测试成功")
        return True
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("跨平台文件工具兼容性测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("异常模块测试", test_exceptions),
        ("配置模块测试", test_config),
        ("文件操作测试", test_file_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
