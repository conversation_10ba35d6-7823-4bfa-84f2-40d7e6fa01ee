#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from django.core.management import call_command
from django.db import connection

print("重新创建exec应用的数据库表...")

try:
    # 删除现有的迁移记录（但保留文件）
    with connection.cursor() as cursor:
        cursor.execute("DELETE FROM django_migrations WHERE app = 'exec'")
        print("已删除exec应用的迁移记录")
    
    # 重新运行迁移
    call_command('migrate', 'exec', '--fake-initial')
    print("已重新应用初始迁移")
    
    # 运行后续迁移
    call_command('migrate', 'exec')
    print("已应用所有迁移")
    
    # 验证表结构
    with connection.cursor() as cursor:
        cursor.execute('PRAGMA table_info(exec_test_plan_executions)')
        columns = cursor.fetchall()
        
        print(f"\n表 exec_test_plan_executions 当前有 {len(columns)} 个字段:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # 检查是否有 step_logs_folder 字段
        has_step_logs_folder = any(col[1] == 'step_logs_folder' for col in columns)
        
        if has_step_logs_folder:
            print("\n✅ step_logs_folder 字段已存在")
        else:
            print("\n❌ step_logs_folder 字段仍然不存在")

except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc() 