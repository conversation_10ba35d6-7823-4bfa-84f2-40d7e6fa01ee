# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.views.generic import View
from django.db.models import F
from libs import json_response, JsonParser, Argument, auth
from apps.app.models import Deploy, App, ConfigTask
from apps.repository.models import Repository
from apps.config.models import *
import json
import re


class EnvironmentView(View):
    def get(self, request):
        query = {}
        if not request.user.is_supper:
            query['id__in'] = request.user.deploy_perms['envs']
        envs = Environment.objects.filter(**query)
        return json_response(envs)

    @auth('config.env.add|config.env.edit')
    def post(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False),
            Argument('name', help='请输入环境名称'),
            Argument('key', help='请输入唯一标识符'),
            Argument('desc', required=False)
        ).parse(request.body)
        if error is None:
            if not re.fullmatch(r'\w+', form.key, re.ASCII):
                return json_response(error='标识符必须为字母、数字和下划线的组合')

            env = Environment.objects.filter(key=form.key).first()
            if env and env.id != form.id:
                return json_response(error=f'唯一标识符 {form.key} 已存在，请更改后重试')
            if form.id:
                Environment.objects.filter(pk=form.id).update(**form)
            else:
                env = Environment.objects.create(created_by=request.user, **form)
                env.sort_id = env.id
                env.save()
        return json_response(error=error)

    @auth('config.env.edit')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('sort', filter=lambda x: x in ('up', 'down'), required=False)
        ).parse(request.body)
        if error is None:
            env = Environment.objects.filter(pk=form.id).first()
            if not env:
                return json_response(error='未找到指定环境')
            if form.sort:
                if form.sort == 'up':
                    tmp = Environment.objects.filter(sort_id__gt=env.sort_id).last()
                else:
                    tmp = Environment.objects.filter(sort_id__lt=env.sort_id).first()
                if tmp:
                    tmp.sort_id, env.sort_id = env.sort_id, tmp.sort_id
                    tmp.save()
            env.save()
        return json_response(error=error)

    @auth('config.env.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            if Deploy.objects.filter(env_id=form.id).exists():
                return json_response(error='该环境已关联了发布配置，请删除相关发布配置后再尝试删除')
            if Repository.objects.filter(env_id=form.id).exists():
                return json_response(error='该环境关联了构建记录，请在删除应用发布/构建仓库中相关记录后再尝试')
            # auto delete configs
            Config.objects.filter(env_id=form.id).delete()
            ConfigHistory.objects.filter(env_id=form.id).delete()
            Environment.objects.filter(pk=form.id).delete()
        return json_response(error=error)


# 新增配套管理API
class PackageView(View):
    @auth('config.package.view')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False)
        ).parse(request.GET)
        if error is None:
            if form.id:
                package = Package.objects.get(pk=form.id)
                return json_response(package)
            packages = Package.objects.all()
            return json_response(packages)
        return json_response(error=error)

    @auth('config.package.add|config.package.edit')
    def post(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False),
            Argument('name', help='请输入配套名称'),
            Argument('key', help='请输入唯一标识符'),
            Argument('desc', required=False)
        ).parse(request.body)
        if error is None:
            if not re.fullmatch(r'\w+', form.key, re.ASCII):
                return json_response(error='标识符必须为字母、数字和下划线的组合')

            package = Package.objects.filter(key=form.key).first()
            if package and package.id != form.id:
                return json_response(error=f'唯一标识符 {form.key} 已存在，请更改后重试')
            if form.id:
                Package.objects.filter(pk=form.id).update(**form)
            else:
                package = Package.objects.create(created_by=request.user, **form)
                package.sort_id = package.id
                package.save()
        return json_response(error=error)

    @auth('config.package.edit')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='参数错误'),
            Argument('sort', filter=lambda x: x in ('up', 'down'), required=False)
        ).parse(request.body)
        if error is None:
            package = Package.objects.filter(pk=form.id).first()
            if not package:
                return json_response(error='未找到指定配套')
            if form.sort:
                if form.sort == 'up':
                    tmp = Package.objects.filter(sort_id__gt=package.sort_id).last()
                else:
                    tmp = Package.objects.filter(sort_id__lt=package.sort_id).first()
                if tmp:
                    tmp.sort_id, package.sort_id = package.sort_id, tmp.sort_id
                    tmp.save()
            package.save()
        return json_response(error=error)

    @auth('config.package.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            # 检查是否有任务版本配套关联
            if TaskVersionPackage.objects.filter(package_id=form.id).exists():
                return json_response(error='该配套已被任务使用，请先删除相关版本配套后再尝试删除')
            # auto delete configs
            Config.objects.filter(package_id=form.id).delete()
            ConfigHistory.objects.filter(package_id=form.id).delete()
            Package.objects.filter(pk=form.id).delete()
        return json_response(error=error)


# 新增任务管理API
class TaskView(View):
    @auth('config.task.view')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False)
        ).parse(request.GET)
        if error is None:
            if form.id:
                task = ConfigTask.objects.get(pk=form.id)
                return json_response(task)
            tasks = ConfigTask.objects.all()
            return json_response(tasks)
        return json_response(error=error)

    @auth('config.task.add|config.task.edit')
    def post(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False),
            Argument('name', help='请输入任务名称'),
            Argument('key', help='请输入唯一标识符'),
            Argument('desc', required=False),
            Argument('rel_packages', type=list, required=False)
        ).parse(request.body)
        if error is None:
            if not re.fullmatch(r'\w+', form.key, re.ASCII):
                return json_response(error='标识符必须为字母、数字和下划线的组合')

            task = ConfigTask.objects.filter(key=form.key).first()
            if task and task.id != form.id:
                return json_response(error=f'唯一标识符 {form.key} 已存在，请更改后重试')
            
            # 处理关联配套
            form.rel_packages = json.dumps(form.rel_packages or [])
            
            if form.id:
                ConfigTask.objects.filter(pk=form.id).update(**form)
            else:
                task = ConfigTask.objects.create(created_by=request.user, **form)
                task.sort_id = task.id
                task.save()
        return json_response(error=error)

    @auth('config.task.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            # 检查是否有版本配套关联
            if TaskVersionPackage.objects.filter(task_id=form.id).exists():
                return json_response(error='该任务已配置版本配套，请先删除相关配套后再尝试删除')
            ConfigTask.objects.filter(pk=form.id).delete()
        return json_response(error=error)


# 新增任务版本配套管理API
class TaskVersionPackageView(View):
    @auth('config.task.view_config')
    def get(self, request):
        form, error = JsonParser(
            Argument('task_id', type=int, help='未指定任务'),
            Argument('package_id', type=int, required=False, help='配套ID'),
        ).parse(request.GET)
        if error is None:
            query = {'task_id': form.task_id}
            if form.package_id:
                query['package_id'] = form.package_id
            
            data = []
            for item in TaskVersionPackage.objects.filter(**query).select_related('package', 'created_by', 'updated_by'):
                tmp = item.to_dict()
                tmp['package_name'] = item.package.name
                tmp['package_key'] = item.package.key
                tmp['created_by_user'] = item.created_by.nickname
                if item.updated_by:
                    tmp['updated_by_user'] = item.updated_by.nickname
                data.append(tmp)
            return json_response(data)
        return json_response(error=error)

    @auth('config.task.edit_config')
    def post(self, request):
        form, error = JsonParser(
            Argument('task_id', type=int, help='缺少任务ID'),
            Argument('package_id', type=int, help='请选择配套'),
            Argument('version', help='请输入版本号'),
            Argument('file_path', help='请选择文件'),
            Argument('desc', required=False)
        ).parse(request.body)
        if error is None:
            # 检查是否已存在相同的版本配套
            existing = TaskVersionPackage.objects.filter(
                task_id=form.task_id,
                package_id=form.package_id,
                version=form.version
            ).first()
            if existing:
                return json_response(error='该任务在此配套下已存在相同版本号的配套')
            
            TaskVersionPackage.objects.create(
                created_by=request.user,
                **form
            )
        return json_response(error=error)

    @auth('config.task.edit_config')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='缺少必要参数'),
            Argument('file_path', help='请选择文件'),
            Argument('desc', required=False),
            Argument('is_active', type=bool, required=False)
        ).parse(request.body)
        if error is None:
            package = TaskVersionPackage.objects.filter(pk=form.id).first()
            if not package:
                return json_response(error='未找到指定版本配套')
            
            form.updated_at = human_datetime()
            form.updated_by = request.user
            TaskVersionPackage.objects.filter(pk=form.id).update(**form)
        return json_response(error=error)

    @auth('config.task.edit_config')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            TaskVersionPackage.objects.filter(pk=form.id).delete()
        return json_response(error=error)


class ServiceView(View):
    @auth('config.src.view')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False)
        ).parse(request.GET)
        if error is None:
            if form.id:
                service = Service.objects.get(pk=form.id)
                return json_response(service)
            services = Service.objects.all()
            return json_response(services)
        return json_response(error=error)

    @auth('config.src.add|config.src.edit')
    def post(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False),
            Argument('name', help='请输入服务名称'),
            Argument('key', help='请输入唯一标识符'),
            Argument('desc', required=False)
        ).parse(request.body)
        if error is None:
            if not re.fullmatch(r'\w+', form.key, re.ASCII):
                return json_response(error='标识符必须为字母、数字和下划线的组合')

            service = Service.objects.filter(key=form.key).first()
            if service and service.id != form.id:
                return json_response(error='该标识符已存在，请更改后重试')
            app = App.objects.filter(key=form.key).first()
            if app:
                return json_response(error=f'该标识符已被应用 {app.name} 使用，请更改后重试')
            if form.id:
                Service.objects.filter(pk=form.id).update(**form)
            else:
                Service.objects.create(created_by=request.user, **form)
        return json_response(error=error)

    @auth('config.src.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            rel_apps = []
            for app in App.objects.filter(rel_services__isnull=False):
                rel_services = json.loads(app.rel_services)
                if form.id in rel_services:
                    rel_apps.append(app.name)
            if rel_apps:
                return json_response(
                    error=f'该服务在配置中心已被 "{", ".join(rel_apps)}" 依赖，请解除依赖关系后再尝试删除。')
            # auto delete configs
            Config.objects.filter(type='src', o_id=form.id).delete()
            ConfigHistory.objects.filter(type='src', o_id=form.id).delete()
            Service.objects.filter(pk=form.id).delete()
        return json_response(error=error)


class ConfigView(View):
    @auth('config.src.view_config|config.app.view_config|config.task.view_config')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='未指定操作对象'),
            Argument('type', filter=lambda x: x in dict(Config.TYPES), help='缺少必要参数'),
            Argument('env_id', type=int, required=False, help='环境ID'),
            Argument('package_id', type=int, required=False, help='配套ID'),
        ).parse(request.GET)
        if error is None:
            form.o_id, data = form.pop('id'), []
            query_filter = {'type': form.type, 'o_id': form.o_id}
            
            # 根据类型使用不同的关联字段
            if form.type == 'task' and form.package_id:
                query_filter['package_id'] = form.package_id
            elif form.env_id:
                query_filter['env_id'] = form.env_id
                
            try:
                query_set = Config.objects.filter(**query_filter).annotate(update_user=F('updated_by__nickname'))
            except Exception as e:
                # 如果package_id字段不存在，移除相关过滤条件
                if 'package_id' in str(e):
                    query_filter.pop('package_id', None)
                    query_set = Config.objects.filter(**query_filter).annotate(update_user=F('updated_by__nickname'))
                else:
                    raise e
            
            for item in query_set:
                tmp = item.to_dict()
                tmp['update_user'] = item.update_user
                if hasattr(item, 'env') and item.env:
                    tmp['env_name'] = item.env.name
                if hasattr(item, 'package') and item.package:
                    tmp['package_name'] = item.package.name
                data.append(tmp)
            return json_response(data)
        return json_response(error=error)

    @auth('config.src.edit_config|config.app.edit_config|config.task.edit_config')
    def post(self, request):
        form, error = JsonParser(
            Argument('o_id', type=int, help='缺少必要参数'),
            Argument('type', filter=lambda x: x in dict(Config.TYPES), help='缺少必要参数'),
            Argument('envs', type=list, required=False, help='环境列表'),
            Argument('packages', type=list, required=False, help='配套列表'),
            Argument('key', help='请输入版本号'),
            Argument('is_public', type=bool, default=False),
            Argument('value', type=str, default='', help='请选择文件'),
            Argument('desc', required=False)
        ).parse(request.body)
        if error is None:
            form.value = form.value.strip()
            form.updated_at = human_datetime()
            form.updated_by = request.user
            
            # 移除envs和packages参数，避免传递给Config.objects.create
            envs = form.pop('envs', [])
            packages = form.pop('packages', [])
            
            # 根据类型处理不同的关联
            if form.type == 'task':
                if not packages:
                    return json_response(error='请选择配套')
                for package_id in packages:
                    cf = Config.objects.filter(o_id=form.o_id, type=form.type, package_id=package_id, key=form.key).first()
                    if cf:
                        package = Package.objects.get(pk=package_id)
                        return json_response(error=f'{package.name} 中已存在该版本号')
                    Config.objects.create(package_id=package_id, **form)
                    ConfigHistory.objects.create(action='1', package_id=package_id, **form)
            else:
                if not envs:
                    return json_response(error='请选择环境')
                for env_id in envs:
                    cf = Config.objects.filter(o_id=form.o_id, type=form.type, env_id=env_id, key=form.key).first()
                    if cf:
                        env = Environment.objects.get(pk=env_id)
                        return json_response(error=f'{env.name} 中已存在该Key')
                    Config.objects.create(env_id=env_id, **form)
                    ConfigHistory.objects.create(action='1', env_id=env_id, **form)
        return json_response(error=error)

    @auth('config.src.edit_config|config.app.edit_config|config.task.edit_config')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='缺少必要参数'),
            Argument('value', type=str, default=''),
            Argument('is_public', type=bool, help='缺少必要参数'),
            Argument('desc', required=False)
        ).parse(request.body)
        if error is None:
            form.value = form.value.strip()
            config = Config.objects.filter(pk=form.id).first()
            if not config:
                return json_response(error='未找到指定对象')
            config.desc = form.desc
            config.is_public = form.is_public
            if config.value != form.value:
                old_value = config.value
                config.value = form.value
                config.updated_at = human_datetime()
                config.updated_by = request.user
                config.save()
                ConfigHistory.objects.create(
                    action='2',
                    old_value=old_value,
                    **config.to_dict(excludes=('id',)))
        return json_response(error=error)

    @auth('config.src.edit_config|config.app.edit_config|config.task.edit_config')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            config = Config.objects.filter(pk=form.id).first()
            if not config:
                return json_response(error='未找到指定对象')
            ConfigHistory.objects.create(
                action='3',
                old_value=config.value,
                value='',
                updated_at=human_datetime(),
                updated_by=request.user,
                **config.to_dict(excludes=('id', 'value', 'updated_at', 'updated_by_id'))
            )
            config.delete()
        return json_response(error=error)


class HistoryView(View):
    @auth('config.src.view_config|config.app.view_config|config.task.view_config')
    def post(self, request):
        form, error = JsonParser(
            Argument('o_id', type=int, help='缺少必要参数'),
            Argument('type', filter=lambda x: x in dict(Config.TYPES), help='缺少必要参数'),
            Argument('key', help='缺少必要参数'),
            Argument('env_id', type=int, required=False),
            Argument('package_id', type=int, required=False)
        ).parse(request.body)
        if error is None:
            query = {'o_id': form.o_id, 'type': form.type, 'key': form.key}
            if form.env_id:
                query['env_id'] = form.env_id
            if form.package_id:
                query['package_id'] = form.package_id
                
            data = []
            for item in ConfigHistory.objects.filter(**query).annotate(
                    update_user=F('updated_by__nickname')).order_by('-id'):
                tmp = item.to_dict()
                tmp['update_user'] = item.update_user
                data.append(tmp)
            return json_response(data)
        return json_response(error=error)


@auth('config.src.view_config|config.app.view_config|config.task.view_config')
def post_diff(request):
    form, error = JsonParser(
        Argument('o_id', type=int, help='缺少必要参数'),
        Argument('type', filter=lambda x: x in dict(Config.TYPES), help='缺少必要参数'),
        Argument('envs', type=list, required=False),
        Argument('packages', type=list, required=False),
    ).parse(request.body)
    if error is None:
        data = {}
        if form.type == 'task' and form.packages:
            form.package_id__in = form.pop('packages')
            for item in Config.objects.filter(**form).order_by('key'):
                if item.key in data:
                    data[item.key][item.package_id] = item.value
                else:
                    data[item.key] = {'key': item.key, item.package_id: item.value}
        elif form.envs:
            form.env_id__in = form.pop('envs')
            for item in Config.objects.filter(**form).order_by('key'):
                if item.key in data:
                    data[item.key][item.env_id] = item.value
                else:
                    data[item.key] = {'key': item.key, item.env_id: item.value}
        return json_response(list(data.values()))
    return json_response(error=error)


@auth('config.src.edit_config|config.app.edit_config|config.task.edit_config')
def parse_json(request):
    form, error = JsonParser(
        Argument('o_id', type=int, help='缺少必要参数'),
        Argument('type', filter=lambda x: x in dict(Config.TYPES), help='缺少必要参数'),
        Argument('env_id', type=int, required=False),
        Argument('package_id', type=int, required=False),
        Argument('data', type=dict, help='缺少必要参数')
    ).parse(request.body)
    if error is None:
        data = form.pop('data')
        _parse(request, form, data)
    return json_response(error=error)


@auth('config.src.edit_config|config.app.edit_config|config.task.edit_config')
def parse_text(request):
    form, error = JsonParser(
        Argument('o_id', type=int, help='缺少必要参数'),
        Argument('type', filter=lambda x: x in dict(Config.TYPES), help='缺少必要参数'),
        Argument('env_id', type=int, required=False),
        Argument('package_id', type=int, required=False),
        Argument('data', help='缺少必要参数')
    ).parse(request.body)
    if error is None:
        data = {}
        for line in form.data.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                fields = line.split('=', 1)
                if len(fields) == 2:
                    data[fields[0].strip()] = fields[1].strip()
        _parse(request, form, data)
    return json_response(error=error)


def _parse(request, query, data):
    if query.get('env_id'):
        for item in Config.objects.filter(o_id=query['o_id'], type=query['type'], env_id=query['env_id']):
            if item.key in data:
                value = _filter_value(data.pop(item.key))
                if item.value != value:
                    old_value = item.value
                    item.value = value
                    item.updated_at = human_datetime()
                    item.updated_by = request.user
                    item.save()
                    ConfigHistory.objects.create(
                        action='2',
                        old_value=old_value,
                        **item.to_dict(excludes=('id',)))
            else:
                ConfigHistory.objects.create(
                    action='3',
                    old_value=item.value,
                    value='',
                    updated_at=human_datetime(),
                    updated_by=request.user,
                    **item.to_dict(excludes=('id', 'value', 'updated_at', 'updated_by_id'))
                )
                item.delete()
        for key, value in data.items():
            query['key'] = key
            query['is_public'] = False
            query['value'] = _filter_value(value)
            query['updated_at'] = human_datetime()
            query['updated_by'] = request.user
            Config.objects.create(**query)
            ConfigHistory.objects.create(action='1', **query)
    elif query.get('package_id'):
        for item in Config.objects.filter(o_id=query['o_id'], type=query['type'], package_id=query['package_id']):
            if item.key in data:
                value = _filter_value(data.pop(item.key))
                if item.value != value:
                    old_value = item.value
                    item.value = value
                    item.updated_at = human_datetime()
                    item.updated_by = request.user
                    item.save()
                    ConfigHistory.objects.create(
                        action='2',
                        old_value=old_value,
                        **item.to_dict(excludes=('id',)))
            else:
                ConfigHistory.objects.create(
                    action='3',
                    old_value=item.value,
                    value='',
                    updated_at=human_datetime(),
                    updated_by=request.user,
                    **item.to_dict(excludes=('id', 'value', 'updated_at', 'updated_by_id'))
                )
                item.delete()
        for key, value in data.items():
            query['key'] = key
            query['is_public'] = False
            query['value'] = _filter_value(value)
            query['updated_at'] = human_datetime()
            query['updated_by'] = request.user
            Config.objects.create(**query)
            ConfigHistory.objects.create(action='1', **query)


def _filter_value(value):
    if isinstance(value, str) and len(value) > 1000:
        return value[:1000]
    return value
