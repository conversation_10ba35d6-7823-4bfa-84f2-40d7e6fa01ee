2025-06-28 11:36:32 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 11:36:32 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 11:36:32 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 11:36:32 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 11:36:34 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 11:36:34 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 11:36:34 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 11:36:34 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 11:36:34 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 11:36:53 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 11:36:53 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 11:36:53 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 12:27:39 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py changed, reloading.
2025-06-28 12:27:42 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:27:42 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:27:42 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:27:42 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:27:42 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 12:27:53 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\settings.py changed, reloading.
2025-06-28 12:27:55 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:27:55 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:27:55 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:27:55 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:27:55 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 12:30:37 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:30:37 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:30:38 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:30:38 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:30:55 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:30:55 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:30:55 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:30:55 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:31:06 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:31:06 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:31:06 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:31:06 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:34:33 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:34:33 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:34:33 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:34:33 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:34:56 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\models.py changed, reloading.
2025-06-28 12:34:58 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:34:58 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:34:58 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:34:58 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:34:58 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 12:34:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 12:34:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 12:34:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 12:35:37 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 12:35:38 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:35:38 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:35:38 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:35:38 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:35:38 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 12:36:20 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:36:20 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:36:20 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:36:20 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:36:28 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 12:36:30 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:36:30 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:36:30 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:36:30 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:36:30 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 12:36:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 12:36:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 12:36:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 12:36:37 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:36:37 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:36:37 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:36:37 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:46:19 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:46:19 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:46:20 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:46:20 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:51:26 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\models.py changed, reloading.
2025-06-28 12:51:39 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:51:39 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:51:39 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:51:39 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:51:39 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 12:51:41 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 12:51:41 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 12:51:41 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 12:51:58 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:51:58 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:51:58 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:51:58 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 12:59:31 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 12:59:31 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 12:59:31 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 12:59:31 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 13:00:31 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP POST /account/login/ 200 [0.13, 127.0.0.1:63992]
2025-06-28 13:00:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:63996]
2025-06-28 13:00:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /home/<USER>/ 200 [0.04, 127.0.0.1:63998]
2025-06-28 13:00:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /home/<USER>/ 200 [0.05, 127.0.0.1:63999]
2025-06-28 13:00:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64003]
2025-06-28 13:00:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64003]
2025-06-28 13:00:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:64003]
2025-06-28 13:00:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:64070]
2025-06-28 13:02:18 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:64246]
2025-06-28 13:02:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:64289]
2025-06-28 13:02:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:64310]
2025-06-28 13:02:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64316]
2025-06-28 13:02:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64316]
2025-06-28 13:02:41 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/release-plans/
2025-06-28 13:02:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/release-plans/ 500 [0.07, 127.0.0.1:64313]
2025-06-28 13:02:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.08, 127.0.0.1:64312]
2025-06-28 13:02:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.09, 127.0.0.1:64314]
2025-06-28 13:02:43 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:02:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.15, 127.0.0.1:64311]
2025-06-28 13:02:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:64316]
2025-06-28 13:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:64358]
2025-06-28 13:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.05, 127.0.0.1:64362]
2025-06-28 13:02:55 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/release-plans/
2025-06-28 13:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/release-plans/ 500 [0.07, 127.0.0.1:64361]
2025-06-28 13:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.08, 127.0.0.1:64360]
2025-06-28 13:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64364]
2025-06-28 13:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64364]
2025-06-28 13:02:56 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:02:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.07, 127.0.0.1:64359]
2025-06-28 13:04:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:64364]
2025-06-28 13:04:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:64458]
2025-06-28 13:04:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64465]
2025-06-28 13:04:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64465]
2025-06-28 13:04:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.02, 127.0.0.1:64471]
2025-06-28 13:04:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.03, 127.0.0.1:64473]
2025-06-28 13:04:03 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/release-plans/
2025-06-28 13:04:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/release-plans/ 500 [0.05, 127.0.0.1:64472]
2025-06-28 13:04:03 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:04:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.07, 127.0.0.1:64461]
2025-06-28 13:04:34 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:04:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:64542]
2025-06-28 13:04:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:64465]
2025-06-28 13:04:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:64569]
2025-06-28 13:04:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.03, 127.0.0.1:64571]
2025-06-28 13:04:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64576]
2025-06-28 13:04:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64576]
2025-06-28 13:04:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.04, 127.0.0.1:64573]
2025-06-28 13:04:43 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/release-plans/
2025-06-28 13:04:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/release-plans/ 500 [0.06, 127.0.0.1:64572]
2025-06-28 13:04:44 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:04:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:64570]
2025-06-28 13:05:14 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:05:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:64653]
2025-06-28 13:05:44 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:05:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:64686]
2025-06-28 13:06:14 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:06:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.06, 127.0.0.1:64707]
2025-06-28 13:06:44 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:06:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:64748]
2025-06-28 13:07:14 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:07:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:64771]
2025-06-28 13:07:44 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:07:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:64804]
2025-06-28 13:08:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:08:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:64842]
2025-06-28 13:09:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:09:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.06, 127.0.0.1:64930]
2025-06-28 13:10:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:10:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:65024]
2025-06-28 13:11:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:11:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:65140]
2025-06-28 13:12:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:12:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:65187]
2025-06-28 13:13:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:13:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:65232]
2025-06-28 13:14:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:14:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:65275]
2025-06-28 13:15:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:15:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:65332]
2025-06-28 13:16:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:16:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:65395]
2025-06-28 13:17:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:17:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:65432]
2025-06-28 13:18:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:18:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:65471]
2025-06-28 13:19:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:19:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:65519]
2025-06-28 13:20:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:20:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52305]
2025-06-28 13:21:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:21:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52383]
2025-06-28 13:22:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:22:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52421]
2025-06-28 13:23:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:23:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:52464]
2025-06-28 13:24:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:24:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52522]
2025-06-28 13:25:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:25:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52579]
2025-06-28 13:26:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:26:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52694]
2025-06-28 13:27:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:27:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52724]
2025-06-28 13:28:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:28:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.06, 127.0.0.1:52793]
2025-06-28 13:29:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:29:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52818]
2025-06-28 13:30:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:30:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:52891]
2025-06-28 13:31:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:31:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:52963]
2025-06-28 13:32:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:32:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:53036]
2025-06-28 13:33:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:33:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:53100]
2025-06-28 13:34:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:34:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:53144]
2025-06-28 13:35:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:35:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:53188]
2025-06-28 13:36:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:36:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:53233]
2025-06-28 13:37:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:37:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:53254]
2025-06-28 13:38:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:38:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:53307]
2025-06-28 13:39:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:39:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:53341]
2025-06-28 13:40:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:40:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:53576]
2025-06-28 13:41:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:41:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.16, 127.0.0.1:53725]
2025-06-28 13:42:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:42:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:53801]
2025-06-28 13:43:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:64576]
2025-06-28 13:43:22 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:43:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.06, 127.0.0.1:53846]
2025-06-28 13:43:23 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/release-plans/
2025-06-28 13:43:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/release-plans/ 500 [0.08, 127.0.0.1:53860]
2025-06-28 13:43:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.09, 127.0.0.1:53859]
2025-06-28 13:43:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.11, 127.0.0.1:53862]
2025-06-28 13:43:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.12, 127.0.0.1:53861]
2025-06-28 13:43:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:53868]
2025-06-28 13:43:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:53868]
2025-06-28 13:43:24 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:43:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.08, 127.0.0.1:53858]
2025-06-28 13:43:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:53868]
2025-06-28 13:43:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:53902]
2025-06-28 13:43:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.06, 127.0.0.1:53904]
2025-06-28 13:43:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.08, 127.0.0.1:53906]
2025-06-28 13:43:28 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/release-plans/
2025-06-28 13:43:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/release-plans/ 500 [0.09, 127.0.0.1:53905]
2025-06-28 13:43:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:53910]
2025-06-28 13:43:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:53910]
2025-06-28 13:43:29 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:43:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.08, 127.0.0.1:53903]
2025-06-28 13:43:59 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:43:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:53976]
2025-06-28 13:44:29 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:44:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54000]
2025-06-28 13:45:00 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:45:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54053]
2025-06-28 13:45:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 13:45:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 13:45:13 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 13:45:13 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 13:45:27 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 13:45:27 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 13:45:27 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 13:45:27 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 13:45:29 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:45:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54144]
2025-06-28 13:45:37 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 13:45:37 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 13:45:37 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 13:45:37 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 13:46:00 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:46:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:54192]
2025-06-28 13:46:30 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:46:30 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54240]
2025-06-28 13:47:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:47:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54336]
2025-06-28 13:48:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:48:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:54401]
2025-06-28 13:49:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:49:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54457]
2025-06-28 13:50:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:50:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:54568]
2025-06-28 13:51:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54659]
2025-06-28 13:52:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:52:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54736]
2025-06-28 13:53:26 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:53:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:54798]
2025-06-28 13:53:30 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:53:30 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54806]
2025-06-28 13:54:00 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:54:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:54887]
2025-06-28 13:54:30 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:54:30 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:54925]
2025-06-28 13:55:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:55:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:55056]
2025-06-28 13:56:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:56:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:55161]
2025-06-28 13:57:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:57:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:55262]
2025-06-28 13:58:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:58:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:55322]
2025-06-28 13:59:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 13:59:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.03, 127.0.0.1:55403]
2025-06-28 14:00:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:00:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:55490]
2025-06-28 14:01:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:01:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:55605]
2025-06-28 14:02:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:02:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:55711]
2025-06-28 14:03:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:03:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:55785]
2025-06-28 14:04:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:04:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:55844]
2025-06-28 14:05:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:05:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.18, 127.0.0.1:55937]
2025-06-28 14:06:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:06:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:55997]
2025-06-28 14:07:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:07:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:56100]
2025-06-28 14:08:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:08:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:56159]
2025-06-28 14:09:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:09:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:56248]
2025-06-28 14:10:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:10:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:56355]
2025-06-28 14:11:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:11:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:56513]
2025-06-28 14:12:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:12:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:56591]
2025-06-28 14:13:31 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:13:31 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:56668]
2025-06-28 14:13:40 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:13:40 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:13:40 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:13:40 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:14:00 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:14:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:56706]
2025-06-28 14:14:30 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:14:30 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.05, 127.0.0.1:56731]
2025-06-28 14:15:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:15:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:56810]
2025-06-28 14:16:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:16:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:56853]
2025-06-28 14:17:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:17:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:56922]
2025-06-28 14:18:48 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:18:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:56954]
2025-06-28 14:19:27 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\models.py changed, reloading.
2025-06-28 14:19:27 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:19:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.04, 127.0.0.1:57004]
2025-06-28 14:19:30 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:19:30 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:19:30 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:19:30 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:19:30 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:19:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:19:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 14:19:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 14:19:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57029]
2025-06-28 14:19:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57029]
2025-06-28 14:20:00 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - ERROR - Internal Server Error: /model-storage/server-metrics/
2025-06-28 14:20:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:154" - log_action - ERROR - HTTP GET /model-storage/server-metrics/ 500 [1.06, 127.0.0.1:57060]
2025-06-28 14:20:11 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:20:13 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:20:13 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:20:13 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:20:13 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:20:13 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:20:15 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:20:15 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 14:20:15 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 14:20:18 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57094]
2025-06-28 14:20:18 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57094]
2025-06-28 14:20:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57108]
2025-06-28 14:21:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57153]
2025-06-28 14:21:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57094]
2025-06-28 14:21:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.05, 127.0.0.1:57212]
2025-06-28 14:21:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:57215]
2025-06-28 14:21:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.07, 127.0.0.1:57214]
2025-06-28 14:21:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57220]
2025-06-28 14:21:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57220]
2025-06-28 14:21:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:57224]
2025-06-28 14:21:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57213]
2025-06-28 14:21:37 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:21:37 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:21:37 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:21:37 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:21:39 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:21:39 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:21:39 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:21:39 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:21:39 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:21:42 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:21:42 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:21:42 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 14:21:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57279]
2025-06-28 14:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57297]
2025-06-28 14:22:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57315]
2025-06-28 14:23:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57361]
2025-06-28 14:23:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57220]
2025-06-28 14:23:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.04, 127.0.0.1:57387]
2025-06-28 14:23:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.05, 127.0.0.1:57384]
2025-06-28 14:23:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.07, 127.0.0.1:57386]
2025-06-28 14:23:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57394]
2025-06-28 14:23:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57394]
2025-06-28 14:23:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:57397]
2025-06-28 14:23:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57385]
2025-06-28 14:23:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57394]
2025-06-28 14:23:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57430]
2025-06-28 14:23:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57430]
2025-06-28 14:23:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:57429]
2025-06-28 14:23:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.03, 127.0.0.1:57439]
2025-06-28 14:23:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.05, 127.0.0.1:57440]
2025-06-28 14:23:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.08, 127.0.0.1:57438]
2025-06-28 14:23:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:57437]
2025-06-28 14:24:15 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57529]
2025-06-28 14:24:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57562]
2025-06-28 14:25:15 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57617]
2025-06-28 14:25:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57655]
2025-06-28 14:26:15 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57742]
2025-06-28 14:26:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57785]
2025-06-28 14:27:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57860]
2025-06-28 14:28:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57925]
2025-06-28 14:29:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57991]
2025-06-28 14:30:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58080]
2025-06-28 14:31:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58171]
2025-06-28 14:32:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58230]
2025-06-28 14:33:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58320]
2025-06-28 14:34:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58429]
2025-06-28 14:35:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58567]
2025-06-28 14:35:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57430]
2025-06-28 14:35:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.05, 127.0.0.1:58589]
2025-06-28 14:35:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.09, 127.0.0.1:58591]
2025-06-28 14:35:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.11, 127.0.0.1:58592]
2025-06-28 14:35:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.12, 127.0.0.1:58593]
2025-06-28 14:35:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58599]
2025-06-28 14:35:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58599]
2025-06-28 14:35:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:58590]
2025-06-28 14:35:58 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Unauthorized: /api/model-storage/server-metrics/
2025-06-28 14:35:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:151" - log_action - WARNING - HTTP GET /api/model-storage/server-metrics/ 401 [0.01, 127.0.0.1:58629]
2025-06-28 14:36:19 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:36:19 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:36:19 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:36:19 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:36:21 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:36:21 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:36:21 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:36:21 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:36:21 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:36:24 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:36:24 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:36:24 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:36:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58714]
2025-06-28 14:36:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58752]
2025-06-28 14:37:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58800]
2025-06-28 14:37:25 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:37:25 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:37:25 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:37:27 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:37:27 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:37:27 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:37:27 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:37:28 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:37:28 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:37:28 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:37:28 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:37:28 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:37:28 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:37:28 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:37:28 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:37:28 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:37:28 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:37:28 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:37:33 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:37:33 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:37:33 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 14:37:33 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:37:33 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 14:37:33 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 14:37:33 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58851]
2025-06-28 14:37:33 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58851]
2025-06-28 14:37:38 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:37:38 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:37:38 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:37:46 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:58851]
2025-06-28 14:37:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.05, 127.0.0.1:58884]
2025-06-28 14:37:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.06, 127.0.0.1:58887]
2025-06-28 14:37:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.07, 127.0.0.1:58888]
2025-06-28 14:37:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58891]
2025-06-28 14:37:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58891]
2025-06-28 14:37:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:58895]
2025-06-28 14:37:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:58886]
2025-06-28 14:37:57 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:37:58 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:37:58 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:38:00 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:38:00 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:38:00 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:38:00 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:38:00 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:38:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:38:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:38:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:38:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:38:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:38:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:38:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:38:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:38:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:38:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:38:06 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:38:06 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:38:06 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 14:38:06 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:38:06 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 14:38:06 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 14:38:08 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58951]
2025-06-28 14:38:08 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58951]
2025-06-28 14:38:11 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:38:11 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:38:11 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:38:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:58964]
2025-06-28 14:38:21 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:38:21 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:38:21 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:38:21 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:38:22 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:38:22 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:38:23 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:38:23 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:38:23 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:38:25 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:38:25 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:38:25 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59018]
2025-06-28 14:39:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59051]
2025-06-28 14:39:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59095]
2025-06-28 14:40:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59196]
2025-06-28 14:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59237]
2025-06-28 14:41:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59389]
2025-06-28 14:42:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59493]
2025-06-28 14:43:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59553]
2025-06-28 14:44:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59605]
2025-06-28 14:45:12 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Unauthorized: /api/model-storage/server-metrics/
2025-06-28 14:45:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:151" - log_action - WARNING - HTTP GET /api/model-storage/server-metrics/ 401 [0.00, 127.0.0.1:59657]
2025-06-28 14:45:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59712]
2025-06-28 14:46:15 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\models.py changed, reloading.
2025-06-28 14:46:15 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\models.py changed, reloading.
2025-06-28 14:46:16 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\models.py changed, reloading.
2025-06-28 14:46:16 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\models.py changed, reloading.
2025-06-28 14:46:18 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:46:18 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:46:18 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:46:18 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:46:18 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:46:18 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:46:18 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:46:18 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:46:18 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:46:18 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:46:18 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:46:18 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:46:18 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:46:18 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:46:18 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:46:18 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:46:19 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:46:19 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:46:19 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:46:24 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:46:24 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:46:24 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 14:46:25 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:46:25 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 14:46:25 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 14:46:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:59797]
2025-06-28 14:46:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:59797]
2025-06-28 14:46:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:46:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:46:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:46:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:46:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:46:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59846]
2025-06-28 14:47:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59865]
2025-06-28 14:47:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59895]
2025-06-28 14:47:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:59897]
2025-06-28 14:47:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/check-missing/ 200 [0.01, 127.0.0.1:59901]
2025-06-28 14:47:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.01, 127.0.0.1:59906]
2025-06-28 14:48:04 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/check-missing/ 200 [0.01, 127.0.0.1:59913]
2025-06-28 14:48:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.01, 127.0.0.1:59917]
2025-06-28 14:48:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59934]
2025-06-28 14:48:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59960]
2025-06-28 14:49:18 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59987]
2025-06-28 14:49:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:59797]
2025-06-28 14:49:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:60010]
2025-06-28 14:49:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.04, 127.0.0.1:60013]
2025-06-28 14:49:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.06, 127.0.0.1:60014]
2025-06-28 14:49:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:60020]
2025-06-28 14:49:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:60020]
2025-06-28 14:49:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.01, 127.0.0.1:60024]
2025-06-28 14:49:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:60012]
2025-06-28 14:50:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60061]
2025-06-28 14:50:40 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60134]
2025-06-28 14:51:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60178]
2025-06-28 14:51:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60207]
2025-06-28 14:51:46 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:60020]
2025-06-28 14:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.04, 127.0.0.1:60248]
2025-06-28 14:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:60250]
2025-06-28 14:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:60250]
2025-06-28 14:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.06, 127.0.0.1:60247]
2025-06-28 14:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.02, 127.0.0.1:60257]
2025-06-28 14:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:60256]
2025-06-28 14:51:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60246]
2025-06-28 14:52:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60311]
2025-06-28 14:52:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:60250]
2025-06-28 14:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:60377]
2025-06-28 14:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:60381]
2025-06-28 14:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:60381]
2025-06-28 14:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:60388]
2025-06-28 14:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.03, 127.0.0.1:60386]
2025-06-28 14:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.05, 127.0.0.1:60387]
2025-06-28 14:52:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:60376]
2025-06-28 14:53:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60419]
2025-06-28 14:53:48 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:53:49 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:53:49 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:53:49 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 14:53:52 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:53:52 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:53:52 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:53:52 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:53:52 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:53:52 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:53:52 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:53:52 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:53:52 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:53:52 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:53:52 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:53:52 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:53:52 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:53:52 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:53:52 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:53:52 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:53:52 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:53:52 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:53:52 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:53:52 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 14:53:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:53:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:53:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 14:53:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:60476]
2025-06-28 14:53:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:60476]
2025-06-28 14:53:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:53:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:53:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:54:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:54:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 14:54:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 14:54:04 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 14:54:04 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 14:54:04 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 14:54:15 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:54:15 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:54:16 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:54:16 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:54:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:60527]
2025-06-28 14:54:25 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 14:54:25 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 14:54:26 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 14:54:26 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 14:54:49 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Unauthorized: /api/model-storage/server-metrics/
2025-06-28 14:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:151" - log_action - WARNING - HTTP GET /api/model-storage/server-metrics/ 401 [0.00, 127.0.0.1:60576]
2025-06-28 14:54:51 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60577]
2025-06-28 14:55:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:60605]
2025-06-28 14:55:51 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:60673]
2025-06-28 14:56:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:60843]
2025-06-28 14:57:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:60476]
2025-06-28 14:57:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:60885]
2025-06-28 14:57:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:60885]
2025-06-28 14:57:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.02, 127.0.0.1:60884]
2025-06-28 14:57:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.08, 127.0.0.1:60882]
2025-06-28 14:57:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.04, 127.0.0.1:60891]
2025-06-28 14:57:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.11, 127.0.0.1:60886]
2025-06-28 14:57:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:60883]
2025-06-28 14:57:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60935]
2025-06-28 14:58:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60962]
2025-06-28 14:58:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60992]
2025-06-28 14:59:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61009]
2025-06-28 14:59:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61041]
2025-06-28 15:00:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61071]
2025-06-28 15:01:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61170]
2025-06-28 15:02:07 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61216]
2025-06-28 15:02:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:60885]
2025-06-28 15:02:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:61238]
2025-06-28 15:02:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree/ 200 [0.07, 127.0.0.1:61237]
2025-06-28 15:02:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.09, 127.0.0.1:61236]
2025-06-28 15:02:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61244]
2025-06-28 15:02:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61244]
2025-06-28 15:02:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:61248]
2025-06-28 15:02:15 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:61234]
2025-06-28 15:02:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:61311]
2025-06-28 15:03:15 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61324]
2025-06-28 15:03:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61374]
2025-06-28 15:04:15 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61392]
2025-06-28 15:04:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61411]
2025-06-28 15:05:15 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:61455]
2025-06-28 15:05:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.05, 127.0.0.1:61483]
2025-06-28 15:05:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.06, 127.0.0.1:61482]
2025-06-28 15:05:38 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-06-28 15:05:38 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) failed.
2025-06-28 15:05:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP POST /host/ 200 [0.20, 127.0.0.1:61519]
2025-06-28 15:05:39 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-06-28 15:05:39 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (password) successful!
2025-06-28 15:05:40 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-06-28 15:05:41 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
2025-06-28 15:05:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP POST /host/ 200 [1.27, 127.0.0.1:61532]
2025-06-28 15:05:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.02, 127.0.0.1:61536]
2025-06-28 15:05:41 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-06-28 15:05:41 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
2025-06-28 15:05:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP PUT /host/ 200 [0.64, 127.0.0.1:61539]
2025-06-28 15:05:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.01, 127.0.0.1:61544]
2025-06-28 15:05:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.04, 127.0.0.1:61555]
2025-06-28 15:05:43 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:24" - get - INFO - 开始获取主机 ************* (ID: 4) 的GPU数据
2025-06-28 15:05:43 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Not Found: /host/4/
2025-06-28 15:05:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:149" - log_action - WARNING - HTTP GET /host/4/ 404 [0.14, 127.0.0.1:61553]
2025-06-28 15:05:43 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-06-28 15:05:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61562]
2025-06-28 15:05:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61562]
2025-06-28 15:05:43 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
2025-06-28 15:05:44 - paramiko.transport.sftp - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\sftp.py:158" - _log - INFO - [chan 1] Opened sftp connection (server version 3)
2025-06-28 15:05:44 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:37" - get - INFO - 检测到GPU类型: kunlunxin
2025-06-28 15:05:44 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:149" - _get_gpu_basic_info - INFO - 开始获取GPU基础信息，GPU类型: kunlunxin
2025-06-28 15:05:44 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:153" - _get_gpu_basic_info - INFO - 检测到昆仑芯XPU，获取GPU基础信息
2025-06-28 15:05:44 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:232" - _get_gpu_basic_info - INFO - 从xpu-smi -q解析出GPU数量: 8
2025-06-28 15:05:44 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:275" - _get_gpu_basic_info - INFO - GPU基础信息获取完成，GPU数量: 8
2025-06-28 15:05:44 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:292" - _get_gpu_basic_info - INFO - GPU基础信息获取完成，GPU数量: 8, 数据: {'gpus': [{'index': '0', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:23:00.0', 'serial_number': '02K15K6253V001S4', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '1940 MiB', 'utilization': '1 %', 'temperature': '37 C', 'power_draw': '87.00 W'}, {'index': '1', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:26:00.0', 'serial_number': '02K15K6253V002C5', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '100 MiB', 'utilization': '0 %', 'temperature': '38 C', 'power_draw': '89.00 W'}, {'index': '2', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:63:00.0', 'serial_number': '02K15K6253V002BB', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '100 MiB', 'utilization': '0 %', 'temperature': '39 C', 'power_draw': '90.00 W'}, {'index': '3', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:66:00.0', 'serial_number': '02K15K6253V001WJ', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '100 MiB', 'utilization': '0 %', 'temperature': '44 C', 'power_draw': '87.00 W'}, {'index': '4', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:A3:00.0', 'serial_number': '02K15K6253V002L0', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '100 MiB', 'utilization': '0 %', 'temperature': '39 C', 'power_draw': '88.00 W'}, {'index': '5', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:A4:00.0', 'serial_number': '02K15K6253V002LB', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '100 MiB', 'utilization': '0 %', 'temperature': '39 C', 'power_draw': '89.00 W'}, {'index': '6', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:E3:00.0', 'serial_number': '02K15K6253V002L4', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '100 MiB', 'utilization': '0 %', 'temperature': '38 C', 'power_draw': '89.00 W'}, {'index': '7', 'name': 'P800 OAM', 'manufacturer': 'KUNLUNXIN', 'status': 'normal', 'pci_bus_id': '00000000:E4:00.0', 'serial_number': '02K15K6253V001QD', 'part_number': 'B00100300110312', 'firmware_version': '********.1.48', 'device_id': '0x36881D22', 'subsystem_id': '0x00010001', 'memory_total': '98304 MiB', 'memory_used': '100 MiB', 'utilization': '0 %', 'temperature': '36 C', 'power_draw': '88.00 W'}], 'gpu_count': 8, 'total_memory': 786432, 'manufacturer_stats': {'KUNLUNXIN': 8}}
2025-06-28 15:05:45 - apps.host.gpu_dashboard - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\host\gpu_dashboard.py:42" - get - INFO - 成功收集GPU数据
2025-06-28 15:05:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/gpu-dashboard/4/ 200 [2.20, 127.0.0.1:61554]
2025-06-28 15:06:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.02, 127.0.0.1:61605]
2025-06-28 15:06:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.04, 127.0.0.1:61604]
2025-06-28 15:06:04 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/template/ 200 [0.02, 127.0.0.1:61612]
2025-06-28 15:06:04 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/?with_hosts=true 200 [0.02, 127.0.0.1:61618]
2025-06-28 15:06:04 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/test-plans/ 200 [0.03, 127.0.0.1:61619]
2025-06-28 15:06:04 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /file/tree/ 200 [0.13, 127.0.0.1:61615]
2025-06-28 15:06:04 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /setting/user/ 200 [0.02, 127.0.0.1:61624]
2025-06-28 15:06:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/template/ 200 [0.03, 127.0.0.1:61635]
2025-06-28 15:06:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /file/tree/ 200 [0.08, 127.0.0.1:61636]
2025-06-28 15:06:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/ssh/4/ [127.0.0.1:61640]
2025-06-28 15:06:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/ssh/4/ [127.0.0.1:61640]
2025-06-28 15:06:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/test-plans/ 200 [0.01, 127.0.0.1:61641]
2025-06-28 15:06:06 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-06-28 15:06:06 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
2025-06-28 15:06:08 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/ssh/4/ [127.0.0.1:61640]
2025-06-28 15:06:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /setting/user/ 200 [0.01, 127.0.0.1:61665]
2025-06-28 15:06:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/do/ 200 [0.01, 127.0.0.1:61668]
2025-06-28 15:06:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/template/ 200 [0.01, 127.0.0.1:61683]
2025-06-28 15:06:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/test-plans/?replace_variables=true 200 [0.01, 127.0.0.1:61719]
2025-06-28 15:07:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:61244]
2025-06-28 15:07:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:61562]
2025-06-28 15:07:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.03, 127.0.0.1:61832]
2025-06-28 15:07:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.05, 127.0.0.1:61831]
2025-06-28 15:07:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.06, 127.0.0.1:61830]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61838]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61838]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61851]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61851]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:61853]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.02, 127.0.0.1:61856]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.03, 127.0.0.1:61861]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/do/ 200 [0.05, 127.0.0.1:61862]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /setting/user/ 200 [0.07, 127.0.0.1:61863]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.07, 127.0.0.1:61864]
2025-06-28 15:07:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.01, 127.0.0.1:61871]
2025-06-28 15:08:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:61838]
2025-06-28 15:08:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:61851]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61977]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61977]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/do/ 200 [0.03, 127.0.0.1:61973]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /setting/user/ 200 [0.05, 127.0.0.1:61976]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.08, 127.0.0.1:61971]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61985]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61985]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.11, 127.0.0.1:61970]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.06, 127.0.0.1:61981]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.16, 127.0.0.1:61972]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:61992]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.01, 127.0.0.1:61994]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.02, 127.0.0.1:61996]
2025-06-28 15:08:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.01, 127.0.0.1:61999]
2025-06-28 15:10:09 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:10:09 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:10:10 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:10:10 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:10:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:10:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:10:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:10:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:10:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:10:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:10:12 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:10:12 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:10:12 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:10:12 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:10:12 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:10:12 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:10:12 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:10:12 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:10:12 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:10:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:10:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:10:12 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:10:12 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:10:12 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:10:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:10:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:10:29 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 15:10:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:10:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:10:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:10:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:10:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 15:10:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 15:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:62243]
2025-06-28 15:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:62243]
2025-06-28 15:10:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:62252]
2025-06-28 15:10:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:62252]
2025-06-28 15:10:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/test-plans/ 200 [0.06, 127.0.0.1:62275]
2025-06-28 15:11:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:11:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:11:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:11:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/test-plans/11/ 200 [0.02, 127.0.0.1:62277]
2025-06-28 15:11:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/test-plans/ 200 [0.01, 127.0.0.1:62287]
2025-06-28 15:11:04 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /exec/test-plans/13/ 200 [0.01, 127.0.0.1:62289]
2025-06-28 15:11:29 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:11:29 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:11:29 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:11:29 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:11:38 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:11:38 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:11:38 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:11:38 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:22:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-compare/ 200 [0.05, 127.0.0.1:63048]
2025-06-28 15:22:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:63051]
2025-06-28 15:22:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.09, 127.0.0.1:63050]
2025-06-28 15:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:62243]
2025-06-28 15:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:63049]
2025-06-28 15:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.04, 127.0.0.1:63063]
2025-06-28 15:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:63064]
2025-06-28 15:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-compare/ 200 [0.07, 127.0.0.1:63062]
2025-06-28 15:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:63070]
2025-06-28 15:22:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:63070]
2025-06-28 15:22:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:63073]
2025-06-28 15:22:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63061]
2025-06-28 15:22:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/check-differences/ 200 [0.01, 127.0.0.1:63111]
2025-06-28 15:22:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-compare/ 200 [0.01, 127.0.0.1:63112]
2025-06-28 15:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/check-differences/ 200 [0.01, 127.0.0.1:63116]
2025-06-28 15:22:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-compare/ 200 [0.01, 127.0.0.1:63119]
2025-06-28 15:22:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63132]
2025-06-28 15:23:17 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.04, 127.0.0.1:63152]
2025-06-28 15:23:17 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.05, 127.0.0.1:63153]
2025-06-28 15:23:17 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-compare/ 200 [0.06, 127.0.0.1:63151]
2025-06-28 15:23:17 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-compare/ 200 [0.01, 127.0.0.1:63160]
2025-06-28 15:23:17 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:63162]
2025-06-28 15:23:17 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.03, 127.0.0.1:63161]
2025-06-28 15:23:18 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63150]
2025-06-28 15:23:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63168]
2025-06-28 15:23:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63219]
2025-06-28 15:23:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/check-differences/ 200 [0.01, 127.0.0.1:63221]
2025-06-28 15:23:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-compare/ 200 [0.01, 127.0.0.1:63226]
2025-06-28 15:24:18 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63244]
2025-06-28 15:24:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63296]
2025-06-28 15:25:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63433]
2025-06-28 15:25:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63552]
2025-06-28 15:26:19 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:63782]
2025-06-28 15:27:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:63070]
2025-06-28 15:27:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:62252]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:63916]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.14, 127.0.0.1:63912]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:63916]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.15, 127.0.0.1:63913]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.15, 127.0.0.1:63914]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:63922]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:63928]
2025-06-28 15:27:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:63928]
2025-06-28 15:27:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.02, 127.0.0.1:63937]
2025-06-28 15:27:06 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Not Found: /model-storage/file-tree-compare/
2025-06-28 15:27:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.10, 127.0.0.1:63938]
2025-06-28 15:27:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:149" - log_action - WARNING - HTTP GET /model-storage/file-tree-compare/ 404 [0.10, 127.0.0.1:63935]
2025-06-28 15:27:07 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:63936]
2025-06-28 15:27:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63965]
2025-06-28 15:28:07 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64000]
2025-06-28 15:28:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:63916]
2025-06-28 15:28:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:63928]
2025-06-28 15:28:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:64018]
2025-06-28 15:28:22 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Not Found: /model-storage/file-tree-compare/
2025-06-28 15:28:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:149" - log_action - WARNING - HTTP GET /model-storage/file-tree-compare/ 404 [0.06, 127.0.0.1:64020]
2025-06-28 15:28:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.08, 127.0.0.1:64021]
2025-06-28 15:28:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.11, 127.0.0.1:64022]
2025-06-28 15:28:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64025]
2025-06-28 15:28:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64025]
2025-06-28 15:28:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64036]
2025-06-28 15:28:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64036]
2025-06-28 15:28:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:64038]
2025-06-28 15:28:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/ 200 [0.02, 127.0.0.1:64043]
2025-06-28 15:28:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /host/group/ 200 [0.04, 127.0.0.1:64044]
2025-06-28 15:28:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:64019]
2025-06-28 15:28:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64077]
2025-06-28 15:29:10 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:29:11 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:29:11 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:29:11 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:29:13 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:29:13 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:29:13 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:29:13 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:29:13 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:29:14 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:29:14 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:29:14 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:29:14 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:29:14 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:29:14 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:29:14 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:29:14 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:29:14 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:29:14 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:29:14 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:29:14 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:29:14 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:29:14 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:29:14 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:29:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:29:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64154]
2025-06-28 15:29:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64154]
2025-06-28 15:29:28 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:29:28 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:29:28 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:29:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:64161]
2025-06-28 15:29:32 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:64161]
2025-06-28 15:29:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:64186]
2025-06-28 15:29:58 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:29:58 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:29:58 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:29:59 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:30:00 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:30:00 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:30:00 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:30:00 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:30:00 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:30:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:30:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:30:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:30:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:30:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:30:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:30:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:30:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:30:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:30:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:30:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:30:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:30:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:30:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:30:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:52:42 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:52:43 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:52:43 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:52:43 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 15:52:45 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:52:45 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:52:45 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:52:45 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:52:45 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:52:45 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:52:45 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:52:45 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:52:45 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:52:45 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:52:45 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:52:45 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:52:45 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:52:45 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:52:45 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:52:45 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:52:45 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:52:45 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:52:45 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:52:45 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:52:57 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:52:57 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:52:57 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 15:52:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 15:53:03 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:53:03 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:53:03 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:53:03 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:53:18 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:53:18 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:53:18 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:53:18 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:53:28 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:53:28 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:53:28 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:53:28 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:53:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:53:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 15:53:30 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 127.0.0.1:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:53:37 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:53:37 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:53:37 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:53:37 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:53:39 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:53:39 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8001:interface=127.0.0.1
2025-06-28 15:53:39 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8001
2025-06-28 15:53:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:56489]
2025-06-28 15:53:47 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:56489]
2025-06-28 15:55:51 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:56489]
2025-06-28 15:55:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /home/<USER>/ 200 [0.05, 127.0.0.1:56596]
2025-06-28 15:55:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /home/<USER>/ 200 [0.06, 127.0.0.1:56597]
2025-06-28 15:55:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.09, 127.0.0.1:56595]
2025-06-28 15:55:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:56602]
2025-06-28 15:55:52 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:56602]
2025-06-28 15:55:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.02, 127.0.0.1:56614]
2025-06-28 15:55:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.02, 127.0.0.1:56615]
2025-06-28 15:55:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:56616]
2025-06-28 15:55:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:56610]
2025-06-28 15:56:09 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/check-differences/ 200 [0.01, 127.0.0.1:56639]
2025-06-28 15:56:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.01, 127.0.0.1:56644]
2025-06-28 15:56:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56712]
2025-06-28 15:56:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:56750]
2025-06-28 15:57:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56785]
2025-06-28 15:57:25 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:56602]
2025-06-28 15:57:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.02, 127.0.0.1:56799]
2025-06-28 15:57:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.04, 127.0.0.1:56796]
2025-06-28 15:57:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.07, 127.0.0.1:56798]
2025-06-28 15:57:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:56805]
2025-06-28 15:57:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:56805]
2025-06-28 15:57:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:56809]
2025-06-28 15:57:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:56797]
2025-06-28 15:57:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56843]
2025-06-28 15:58:27 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56880]
2025-06-28 15:58:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56910]
2025-06-28 15:59:17 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:59:17 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:59:18 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:59:18 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 15:59:20 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:59:20 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:59:20 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:59:20 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:59:20 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:59:20 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:59:20 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:59:20 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:59:20 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:59:20 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:59:20 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:59:20 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 15:59:20 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:59:20 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 15:59:20 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:59:20 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:59:20 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 15:59:20 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 15:59:20 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:59:20 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 15:59:31 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:59:31 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:59:31 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 15:59:31 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:59:31 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:59:31 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:59:36 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57009]
2025-06-28 15:59:36 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57009]
2025-06-28 15:59:36 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:59:36 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 15:59:36 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 15:59:36 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 15:59:36 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 15:59:36 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 15:59:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57009]
2025-06-28 15:59:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.08, 127.0.0.1:57024]
2025-06-28 15:59:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.10, 127.0.0.1:57026]
2025-06-28 15:59:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.12, 127.0.0.1:57025]
2025-06-28 15:59:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57032]
2025-06-28 15:59:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57032]
2025-06-28 15:59:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:57036]
2025-06-28 15:59:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:57023]
2025-06-28 16:00:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57032]
2025-06-28 16:00:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57101]
2025-06-28 16:00:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57101]
2025-06-28 16:00:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.03, 127.0.0.1:57099]
2025-06-28 16:00:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.06, 127.0.0.1:57095]
2025-06-28 16:00:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.07, 127.0.0.1:57097]
2025-06-28 16:00:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.09, 127.0.0.1:57098]
2025-06-28 16:00:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:57096]
2025-06-28 16:00:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57101]
2025-06-28 16:00:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.01, 127.0.0.1:57123]
2025-06-28 16:00:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.04, 127.0.0.1:57127]
2025-06-28 16:00:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:57126]
2025-06-28 16:00:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.09, 127.0.0.1:57125]
2025-06-28 16:00:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57132]
2025-06-28 16:00:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57132]
2025-06-28 16:00:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:57124]
2025-06-28 16:00:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57132]
2025-06-28 16:00:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:57161]
2025-06-28 16:00:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.05, 127.0.0.1:57163]
2025-06-28 16:00:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.09, 127.0.0.1:57165]
2025-06-28 16:00:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.10, 127.0.0.1:57164]
2025-06-28 16:00:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57171]
2025-06-28 16:00:22 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57171]
2025-06-28 16:00:23 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:57162]
2025-06-28 16:00:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57229]
2025-06-28 16:00:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57171]
2025-06-28 16:00:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.05, 127.0.0.1:57260]
2025-06-28 16:00:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:57259]
2025-06-28 16:00:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.07, 127.0.0.1:57256]
2025-06-28 16:00:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.09, 127.0.0.1:57258]
2025-06-28 16:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57267]
2025-06-28 16:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57267]
2025-06-28 16:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57257]
2025-06-28 16:01:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57347]
2025-06-28 16:01:45 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:01:45 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:01:45 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:01:45 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:01:48 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:01:48 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8001:interface=127.0.0.1
2025-06-28 16:01:48 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 127.0.0.1:8001: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57373]
2025-06-28 16:02:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57400]
2025-06-28 16:02:33 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:02:33 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:02:33 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:02:33 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:02:39 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:02:39 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8010:interface=127.0.0.1
2025-06-28 16:02:39 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8010
2025-06-28 16:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57426]
2025-06-28 16:03:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57452]
2025-06-28 16:03:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57473]
2025-06-28 16:04:26 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57509]
2025-06-28 16:04:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57267]
2025-06-28 16:04:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.05, 127.0.0.1:57552]
2025-06-28 16:04:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:57554]
2025-06-28 16:04:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57558]
2025-06-28 16:04:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57558]
2025-06-28 16:04:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:57563]
2025-06-28 16:04:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.04, 127.0.0.1:57564]
2025-06-28 16:04:46 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57553]
2025-06-28 16:05:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57558]
2025-06-28 16:05:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:57666]
2025-06-28 16:05:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.06, 127.0.0.1:57668]
2025-06-28 16:05:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.10, 127.0.0.1:57669]
2025-06-28 16:05:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57675]
2025-06-28 16:05:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57675]
2025-06-28 16:05:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:57678]
2025-06-28 16:05:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:57667]
2025-06-28 16:05:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57902]
2025-06-28 16:06:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58119]
2025-06-28 16:06:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58178]
2025-06-28 16:07:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58225]
2025-06-28 16:07:28 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57675]
2025-06-28 16:07:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.02, 127.0.0.1:58263]
2025-06-28 16:07:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:58264]
2025-06-28 16:07:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.07, 127.0.0.1:58261]
2025-06-28 16:07:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58271]
2025-06-28 16:07:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58271]
2025-06-28 16:07:29 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:58274]
2025-06-28 16:07:30 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:58262]
2025-06-28 16:07:40 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:58271]
2025-06-28 16:07:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:53657]
2025-06-28 16:07:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.05, 127.0.0.1:53661]
2025-06-28 16:07:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:53667]
2025-06-28 16:07:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:53667]
2025-06-28 16:07:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:53672]
2025-06-28 16:07:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.04, 127.0.0.1:53671]
2025-06-28 16:07:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:53660]
2025-06-28 16:07:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:53667]
2025-06-28 16:07:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:53720]
2025-06-28 16:07:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:53720]
2025-06-28 16:07:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.03, 127.0.0.1:53717]
2025-06-28 16:07:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.08, 127.0.0.1:53715]
2025-06-28 16:07:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.09, 127.0.0.1:53719]
2025-06-28 16:07:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.09, 127.0.0.1:53721]
2025-06-28 16:07:51 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:53716]
2025-06-28 16:08:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53815]
2025-06-28 16:08:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:53720]
2025-06-28 16:08:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:53898]
2025-06-28 16:08:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.05, 127.0.0.1:53900]
2025-06-28 16:08:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.06, 127.0.0.1:53901]
2025-06-28 16:08:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:53908]
2025-06-28 16:08:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:53908]
2025-06-28 16:08:50 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:53907]
2025-06-28 16:08:51 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:53899]
2025-06-28 16:09:21 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53943]
2025-06-28 16:09:33 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:53908]
2025-06-28 16:09:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:49174]
2025-06-28 16:09:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.05, 127.0.0.1:49175]
2025-06-28 16:09:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.07, 127.0.0.1:49176]
2025-06-28 16:09:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:49179]
2025-06-28 16:09:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:49179]
2025-06-28 16:09:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:49183]
2025-06-28 16:09:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:49173]
2025-06-28 16:09:53 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:49179]
2025-06-28 16:09:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:49241]
2025-06-28 16:09:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.03, 127.0.0.1:49244]
2025-06-28 16:09:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.05, 127.0.0.1:49243]
2025-06-28 16:09:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:49253]
2025-06-28 16:09:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:49253]
2025-06-28 16:09:54 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:49252]
2025-06-28 16:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:49242]
2025-06-28 16:10:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:49253]
2025-06-28 16:10:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:49965]
2025-06-28 16:10:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:49979]
2025-06-28 16:10:02 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:49979]
2025-06-28 16:10:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.02, 127.0.0.1:49976]
2025-06-28 16:10:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.04, 127.0.0.1:49977]
2025-06-28 16:10:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.05, 127.0.0.1:49978]
2025-06-28 16:10:03 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:49970]
2025-06-28 16:10:33 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:49979]
2025-06-28 16:10:33 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50060]
2025-06-28 16:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.06, 127.0.0.1:50078]
2025-06-28 16:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.08, 127.0.0.1:50077]
2025-06-28 16:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.09, 127.0.0.1:50079]
2025-06-28 16:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.11, 127.0.0.1:50080]
2025-06-28 16:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:50084]
2025-06-28 16:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:50084]
2025-06-28 16:10:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:50084]
2025-06-28 16:10:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:50076]
2025-06-28 16:10:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:50098]
2025-06-28 16:10:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.09, 127.0.0.1:50096]
2025-06-28 16:10:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.11, 127.0.0.1:50097]
2025-06-28 16:10:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:50104]
2025-06-28 16:10:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:50104]
2025-06-28 16:10:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:50107]
2025-06-28 16:10:36 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50095]
2025-06-28 16:11:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50180]
2025-06-28 16:11:33 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:50104]
2025-06-28 16:11:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:50308]
2025-06-28 16:11:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:50304]
2025-06-28 16:11:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:50308]
2025-06-28 16:11:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.05, 127.0.0.1:50307]
2025-06-28 16:11:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.08, 127.0.0.1:50309]
2025-06-28 16:11:34 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.09, 127.0.0.1:50310]
2025-06-28 16:11:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:50306]
2025-06-28 16:12:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50385]
2025-06-28 16:12:35 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50453]
2025-06-28 16:12:59 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Unauthorized: /model-storage/file-tree-compare/
2025-06-28 16:12:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:151" - log_action - WARNING - HTTP GET /model-storage/file-tree-compare/ 401 [0.01, 127.0.0.1:50485]
2025-06-28 16:13:00 - django.request - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\log.py:222" - log_response - WARNING - Unauthorized: /model-storage/file-tree-compare/
2025-06-28 16:13:00 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:151" - log_action - WARNING - HTTP GET /model-storage/file-tree-compare/ 401 [0.00, 127.0.0.1:50488]
2025-06-28 16:13:05 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50494]
2025-06-28 16:13:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:50308]
2025-06-28 16:13:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.02, 127.0.0.1:50508]
2025-06-28 16:13:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.06, 127.0.0.1:50509]
2025-06-28 16:13:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.09, 127.0.0.1:50511]
2025-06-28 16:13:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:50517]
2025-06-28 16:13:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:50517]
2025-06-28 16:13:11 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:50521]
2025-06-28 16:13:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:50510]
2025-06-28 16:13:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50555]
2025-06-28 16:14:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50594]
2025-06-28 16:14:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50619]
2025-06-28 16:15:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50657]
2025-06-28 16:15:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50684]
2025-06-28 16:16:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50759]
2025-06-28 16:16:37 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:16:38 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:16:38 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:16:38 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:16:40 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:16:40 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:16:40 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:16:40 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:16:40 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:16:40 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:16:40 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:16:40 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:16:40 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:16:40 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:16:41 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:16:41 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:16:41 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:16:41 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:16:41 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:16:41 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:16:41 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:16:41 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:16:41 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:16:41 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 16:16:55 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 16:17:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:17:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:17:00 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:17:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61512]
2025-06-28 16:17:01 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61512]
2025-06-28 16:17:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:61558]
2025-06-28 16:18:38 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:18:39 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:18:39 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:18:39 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py changed, reloading.
2025-06-28 16:18:40 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:18:40 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:18:40 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:18:40 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:18:40 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:18:41 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:18:41 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:18:41 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:18:41 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:18:41 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:18:41 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:18:41 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:18:41 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:18:41 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:18:41 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:18:41 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:18:41 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:18:41 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:18:41 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:18:41 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:18:54 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 16:18:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:18:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:18:59 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:18:59 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 16:18:59 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 16:18:59 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 16:19:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:19:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:19:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:19:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:19:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:19:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:19:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:19:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:19:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:19:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:19:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:19:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:19:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:19:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:19:01 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:19:02 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:217" - trigger_reload - INFO - C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py changed, reloading.
2025-06-28 16:19:04 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:19:04 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:19:04 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:19:04 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:19:04 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 0.0.0.0:8000
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 0.0.0.0:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 16:19:13 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-28 16:19:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61740]
2025-06-28 16:19:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61740]
2025-06-28 16:19:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.04, 127.0.0.1:61747]
2025-06-28 16:19:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/ 200 [0.02, 127.0.0.1:61756]
2025-06-28 16:19:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.03, 127.0.0.1:61758]
2025-06-28 16:19:24 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.05, 127.0.0.1:61757]
2025-06-28 16:19:25 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:61751]
2025-06-28 16:19:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:61740]
2025-06-28 16:19:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:61792]
2025-06-28 16:19:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/lazy-load-tree/?path=%2FHDD_Raid%2FSVN_MODEL_REPO&depth=1 200 [0.07, 127.0.0.1:61795]
2025-06-28 16:19:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.09, 127.0.0.1:61796]
2025-06-28 16:19:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:61800]
2025-06-28 16:19:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:61800]
2025-06-28 16:19:44 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:61805]
2025-06-28 16:19:45 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:61794]
2025-06-28 16:20:12 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:61800]
2025-06-28 16:20:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:57980]
2025-06-28 16:20:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.06, 127.0.0.1:57983]
2025-06-28 16:20:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/lazy-load-tree/?path=%2FHDD_Raid%2FSVN_MODEL_REPO&depth=1 200 [0.08, 127.0.0.1:57982]
2025-06-28 16:20:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.10, 127.0.0.1:57984]
2025-06-28 16:20:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57987]
2025-06-28 16:20:13 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57987]
2025-06-28 16:20:14 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57981]
2025-06-28 16:20:37 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:57987]
2025-06-28 16:20:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.02, 127.0.0.1:58059]
2025-06-28 16:20:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.05, 127.0.0.1:58062]
2025-06-28 16:20:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.07, 127.0.0.1:58063]
2025-06-28 16:20:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/lazy-load-tree/?path=%2FHDD_Raid%2FSVN_MODEL_REPO&depth=1 200 [0.08, 127.0.0.1:58061]
2025-06-28 16:20:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58066]
2025-06-28 16:20:38 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58066]
2025-06-28 16:20:39 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:58060]
2025-06-28 16:21:06 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:58066]
2025-06-28 16:21:07 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:58126]
2025-06-28 16:21:07 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/lazy-load-tree/?path=%2FHDD_Raid%2FSVN_MODEL_REPO&depth=1 200 [0.06, 127.0.0.1:58130]
2025-06-28 16:21:07 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58135]
2025-06-28 16:21:07 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58135]
2025-06-28 16:21:08 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.01, 127.0.0.1:58140]
2025-06-28 16:21:08 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.02, 127.0.0.1:58141]
2025-06-28 16:21:08 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:58135]
2025-06-28 16:21:08 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:58128]
2025-06-28 16:21:09 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/lazy-load-tree/?path=%2FHDD_Raid%2FSVN_MODEL_REPO&depth=1 200 [0.02, 127.0.0.1:58158]
2025-06-28 16:21:09 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.06, 127.0.0.1:58156]
2025-06-28 16:21:09 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:58162]
2025-06-28 16:21:09 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:58162]
2025-06-28 16:21:09 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/svn-compare/ 200 [0.01, 127.0.0.1:58166]
2025-06-28 16:21:09 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/release-plans/ 200 [0.02, 127.0.0.1:58167]
2025-06-28 16:21:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:58157]
2025-06-28 16:21:21 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:21:21 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:21:21 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:21:21 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:21:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:21:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-28 16:21:23 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 127.0.0.1:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:21:30 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:21:30 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:21:30 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:21:30 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:21:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:21:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8001:interface=127.0.0.1
2025-06-28 16:21:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 127.0.0.1:8001: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-28 16:21:40 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:58202]
2025-06-28 16:21:43 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-28 16:21:43 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-28 16:21:43 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-28 16:21:43 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-28 16:21:45 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-28 16:21:45 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8002:interface=127.0.0.1
2025-06-28 16:21:45 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:150" - listen_success - INFO - Listening on TCP address 127.0.0.1:8002
2025-06-28 16:22:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49249]
2025-06-28 16:22:40 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49264]
2025-06-28 16:23:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49305]
2025-06-28 16:23:40 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49318]
2025-06-28 16:24:10 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49352]
2025-06-28 16:24:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49371]
2025-06-28 16:25:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:49446]
2025-06-28 16:26:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49528]
2025-06-28 16:27:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49606]
2025-06-28 16:28:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49634]
2025-06-28 16:29:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49701]
2025-06-28 16:30:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49749]
2025-06-28 16:31:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49830]
2025-06-28 16:32:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49919]
2025-06-28 16:33:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49980]
2025-06-28 16:34:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50017]
2025-06-28 16:35:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50100]
2025-06-28 16:36:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50154]
2025-06-28 16:37:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50227]
2025-06-28 16:38:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50252]
2025-06-28 16:39:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50305]
2025-06-28 16:40:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50364]
2025-06-28 16:41:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50491]
2025-06-28 16:42:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50547]
2025-06-28 16:43:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50607]
2025-06-28 16:44:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50635]
2025-06-28 16:45:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50702]
2025-06-28 16:46:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50733]
2025-06-28 16:47:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50797]
2025-06-28 16:48:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50822]
2025-06-28 16:49:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50877]
2025-06-28 16:50:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50930]
2025-06-28 16:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51005]
2025-06-28 16:52:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51047]
2025-06-28 16:53:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51115]
2025-06-28 16:54:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51154]
2025-06-28 16:55:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51216]
2025-06-28 16:56:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51311]
2025-06-28 16:57:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51365]
2025-06-28 16:58:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51410]
2025-06-28 16:59:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51453]
2025-06-28 17:00:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51519]
2025-06-28 17:01:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51574]
2025-06-28 17:02:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51643]
2025-06-28 17:03:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51681]
2025-06-28 17:04:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51735]
2025-06-28 17:05:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51814]
2025-06-28 17:06:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51871]
2025-06-28 17:07:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51917]
2025-06-28 17:08:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51954]
2025-06-28 17:09:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:51991]
2025-06-28 17:10:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52079]
2025-06-28 17:11:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52190]
2025-06-28 17:12:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52241]
2025-06-28 17:13:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52280]
2025-06-28 17:14:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52321]
2025-06-28 17:15:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52372]
2025-06-28 17:16:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:52421]
2025-06-28 17:17:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52462]
2025-06-28 17:18:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52502]
2025-06-28 17:19:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52541]
2025-06-28 17:20:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:52618]
2025-06-28 17:21:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52670]
2025-06-28 17:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:52734]
2025-06-28 17:23:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52763]
2025-06-28 17:24:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52823]
2025-06-28 17:25:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52878]
2025-06-28 17:26:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52987]
2025-06-28 17:27:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53049]
2025-06-28 17:28:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53106]
2025-06-28 17:29:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53131]
2025-06-28 17:30:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53203]
2025-06-28 17:31:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53250]
2025-06-28 17:32:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53349]
2025-06-28 17:33:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53372]
2025-06-28 17:34:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53427]
2025-06-28 17:35:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53482]
2025-06-28 17:36:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53536]
2025-06-28 17:37:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53598]
2025-06-28 17:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53650]
2025-06-28 17:39:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53682]
2025-06-28 17:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53772]
2025-06-28 17:41:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53891]
2025-06-28 17:42:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:53972]
2025-06-28 17:43:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54012]
2025-06-28 17:44:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54056]
2025-06-28 17:45:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54106]
2025-06-28 17:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54163]
2025-06-28 17:47:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54210]
2025-06-28 17:48:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:54252]
2025-06-28 17:49:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54286]
2025-06-28 17:50:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54356]
2025-06-28 17:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54410]
2025-06-28 17:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54473]
2025-06-28 17:53:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54512]
2025-06-28 17:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54555]
2025-06-28 17:55:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54615]
2025-06-28 17:56:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54721]
2025-06-28 17:57:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54763]
2025-06-28 17:58:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54810]
2025-06-28 17:59:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54842]
2025-06-28 18:00:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54907]
2025-06-28 18:01:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54976]
2025-06-28 18:02:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:55058]
2025-06-28 18:03:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55095]
2025-06-28 18:04:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:55149]
2025-06-28 18:05:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55203]
2025-06-28 18:06:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55265]
2025-06-28 18:07:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55309]
2025-06-28 18:08:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55361]
2025-06-28 18:09:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55387]
2025-06-28 18:10:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55487]
2025-06-28 18:11:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55582]
2025-06-28 18:12:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55655]
2025-06-28 18:13:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55695]
2025-06-28 18:14:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55744]
2025-06-28 18:15:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55782]
2025-06-28 18:16:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55846]
2025-06-28 18:17:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55895]
2025-06-28 18:18:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55948]
2025-06-28 18:19:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55982]
2025-06-28 18:20:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:56054]
2025-06-28 18:21:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56099]
2025-06-28 18:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:56174]
2025-06-28 18:23:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56212]
2025-06-28 18:24:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:56263]
2025-06-28 18:25:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56307]
2025-06-28 18:26:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56425]
2025-06-28 18:27:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56460]
2025-06-28 18:28:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56514]
2025-06-28 18:29:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56541]
2025-06-28 18:30:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56609]
2025-06-28 18:31:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56651]
2025-06-28 18:32:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56748]
2025-06-28 18:33:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56779]
2025-06-28 18:34:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:56831]
2025-06-28 18:35:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56881]
2025-06-28 18:36:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56947]
2025-06-28 18:37:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57000]
2025-06-28 18:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57054]
2025-06-28 18:39:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57083]
2025-06-28 18:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57416]
2025-06-28 18:41:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57511]
2025-06-28 18:42:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57636]
2025-06-28 18:43:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57675]
2025-06-28 18:44:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57727]
2025-06-28 18:45:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57772]
2025-06-28 18:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57836]
2025-06-28 18:47:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57895]
2025-06-28 18:48:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57941]
2025-06-28 18:49:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57971]
2025-06-28 18:50:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58040]
2025-06-28 18:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58095]
2025-06-28 18:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58176]
2025-06-28 18:53:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58206]
2025-06-28 18:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58260]
2025-06-28 18:55:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58316]
2025-06-28 18:56:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58444]
2025-06-28 18:57:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58484]
2025-06-28 18:58:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58550]
2025-06-28 18:59:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58580]
2025-06-28 19:00:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58652]
2025-06-28 19:01:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:58709]
2025-06-28 19:02:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:58776]
2025-06-28 19:03:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58805]
2025-06-28 19:04:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:58856]
2025-06-28 19:05:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58914]
2025-06-28 19:06:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58981]
2025-06-28 19:07:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59046]
2025-06-28 19:08:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59104]
2025-06-28 19:09:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59136]
2025-06-28 19:10:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59216]
2025-06-28 19:11:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59316]
2025-06-28 19:12:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59388]
2025-06-28 19:13:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59427]
2025-06-28 19:14:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59476]
2025-06-28 19:15:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59520]
2025-06-28 19:16:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59579]
2025-06-28 19:17:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59619]
2025-06-28 19:18:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59680]
2025-06-28 19:19:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59715]
2025-06-28 19:20:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59787]
2025-06-28 19:21:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59858]
2025-06-28 19:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59931]
2025-06-28 19:23:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59961]
2025-06-28 19:24:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60018]
2025-06-28 19:25:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60066]
2025-06-28 19:26:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60180]
2025-06-28 19:27:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60229]
2025-06-28 19:28:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60274]
2025-06-28 19:29:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:60308]
2025-06-28 19:30:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60374]
2025-06-28 19:31:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60423]
2025-06-28 19:32:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60490]
2025-06-28 19:33:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60522]
2025-06-28 19:34:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60575]
2025-06-28 19:35:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:60638]
2025-06-28 19:36:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60693]
2025-06-28 19:37:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60745]
2025-06-28 19:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60793]
2025-06-28 19:39:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60827]
2025-06-28 19:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60901]
2025-06-28 19:41:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61002]
2025-06-28 19:42:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61083]
2025-06-28 19:43:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61116]
2025-06-28 19:44:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61163]
2025-06-28 19:45:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61207]
2025-06-28 19:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61294]
2025-06-28 19:47:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61336]
2025-06-28 19:48:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61391]
2025-06-28 19:49:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61424]
2025-06-28 19:50:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61491]
2025-06-28 19:51:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61541]
2025-06-28 19:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61628]
2025-06-28 19:53:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61676]
2025-06-28 19:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61753]
2025-06-28 19:55:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61804]
2025-06-28 19:56:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61913]
2025-06-28 19:57:48 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61981]
2025-06-28 19:58:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62032]
2025-06-28 19:59:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62083]
2025-06-28 20:00:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:62153]
2025-06-28 20:01:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62218]
2025-06-28 20:02:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62293]
2025-06-28 20:03:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:62322]
2025-06-28 20:04:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:62383]
2025-06-28 20:05:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62436]
2025-06-28 20:06:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62491]
2025-06-28 20:07:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62541]
2025-06-28 20:08:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62591]
2025-06-28 20:09:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62624]
2025-06-28 20:10:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62707]
2025-06-28 20:11:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62818]
2025-06-28 20:12:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62902]
2025-06-28 20:13:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62943]
2025-06-28 20:14:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62994]
2025-06-28 20:15:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63036]
2025-06-28 20:16:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63101]
2025-06-28 20:17:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63152]
2025-06-28 20:18:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63211]
2025-06-28 20:19:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63251]
2025-06-28 20:20:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63326]
2025-06-28 20:21:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63372]
2025-06-28 20:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63457]
2025-06-28 20:23:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63484]
2025-06-28 20:24:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63539]
2025-06-28 20:25:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63586]
2025-06-28 20:26:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63696]
2025-06-28 20:27:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63728]
2025-06-28 20:28:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63786]
2025-06-28 20:29:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63810]
2025-06-28 20:30:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63887]
2025-06-28 20:31:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63935]
2025-06-28 20:32:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64025]
2025-06-28 20:33:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64061]
2025-06-28 20:34:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64113]
2025-06-28 20:35:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64164]
2025-06-28 20:36:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64238]
2025-06-28 20:37:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64286]
2025-06-28 20:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64342]
2025-06-28 20:39:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64367]
2025-06-28 20:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64441]
2025-06-28 20:41:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64545]
2025-06-28 20:42:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64637]
2025-06-28 20:43:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64673]
2025-06-28 20:44:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64752]
2025-06-28 20:45:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64793]
2025-06-28 20:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64852]
2025-06-28 20:47:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64902]
2025-06-28 20:48:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64958]
2025-06-28 20:49:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64982]
2025-06-28 20:50:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65062]
2025-06-28 20:51:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65111]
2025-06-28 20:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65199]
2025-06-28 20:53:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65230]
2025-06-28 20:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65285]
2025-06-28 20:55:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65328]
2025-06-28 20:56:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65441]
2025-06-28 20:57:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65486]
2025-06-28 20:58:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49157]
2025-06-28 20:59:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49186]
2025-06-28 21:00:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49254]
2025-06-28 21:01:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49306]
2025-06-28 21:02:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49398]
2025-06-28 21:03:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49435]
2025-06-28 21:04:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49498]
2025-06-28 21:05:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49547]
2025-06-28 21:06:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49612]
2025-06-28 21:07:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49659]
2025-06-28 21:08:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49723]
2025-06-28 21:09:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49750]
2025-06-28 21:10:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:49841]
2025-06-28 21:11:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49938]
2025-06-28 21:12:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50037]
2025-06-28 21:13:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50074]
2025-06-28 21:14:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50144]
2025-06-28 21:15:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50188]
2025-06-28 21:16:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50246]
2025-06-28 21:17:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50295]
2025-06-28 21:18:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50349]
2025-06-28 21:19:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50379]
2025-06-28 21:20:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50455]
2025-06-28 21:21:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:50510]
2025-06-28 21:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50585]
2025-06-28 21:23:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50640]
2025-06-28 21:24:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:50698]
2025-06-28 21:25:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50757]
2025-06-28 21:26:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50867]
2025-06-28 21:27:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50904]
2025-06-28 21:28:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:50959]
2025-06-28 21:29:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50989]
2025-06-28 21:30:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51061]
2025-06-28 21:31:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51107]
2025-06-28 21:32:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51200]
2025-06-28 21:33:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51229]
2025-06-28 21:34:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51286]
2025-06-28 21:35:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51337]
2025-06-28 21:36:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51399]
2025-06-28 21:37:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51487]
2025-06-28 21:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51547]
2025-06-28 21:39:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51575]
2025-06-28 21:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50884]
2025-06-28 21:41:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50984]
2025-06-28 21:42:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56708]
2025-06-28 21:43:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56751]
2025-06-28 21:44:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56795]
2025-06-28 21:45:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56841]
2025-06-28 21:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56900]
2025-06-28 21:47:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56951]
2025-06-28 21:48:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57001]
2025-06-28 21:49:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57031]
2025-06-28 21:50:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57100]
2025-06-28 21:51:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57153]
2025-06-28 21:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57229]
2025-06-28 21:53:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57285]
2025-06-28 21:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57340]
2025-06-28 21:55:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57392]
2025-06-28 21:56:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57507]
2025-06-28 21:57:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57546]
2025-06-28 21:58:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57598]
2025-06-28 21:59:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57629]
2025-06-28 22:00:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57691]
2025-06-28 22:01:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57745]
2025-06-28 22:02:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57828]
2025-06-28 22:03:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57859]
2025-06-28 22:04:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57910]
2025-06-28 22:05:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57961]
2025-06-28 22:06:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58020]
2025-06-28 22:07:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58055]
2025-06-28 22:08:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58107]
2025-06-28 22:09:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58147]
2025-06-28 22:10:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58253]
2025-06-28 22:11:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65389]
2025-06-28 22:12:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65483]
2025-06-28 22:13:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60522]
2025-06-28 22:14:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60569]
2025-06-28 22:15:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60624]
2025-06-28 22:16:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60680]
2025-06-28 22:17:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60726]
2025-06-28 22:18:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:60773]
2025-06-28 22:19:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60818]
2025-06-28 22:20:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60889]
2025-06-28 22:21:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60941]
2025-06-28 22:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61022]
2025-06-28 22:23:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61057]
2025-06-28 22:24:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61108]
2025-06-28 22:25:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61161]
2025-06-28 22:26:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61296]
2025-06-28 22:27:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61348]
2025-06-28 22:28:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61401]
2025-06-28 22:29:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61433]
2025-06-28 22:30:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61496]
2025-06-28 22:31:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61550]
2025-06-28 22:32:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61622]
2025-06-28 22:33:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61660]
2025-06-28 22:34:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61733]
2025-06-28 22:35:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61793]
2025-06-28 22:36:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61865]
2025-06-28 22:37:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61919]
2025-06-28 22:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61973]
2025-06-28 22:39:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62004]
2025-06-28 22:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:62087]
2025-06-28 22:41:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62198]
2025-06-28 22:42:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62277]
2025-06-28 22:43:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62313]
2025-06-28 22:44:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62361]
2025-06-28 22:45:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62408]
2025-06-28 22:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62461]
2025-06-28 22:47:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62526]
2025-06-28 22:48:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62574]
2025-06-28 22:49:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62609]
2025-06-28 22:50:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62686]
2025-06-28 22:51:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62737]
2025-06-28 22:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62806]
2025-06-28 22:53:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62858]
2025-06-28 22:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62908]
2025-06-28 22:55:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:62964]
2025-06-28 22:56:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63070]
2025-06-28 22:57:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63110]
2025-06-28 22:58:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63157]
2025-06-28 22:59:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63192]
2025-06-28 23:00:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63260]
2025-06-28 23:01:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63312]
2025-06-28 23:02:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63400]
2025-06-28 23:03:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63430]
2025-06-28 23:04:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:63488]
2025-06-28 23:05:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63541]
2025-06-28 23:06:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63600]
2025-06-28 23:07:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63641]
2025-06-28 23:08:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63688]
2025-06-28 23:09:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63720]
2025-06-28 23:10:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63798]
2025-06-28 23:11:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:63895]
2025-06-28 23:12:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63973]
2025-06-28 23:13:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64016]
2025-06-28 23:14:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64070]
2025-06-28 23:15:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64110]
2025-06-28 23:16:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64173]
2025-06-28 23:17:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64209]
2025-06-28 23:18:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64259]
2025-06-28 23:19:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64298]
2025-06-28 23:20:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64371]
2025-06-28 23:21:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64419]
2025-06-28 23:22:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64491]
2025-06-28 23:23:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64521]
2025-06-28 23:24:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64577]
2025-06-28 23:25:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64627]
2025-06-28 23:26:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:64757]
2025-06-28 23:27:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64811]
2025-06-28 23:28:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64865]
2025-06-28 23:29:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64895]
2025-06-28 23:30:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64962]
2025-06-28 23:31:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65006]
2025-06-28 23:32:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65081]
2025-06-28 23:33:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65108]
2025-06-28 23:34:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:65162]
2025-06-28 23:35:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65210]
2025-06-28 23:36:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65272]
2025-06-28 23:37:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65300]
2025-06-28 23:38:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:65353]
2025-06-28 23:39:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65380]
2025-06-28 23:40:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65453]
2025-06-28 23:41:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49174]
2025-06-28 23:42:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49246]
2025-06-28 23:43:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49272]
2025-06-28 23:44:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49329]
2025-06-28 23:45:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49368]
2025-06-28 23:46:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49433]
2025-06-28 23:47:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49469]
2025-06-28 23:48:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49523]
2025-06-28 23:49:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49565]
2025-06-28 23:50:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49645]
2025-06-28 23:51:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49697]
2025-06-28 23:52:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49771]
2025-06-28 23:53:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49797]
2025-06-28 23:54:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49858]
2025-06-28 23:55:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49909]
2025-06-28 23:56:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50027]
2025-06-28 23:57:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:50068]
2025-06-28 23:58:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:50134]
2025-06-28 23:59:49 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:50170]
