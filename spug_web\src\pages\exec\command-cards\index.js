/**
 * Copyright (c) H3C Heterogeneous Operations Platform.
 * Copyright (c) H3C Technologies Co., Ltd.
 * Released under the Internal License.
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Row,
  Col,
  Tag,
  Empty,
  Spin,
  Drawer,
  Form,
  Input,
  message,
  Space,
  Tooltip,
  Popconfirm,
  Upload,
  Modal,
  Checkbox,
  Dropdown,
  Menu
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  SaveOutlined,
  CloseOutlined,
  DownloadOutlined,
  UploadOutlined,
  CheckSquareOutlined,
  WifiOutlined
} from '@ant-design/icons';
import { http, history } from 'libs';
import CardEditor from './CardEditor';
import styles from './index.module.less';

// 默认命令字段
const DEFAULT_COMMAND_FIELDS = [
  { key: 'prepare', label: '环境检查', command: '', description: '检查当前驱动状态和磁盘空间', enabled: true },
  { key: 'backup', label: '备份当前驱动', command: '', description: '备份当前驱动程序', enabled: true },
  { key: 'install_driver', label: '安装驱动', command: '', description: '执行驱动安装', enabled: true },
  { key: 'verify', label: '验证安装', command: '', description: '验证驱动安装结果', enabled: true }
];

// 默认文件字段
const DEFAULT_FILE_FIELDS = [
  { id: 'file1', name: 'driver.run', localPath: '固件/', targetPath: '/tmp/', description: '驱动安装文件', enabled: true },
  { id: 'file2', name: 'firmware.bin', localPath: '固件/', targetPath: '/opt/firmware/', description: '设备固件文件', enabled: true }
];

function TestPlans() {
  const [cards, setCards] = useState([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editorVisible, setEditorVisible] = useState(false);
  const [editingCard, setEditingCard] = useState(null);
  const [selectedCard, setSelectedCard] = useState(null);
  const [deleting, setDeleting] = useState(false);
  const [form] = Form.useForm();
  
  // 导入导出相关状态
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [selectedCards, setSelectedCards] = useState([]);
  const [selectMode, setSelectMode] = useState(false);
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [importOptions, setImportOptions] = useState({
    skipExisting: false,
    updateExisting: true
  });
  
  // 导出模式选择
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportMode, setExportMode] = useState('json'); // 'json' 或 'zip'
  
  // SSH连接测试相关状态
  const [sshTestModalVisible, setSshTestModalVisible] = useState(false);
  const [sshTestResults, setSshTestResults] = useState([]);
  const [sshTesting, setSshTesting] = useState(false);

  useEffect(() => {
    loadCards();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const loadCards = async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/exec/test-plans/');
      console.log('Load test plans response:', response);
      
      // 确保response是数组
      let plansData = [];
      if (Array.isArray(response)) {
        plansData = response;
      } else if (response && Array.isArray(response.plans)) {
        plansData = response.plans;
      } else if (response && Array.isArray(response.data)) {
        plansData = response.data;
      } else {
        console.warn('API返回的数据格式不正确:', response);
        plansData = [];
      }
      
      setCards(plansData);
      console.log('设置的测试计划数据:', plansData);
    } catch (error) {
      console.error('Load test plans error:', error);
      message.error('加载测试计划失败');
      setCards([]); // 确保设置为空数组
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCard = () => {
    // 跳转到新建页面
    history.push('/exec/test-plan/new');
  };

  const handleEditCard = (card) => {
    setEditingCard(card);
    form.setFieldsValue({
      name: card.name,
      description: card.description,
      category: card.category
    });
    setDrawerVisible(true);
  };

  const handleSaveCard = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingCard) {
        // 更新测试计划
        await http.put(`/api/exec/test-plans/${editingCard.id}/`, {
          ...editingCard,
          ...values
        });
        message.success('测试计划更新成功');
      } else {
        // 创建新测试计划
        const newPlan = {
          ...values,
          commands: DEFAULT_COMMAND_FIELDS.map(field => ({ ...field })),
          files: DEFAULT_FILE_FIELDS.map(field => ({ ...field }))
        };
        await http.post('/api/exec/test-plans/', newPlan);
        message.success('测试计划创建成功');
      }
      
      setDrawerVisible(false);
      loadCards();
    } catch (error) {
      message.error('保存失败');
      console.error('Save test plan error:', error);
    }
  };

  const handleDeleteCard = async (cardId) => {
    try {
      setDeleting(true);
      await http.delete(`/api/exec/test-plans/${cardId}/`);
      message.success('删除成功');
      await loadCards();
      // 延长延迟时间，确保UI更新完成且避免意外点击事件
      setTimeout(() => setDeleting(false), 1000);
    } catch (error) {
      message.error('删除失败');
      console.error('Delete test plan error:', error);
      setDeleting(false);
    }
  };

  const handleCopyCard = async (card) => {
    try {
      const newPlan = {
        ...card,
        name: `${card.name} - 副本`,
        id: undefined
      };
      await http.post('/api/exec/test-plans/', newPlan);
      message.success('复制成功');
      loadCards();
    } catch (error) {
      message.error('复制失败');
      console.error('Copy test plan error:', error);
    }
  };

  const handleEditCommands = (card) => {
    // 跳转到新的编辑页面
    history.push(`/exec/test-plan/${card.id}`);
  };

  const handleCommandsUpdated = () => {
    setEditorVisible(false);
    loadCards();
  };

  // 导出功能 - 显示模式选择对话框
  const handleExport = () => {
    if (exporting) return;
    
    if (selectedCards.length === 0) {
      message.warning('请选择要导出的测试计划');
      return;
    }
    
    setExportModalVisible(true);
  };

  // 执行导出
  const executeExport = (includeFiles = false) => {
    setExporting(true);
    setExportModalVisible(false);
    
    const planIds = selectedCards.join(',');
    const url = `/api/exec/test-plans-import-export/?plan_ids=${planIds}&include_files=${includeFiles}`;
    
    // 防止重复下载
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = includeFiles ? 
      `test_plans_with_files_${timestamp}.zip` : 
      `test_plans_export_${timestamp}.json`;
    
    // 使用原生fetch确保正确处理blob响应
    const token = localStorage.getItem('token');
    if (!token) {
      message.error('认证信息不存在，请重新登录');
      setExporting(false);
      return;
    }
    
    fetch(url, {
      method: 'GET',
      headers: {
        'X-Token': token
      }
    })
      .then(response => {
        console.log('导出响应状态:', response.status);
        console.log('导出响应头:', Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
          // 尝试解析错误信息
          return response.text().then(text => {
            console.log('导出错误响应内容:', text);
            try {
              const errorData = JSON.parse(text);
              throw new Error(errorData.error || errorData.message || `HTTP ${response.status}`);
            } catch (e) {
              throw new Error(`HTTP ${response.status}: ${text || 'Unknown error'}`);
            }
          });
        }
        
        // 根据导出模式检查响应类型
        const contentType = response.headers.get('content-type');
        console.log('导出响应类型:', contentType);
        
        if ((includeFiles && contentType && contentType.includes('application/zip')) ||
            (!includeFiles && contentType && contentType.includes('application/json'))) {
          return response.blob();
        } else {
          // 尝试获取响应文本来调试
          return response.text().then(text => {
            console.log('响应内容:', text.substring(0, 200));
            throw new Error(`服务器返回了无效的响应格式: ${contentType || 'unknown'}`);
          });
        }
      })
      .then(blob => {
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        
        message.success(`导出成功！${includeFiles ? '已包含关联文件' : '仅配置文件'}`);
        setSelectMode(false);
        setSelectedCards([]);
      })
      .catch(error => {
        console.error('导出失败:', error);
        message.error(`导出失败: ${error.message}`);
      })
      .finally(() => {
        setExporting(false);
      });
  };

  // 导出所有 - 分别导出每个测试计划
  const handleExportAll = async () => {
    if (exporting) return; // 防止重复点击
    
    if (cards.length === 0) {
      message.warning('没有测试计划可导出');
      return;
    }
    
    setExporting(true);
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    let successCount = 0;
    let failCount = 0;
    
    try {
      // 添加延迟函数，避免下载过快
      const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
      
      for (let i = 0; i < cards.length; i++) {
        const card = cards[i];
        
        // 显示当前导出进度
        const progressMsg = `正在导出 (${i + 1}/${cards.length}): ${card.name}`;
        message.loading({ content: progressMsg, key: 'export-progress', duration: 0 });
        
        try {
          // 使用项目的 http 工具，但需要直接获取 blob 响应
          const url = `/api/exec/test-plans-import-export/?plan_ids=${card.id}`;
          
          // 直接使用 fetch 但添加认证头
          const token = localStorage.getItem('token');
          if (!token) {
            throw new Error('认证信息不存在，请重新登录');
          }
          
          const response = await fetch(url, {
            headers: {
              'X-Token': token
            }
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          
          const blob = await response.blob();
          
          // 生成安全的文件名（移除特殊字符）
          const safeName = card.name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5_-]/g, '_');
          const filename = `${safeName}_${timestamp}.json`;
          
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = filename;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(downloadUrl);
          
          successCount++;
          
          // 每个下载之间添加短暂延迟，避免浏览器限制
          if (i < cards.length - 1) {
            await delay(500);
          }
          
        } catch (error) {
          console.error(`导出测试计划 "${card.name}" 失败:`, error);
          failCount++;
        }
      }
      
      // 清除进度提示
      message.destroy('export-progress');
      
      // 显示结果
      if (failCount === 0) {
        message.success(`导出完成！成功导出 ${successCount} 个测试计划`);
      } else {
        message.warning(`导出完成！成功 ${successCount} 个，失败 ${failCount} 个`);
      }
      
    } catch (error) {
      console.error('批量导出失败:', error);
      message.error('批量导出失败，请重试');
    } finally {
      setExporting(false);
    }
  };

  // 导入功能
  const handleImport = async (file) => {
    setImporting(true);
    
    // 检查文件类型
    const isZipFile = file.name.toLowerCase().endsWith('.zip');
    const isJsonFile = file.name.toLowerCase().endsWith('.json');
    
    if (!isZipFile && !isJsonFile) {
      message.error('只支持 JSON 或 ZIP 格式的文件');
      setImporting(false);
      return false;
    }
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('skip_existing', importOptions.skipExisting);
      formData.append('update_existing', importOptions.updateExisting);
      
      const response = await http.post('/api/exec/test-plans-import-export/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      message.success(response.message || '导入成功');
      
      // 显示详细结果
      if (response.details) {
        const { imported, updated, skipped, errors, error_messages, files_imported, import_folder } = response.details;
        let detailMsg = `导入结果: 新增${imported}个, 更新${updated}个, 跳过${skipped}个`;
        if (errors > 0) {
          detailMsg += `, 失败${errors}个`;
          if (error_messages && error_messages.length > 0) {
            detailMsg += `\n错误信息: ${error_messages.join(', ')}`;
          }
        }
        
        // 如果是压缩包导入，显示文件导入信息
        if (isZipFile && files_imported !== undefined) {
          detailMsg += `\n文件导入: ${files_imported}个文件导入到 ${import_folder} 目录`;
        }
        
        Modal.info({
          title: isZipFile ? '压缩包导入完成' : '配置导入完成',
          content: (
            <div>
              <div style={{ marginBottom: '8px' }}>
                📊 <strong>统计信息</strong>
              </div>
              <div style={{ marginLeft: '16px', color: '#666' }}>
                {detailMsg.split('\n').map((line, index) => (
                  <div key={index}>{line}</div>
                ))}
              </div>
              {isZipFile && files_imported > 0 && (
                <div style={{ marginTop: '12px', padding: '8px', background: '#f0f9ff', borderRadius: '4px' }}>
                  <div style={{ color: '#0369a1', fontSize: '12px' }}>
                    💡 <strong>提示</strong>：压缩包中的文件已导入到文件管理器，测试计划中的文件路径已自动更新
                  </div>
                </div>
              )}
            </div>
          ),
          width: 600
        });
      }
      
      setImportModalVisible(false);
      loadCards();
    } catch (error) {
      message.error(error.message || '导入失败');
    } finally {
      setImporting(false);
    }
    
    return false; // 阻止自动上传
  };

  // 切换选择模式
  const toggleSelectMode = () => {
    setSelectMode(!selectMode);
    setSelectedCards([]);
  };

  // 选择/取消选择卡片
  const toggleCardSelection = (cardId) => {
    setSelectedCards(prev => 
      prev.includes(cardId) 
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedCards.length === cards.length) {
      setSelectedCards([]);
    } else {
      setSelectedCards(cards.map(card => card.id));
    }
  };

  // SSH连接测试
  const handleSSHTest = async () => {
    try {
      setSshTesting(true);
      setSshTestModalVisible(true);
      setSshTestResults([]);
      
      // 获取所有主机列表
      const hostsResponse = await http.get('/api/host/');
      const hosts = Array.isArray(hostsResponse) ? hostsResponse : (hostsResponse.data || []);
      
      if (hosts.length === 0) {
        message.warning('没有找到可测试的主机');
        setSshTesting(false);
        return;
      }
      
      const hostIds = hosts.map(host => host.id);
      
      // 执行SSH连接测试
      const response = await http.post('/api/exec/ssh-connection-test/', {
        host_ids: hostIds
      });
      
      setSshTestResults(response.results || []);
      
      const summary = response.summary || {};
      if (summary.success > 0) {
        message.success(`SSH连接测试完成：${summary.success}/${summary.total} 主机连接正常`);
      } else {
        message.error(`SSH连接测试完成：所有主机连接失败`);
      }
      
    } catch (error) {
      console.error('SSH connection test error:', error);
      message.error('SSH连接测试失败');
    } finally {
      setSshTesting(false);
    }
  };

  const getCardIcon = (category) => {
    const iconMap = {
      performance: <ThunderboltOutlined style={{ color: '#52c41a' }} />,
      diagnostic: <SettingOutlined style={{ color: '#1890ff' }} />,
      upgrade: <SettingOutlined style={{ color: '#fa541c' }} />,
      test: <ThunderboltOutlined style={{ color: '#722ed1' }} />
    };
    return iconMap[category] || <SettingOutlined style={{ color: '#666' }} />;
  };

  const renderCard = (card) => {
    const isSelected = selectedCards.includes(card.id);
    
    const cardActions = [
      <Tooltip title="编辑测试计划" key="edit">
        <EditOutlined 
          onClick={(e) => { 
            e.stopPropagation(); 
            handleEditCommands(card); 
          }} 
        />
      </Tooltip>,
      <Tooltip title="编辑基本信息" key="settings">
        <SettingOutlined 
          onClick={(e) => { 
            e.stopPropagation(); 
            handleEditCard(card); 
          }} 
        />
      </Tooltip>,
      <Tooltip title="复制" key="copy">
        <CopyOutlined 
          onClick={(e) => { 
            e.stopPropagation(); 
            handleCopyCard(card); 
          }} 
        />
      </Tooltip>,
      <Tooltip title="删除" key="delete">
        <Popconfirm
          title="确定要删除这个测试计划吗？"
          onConfirm={(e) => {
            e && e.stopPropagation();
            handleDeleteCard(card.id);
          }}
          onCancel={(e) => e && e.stopPropagation()}
          okText="删除"
          cancelText="取消"
          overlayStyle={{ zIndex: 1050 }}
        >
          <DeleteOutlined 
            style={{ color: '#ff4d4f' }} 
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          />
        </Popconfirm>
      </Tooltip>
    ];

    return (
      <div 
        key={card.id} 
        style={{ position: 'relative' }}
        onClick={selectMode ? () => toggleCardSelection(card.id) : undefined}
      >
        {selectMode && (
          <div style={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 10,
            background: 'white',
            borderRadius: 4,
            padding: 2
          }}>
            <Checkbox 
              checked={isSelected}
              onChange={() => toggleCardSelection(card.id)}
            />
          </div>
        )}
        <Card
          className={`${styles.commandCard} ${selectMode && isSelected ? styles.selectedCard : ''}`}
          hoverable={!selectMode}
          actions={selectMode ? [] : cardActions}
          style={{ 
            cursor: selectMode ? 'pointer' : 'default',
            border: selectMode && isSelected ? '2px solid #1890ff' : undefined
          }}
        >
        <Card.Meta
          avatar={getCardIcon(card.category)}
          title={
            <div className={styles.cardTitle}>
              <span>{card.name}</span>
            </div>
          }
          description={
            <div className={styles.cardContent}>
              <p className={styles.description}>{card.description}</p>
              <div className={styles.commandCount}>
                <Tag color="orange">
                  📁 {card.files?.filter(f => f.enabled).length || 0} 个文件
                </Tag>
                <Tag color="blue">
                  🚀 {card.commands?.filter(cmd => cmd.enabled).length || 0} 个命令
                </Tag>
                {card.category && (
                  <Tag color="green">{card.category}</Tag>
                )}
              </div>
              <div className={styles.commandPreview}>
                {card.commands?.slice(0, 3).map(cmd => (
                  <Tag key={cmd.key} size="small" style={{ marginBottom: 4 }}>
                    {cmd.label}
                  </Tag>
                ))}
                {card.commands?.length > 3 && (
                  <Tag size="small" style={{ marginBottom: 4 }}>
                    +{card.commands.length - 3} 更多
                  </Tag>
                )}
              </div>
              <div style={{ marginTop: 8, fontSize: '12px', color: '#999', textAlign: 'center' }}>
                使用底部按钮操作测试计划
              </div>
            </div>
          }
        />
        </Card>
      </div>
    );
  };

  return (
    <div className={styles.commandCardsPage}>
      <Card
        title={
          <Space>
            <span>测试计划</span>
            {selectMode && (
              <span style={{ fontSize: '14px', color: '#666' }}>
                已选择 {selectedCards.length} 项
              </span>
            )}
          </Space>
        }
        extra={
          <Space>
            {!selectMode ? (
              <>
                <Dropdown
                  overlay={
                    <Menu>
                      <Menu.Item key="export-selected" onClick={toggleSelectMode}>
                        <CheckSquareOutlined /> 选择导出
                      </Menu.Item>
                      <Menu.Item key="export-all" onClick={handleExportAll} disabled={exporting}>
                        <DownloadOutlined /> 分别导出全部
                      </Menu.Item>
                    </Menu>
                  }
                  trigger={['click']}
                >
                  <Button icon={<DownloadOutlined />}>
                    导出 <span style={{ marginLeft: 4 }}>▼</span>
                  </Button>
                </Dropdown>
                <Button 
                  icon={<UploadOutlined />}
                  onClick={() => setImportModalVisible(true)}
                >
                  导入
                </Button>
                <Button 
                  icon={<WifiOutlined />}
                  onClick={handleSSHTest}
                  loading={sshTesting}
                >
                  SSH连接测试
                </Button>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={handleCreateCard}
                >
                  新建测试计划
                </Button>
              </>
            ) : (
              <>
                <Checkbox
                  indeterminate={selectedCards.length > 0 && selectedCards.length < cards.length}
                  checked={selectedCards.length === cards.length && cards.length > 0}
                  onChange={toggleSelectAll}
                >
                  全选
                </Checkbox>
                <Button 
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  disabled={selectedCards.length === 0}
                  loading={exporting}
                >
                  导出选中 ({selectedCards.length})
                </Button>
                <Button onClick={toggleSelectMode}>
                  取消
                </Button>
              </>
            )}
          </Space>
        }
      >
        <Spin spinning={loading}>
          {cards.length === 0 ? (
            <Empty
              description="暂无测试计划"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateCard}>
                创建第一个测试计划
              </Button>
            </Empty>
          ) : (
            <Row gutter={[16, 16]}>
              {cards.map(card => (
                <Col xs={24} sm={12} md={8} lg={6} xl={6} key={card.id}>
                  {renderCard(card)}
                </Col>
              ))}
            </Row>
          )}
        </Spin>
      </Card>

      {/* 新建/编辑卡片侧边栏 */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span>{editingCard ? '编辑测试计划' : '新建测试计划'}</span>
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveCard}
              >
                保存计划
              </Button>
              <Button
                icon={<CloseOutlined />}
                onClick={() => setDrawerVisible(false)}
              >
                关闭
              </Button>
            </Space>
          </div>
        }
        placement="right"
        size="default"
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        maskClosable={false}
        destroyOnClose={true}
        bodyStyle={{ padding: '16px' }}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="测试计划名称"
            rules={[{ required: true, message: '请输入测试计划名称' }]}
          >
            <Input placeholder="例如: GPU驱动升级测试计划" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="测试计划描述"
            rules={[{ required: true, message: '请输入测试计划描述' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="描述这个测试计划的用途和功能..."
            />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
          >
            <Input placeholder="例如: performance, diagnostic, upgrade, test" />
          </Form.Item>
        </Form>
        
        {!editingCard && (
          <div style={{ marginTop: 16, padding: 12, background: '#f6f8fa', borderRadius: 6 }}>
            <div style={{ fontWeight: 'bold', marginBottom: 8 }}>默认包含的内容：</div>
            
            <div style={{ marginBottom: 12 }}>
              <div style={{ fontWeight: 'bold', fontSize: '13px', marginBottom: 4 }}>📁 文件字段：</div>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                {DEFAULT_FILE_FIELDS.map(field => (
                  <Tag key={field.id} size="small" color="orange">{field.name}</Tag>
                ))}
              </div>
            </div>
            
            <div>
              <div style={{ fontWeight: 'bold', fontSize: '13px', marginBottom: 4 }}>🚀 命令字段：</div>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                {DEFAULT_COMMAND_FIELDS.map(field => (
                  <Tag key={field.key} size="small" color="blue">{field.label}</Tag>
                ))}
              </div>
            </div>
            
            <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
              创建后可以在测试计划编辑器中自定义这些字段
            </div>
          </div>
        )}
      </Drawer>

      {/* 导出模式选择模态框 */}
      <Modal
        title="选择导出模式"
        visible={exportModalVisible}
        onCancel={() => setExportModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setExportModalVisible(false)}>
            取消
          </Button>,
          <Button 
            key="json" 
            onClick={() => executeExport(false)}
            loading={exporting}
          >
            📄 仅配置
          </Button>,
          <Button 
            key="zip" 
            type="primary" 
            onClick={() => executeExport(true)}
            loading={exporting}
          >
            📦 完整包
          </Button>
        ]}
        width={600}
      >
        <div style={{ marginBottom: 20 }}>
          <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 16 }}>
            🚀 即将导出 {selectedCards.length} 个测试计划
          </div>
          
          <div style={{ display: 'flex', gap: 16 }}>
            {/* JSON模式 */}
            <div style={{ 
              flex: 1, 
              padding: 16, 
              border: '2px solid #f0f0f0', 
              borderRadius: 8,
              cursor: 'pointer',
              transition: 'all 0.3s'
            }}
            onClick={() => setExportMode('json')}
            onMouseEnter={(e) => {
              e.target.style.borderColor = '#1890ff';
              e.target.style.backgroundColor = '#f0f9ff';
            }}
            onMouseLeave={(e) => {
              e.target.style.borderColor = exportMode === 'json' ? '#1890ff' : '#f0f0f0';
              e.target.style.backgroundColor = exportMode === 'json' ? '#f0f9ff' : 'transparent';
            }}
            style={{
              ...{
                flex: 1, 
                padding: 16, 
                border: '2px solid #f0f0f0', 
                borderRadius: 8,
                cursor: 'pointer',
                transition: 'all 0.3s'
              },
              borderColor: exportMode === 'json' ? '#1890ff' : '#f0f0f0',
              backgroundColor: exportMode === 'json' ? '#f0f9ff' : 'transparent'
            }}
            >
              <div style={{ textAlign: 'center', marginBottom: 12 }}>
                <span style={{ fontSize: 32 }}>📄</span>
              </div>
              <div style={{ fontWeight: 'bold', marginBottom: 8 }}>仅配置文件</div>
              <div style={{ fontSize: 12, color: '#666', lineHeight: 1.4 }}>
                • 导出 JSON 格式配置文件<br/>
                • 包含测试计划的步骤和变量<br/>
                • 不包含关联的文件内容<br/>
                • 文件小，传输快速
              </div>
              <div style={{ marginTop: 12, padding: 8, background: '#e6f7ff', borderRadius: 4 }}>
                <div style={{ fontSize: 11, color: '#0369a1' }}>
                  💡 适用于：配置备份、跨环境迁移
                </div>
              </div>
            </div>

            {/* ZIP模式 */}
            <div style={{ 
              flex: 1, 
              padding: 16, 
              border: '2px solid #f0f0f0', 
              borderRadius: 8,
              cursor: 'pointer',
              transition: 'all 0.3s'
            }}
            onClick={() => setExportMode('zip')}
            onMouseEnter={(e) => {
              e.target.style.borderColor = '#52c41a';
              e.target.style.backgroundColor = '#f6ffed';
            }}
            onMouseLeave={(e) => {
              e.target.style.borderColor = exportMode === 'zip' ? '#52c41a' : '#f0f0f0';
              e.target.style.backgroundColor = exportMode === 'zip' ? '#f6ffed' : 'transparent';
            }}
            style={{
              ...{
                flex: 1, 
                padding: 16, 
                border: '2px solid #f0f0f0', 
                borderRadius: 8,
                cursor: 'pointer',
                transition: 'all 0.3s'
              },
              borderColor: exportMode === 'zip' ? '#52c41a' : '#f0f0f0',
              backgroundColor: exportMode === 'zip' ? '#f6ffed' : 'transparent'
            }}
            >
              <div style={{ textAlign: 'center', marginBottom: 12 }}>
                <span style={{ fontSize: 32 }}>📦</span>
              </div>
              <div style={{ fontWeight: 'bold', marginBottom: 8 }}>完整压缩包</div>
              <div style={{ fontSize: 12, color: '#666', lineHeight: 1.4 }}>
                • 导出 ZIP 格式压缩包<br/>
                • 包含配置文件和所有关联文件<br/>
                • 完整的测试计划内容<br/>
                • 即插即用，无需重新配置
              </div>
              <div style={{ marginTop: 12, padding: 8, background: '#f6ffed', borderRadius: 4 }}>
                <div style={{ fontSize: 11, color: '#389e0d' }}>
                  🎯 推荐：完整迁移、备份恢复
                </div>
              </div>
            </div>
          </div>

          <div style={{ marginTop: 16, padding: 12, background: '#fffbe6', border: '1px solid #ffe58f', borderRadius: 6 }}>
            <div style={{ color: '#d48806', fontSize: 13 }}>
              <strong>📋 说明：</strong>
              <ul style={{ margin: '4px 0 0 0', paddingLeft: '16px' }}>
                <li>仅配置模式：快速导出测试计划结构，需要手动处理文件依赖</li>
                <li>完整包模式：包含所有依赖文件，可直接在新环境导入使用</li>
              </ul>
            </div>
          </div>
        </div>
      </Modal>

      {/* 导入模态框 */}
      <Modal
        title="导入测试计划"
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
        width={500}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 12 }}>
            <span style={{ fontWeight: 'bold' }}>导入选项：</span>
          </div>
          <div style={{ marginBottom: 8 }}>
            <Checkbox
              checked={importOptions.updateExisting}
              onChange={(e) => setImportOptions(prev => ({
                ...prev,
                updateExisting: e.target.checked,
                skipExisting: e.target.checked ? false : prev.skipExisting
              }))}
            >
              更新已存在的同名测试计划
            </Checkbox>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Checkbox
              checked={importOptions.skipExisting}
              onChange={(e) => setImportOptions(prev => ({
                ...prev,
                skipExisting: e.target.checked,
                updateExisting: e.target.checked ? false : prev.updateExisting
              }))}
            >
              跳过已存在的同名测试计划
            </Checkbox>
          </div>
          <div style={{ color: '#666', fontSize: '12px', marginBottom: 16 }}>
            注意：如果两个选项都不选中，遇到同名计划时将报错
          </div>
        </div>
        
        <Upload.Dragger
          accept=".json,.zip"
          beforeUpload={handleImport}
          showUploadList={false}
          disabled={importing}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 JSON 格式的配置文件 或 ZIP 格式的完整包（含文件）
          </p>
          {importing && (
            <div style={{ marginTop: 16 }}>
              <Spin /> 正在导入...
            </div>
          )}
        </Upload.Dragger>
      </Modal>

      {/* SSH连接测试结果模态框 */}
      <Modal
        title="SSH连接测试结果"
        visible={sshTestModalVisible}
        onCancel={() => setSshTestModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setSshTestModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <Spin spinning={sshTesting}>
          {sshTestResults.length === 0 && !sshTesting ? (
            <Empty description="暂无测试结果" />
          ) : (
            <div>
              {sshTestResults.map((result, index) => (
                <div key={index} style={{ 
                  marginBottom: 16, 
                  padding: 12, 
                  border: '1px solid #f0f0f0', 
                  borderRadius: 6,
                  backgroundColor: result.status === 'success' ? '#f6ffed' : '#fff2f0'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <span style={{ 
                      fontSize: 16, 
                      color: result.status === 'success' ? '#52c41a' : '#ff4d4f' 
                    }}>
                      {result.status === 'success' ? '✅' : '❌'}
                    </span>
                    <span style={{ marginLeft: 8, fontWeight: 'bold' }}>
                      {result.host_name} ({result.hostname})
                    </span>
                  </div>
                  <div style={{ marginBottom: 4 }}>
                    <strong>状态：</strong> {result.message}
                  </div>
                  {result.suggestion && (
                    <div style={{ marginBottom: 4, color: '#fa8c16' }}>
                      <strong>建议：</strong> {result.suggestion}
                    </div>
                  )}
                  {result.details && (
                    <div style={{ 
                      fontSize: 12, 
                      color: '#666', 
                      backgroundColor: '#fafafa', 
                      padding: 8, 
                      borderRadius: 4,
                      marginTop: 8
                    }}>
                      <strong>详细信息：</strong> {result.details}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </Spin>
      </Modal>

      {/* 命令编辑器 - 已弃用，使用新的页面编辑器 */}
      {/* {editorVisible && (
        <CardEditor
          card={selectedCard}
          visible={editorVisible}
          onClose={() => setEditorVisible(false)}
          onSave={handleCommandsUpdated}
        />
      )} */}
    </div>
  );
}

export default TestPlans; 