# Generated by Django 2.2.28 on 2025-06-29 11:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0002_auto_20250628_1454'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='servermetrics',
            name='io_speed',
        ),
        migrations.RemoveField(
            model_name='servermetrics',
            name='network_speed',
        ),
        migrations.AddField(
            model_name='servermetrics',
            name='memory_usage',
            field=models.FloatField(default=0.0, verbose_name='内存使用率'),
        ),
        migrations.AddField(
            model_name='servermetrics',
            name='network_download',
            field=models.CharField(default='0MB', max_length=32, verbose_name='网络下载'),
        ),
        migrations.AddField(
            model_name='servermetrics',
            name='network_total',
            field=models.CharField(default='0MB', max_length=32, verbose_name='网络总计'),
        ),
        migrations.AddField(
            model_name='servermetrics',
            name='network_upload',
            field=models.<PERSON><PERSON><PERSON><PERSON>(default='0MB', max_length=32, verbose_name='网络上传'),
        ),
    ]
