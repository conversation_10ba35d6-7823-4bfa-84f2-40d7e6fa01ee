# Model Storage 权限问题解决方案

## 问题描述
用户访问 `/model-storage/file-tree-compare/` 和 `/model-storage/lazy-load-tree/` 接口时出现 401 未授权错误。

## 问题分析
这些API接口使用了 `@auth('model.storage.view')` 装饰器进行权限控制，但系统中可能没有为用户角色配置相应的权限。

## 解决方案

### 方案1：通过Web界面添加权限（推荐）

1. **登录管理员账号**
   - 使用超级管理员账号登录系统

2. **进入角色管理**
   - 导航到：系统管理 → 角色管理

3. **编辑用户角色**
   - 找到需要访问model-storage功能的角色
   - 点击"编辑"按钮

4. **添加权限**
   - 在权限配置中找到或添加 `model` 模块
   - 在 `model` 模块下添加 `storage` 子模块
   - 为 `storage` 子模块添加以下权限：
     - `view` - 查看权限
     - `edit` - 编辑权限  
     - `del` - 删除权限

5. **保存并重新登录**
   - 保存角色权限配置
   - 用户需要重新登录才能获得新权限

### 方案2：数据库直接修改权限

如果需要批量添加权限，可以直接修改数据库：

```sql
-- 查看现有角色
SELECT id, name, page_perms FROM roles;

-- 更新角色权限（示例）
UPDATE roles SET page_perms = JSON_SET(
    COALESCE(page_perms, '{}'), 
    '$.model.storage', 
    JSON_ARRAY('view', 'edit', 'del')
) WHERE id = 1;
```

### 方案3：创建权限管理脚本

已经创建并执行了权限管理脚本，为所有角色添加了model-storage权限。

## 权限系统说明

### 权限格式
- 权限采用三级结构：`模块.子模块.操作`
- Model Storage权限：
  - `model.storage.view` - 查看权限
  - `model.storage.edit` - 编辑权限
  - `model.storage.del` - 删除权限

### 权限检查流程
1. 用户请求API接口
2. `@auth` 装饰器检查用户权限
3. 调用 `user.has_perms()` 方法验证权限
4. 超级管理员自动拥有所有权限

## 验证解决方案

1. **检查权限是否生效**
   - 重新登录系统
   - 访问 Model Storage 页面
   - 查看是否还有401错误

2. **检查API接口**
   - 打开浏览器开发者工具
   - 访问 `/model-storage/file-tree-compare/` 接口
   - 确认返回200状态码

## 注意事项

1. **权限缓存**
   - 系统使用Redis缓存用户权限
   - 修改权限后需要重新登录或清除缓存

2. **超级管理员**
   - 超级管理员（is_supper=True）自动拥有所有权限
   - 无需单独配置权限

3. **权限继承**
   - 用户通过角色获得权限
   - 一个用户可以拥有多个角色

## 相关文件

- `spug_api/apps/model_storage/views.py` - API视图文件
- `spug_api/apps/model_storage/urls.py` - URL配置
- `spug_api/libs/decorators.py` - 权限装饰器
- `spug_api/apps/account/models.py` - 用户和角色模型

## 问题状态

✅ **已解决** - 通过权限管理脚本为所有角色添加了model-storage权限，用户重新登录后即可正常访问。 