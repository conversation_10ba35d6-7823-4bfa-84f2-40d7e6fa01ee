# Generated by Django 2.2.28 on 2025-06-11 14:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exec', '0002_testplan_testplanexecution_testplanstepresult'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='testplan',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='testplan',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='testplanexecution',
            name='user',
        ),
        migrations.AddField(
            model_name='testplan',
            name='created_by_id',
            field=models.IntegerField(default=1, verbose_name='创建人ID'),
        ),
        migrations.AddField(
            model_name='testplan',
            name='updated_by_id',
            field=models.IntegerField(null=True, verbose_name='更新人ID'),
        ),
        migrations.AddField(
            model_name='testplanexecution',
            name='user_id',
            field=models.IntegerField(null=True, verbose_name='执行用户ID'),
        ),
        migrations.AddField(
            model_name='testplanstepresult',
            name='assertion_config',
            field=models.TextField(default='{}', verbose_name='断言配置JSON'),
        ),
        migrations.AddField(
            model_name='testplanstepresult',
            name='assertion_enabled',
            field=models.BooleanField(default=False, verbose_name='是否启用断言'),
        ),
        migrations.AddField(
            model_name='testplanstepresult',
            name='assertion_message',
            field=models.TextField(default='', verbose_name='断言消息'),
        ),
        migrations.AddField(
            model_name='testplanstepresult',
            name='assertion_result',
            field=models.TextField(default='', verbose_name='断言结果详情'),
        ),
        migrations.AddField(
            model_name='testplanstepresult',
            name='assertion_status',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='断言状态'),
        ),
        migrations.AddField(
            model_name='testplanstepresult',
            name='assertion_type',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='断言类型'),
        ),
    ]
