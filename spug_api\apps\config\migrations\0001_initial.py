# Generated by Django 2.2.28 on 2025-06-04 15:41

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('key', models.CharField(max_length=50, unique=True)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'services',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='Environment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('key', models.CharField(max_length=50)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('sort_id', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'environments',
                'ordering': ('-sort_id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='ConfigHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(max_length=5)),
                ('o_id', models.IntegerField()),
                ('key', models.CharField(max_length=50)),
                ('env_id', models.IntegerField()),
                ('value', models.TextField(null=True)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('is_public', models.BooleanField()),
                ('old_value', models.TextField(null=True)),
                ('action', models.CharField(choices=[('1', '新增'), ('2', '更新'), ('3', '删除')], max_length=2)),
                ('updated_at', models.CharField(max_length=20)),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'config_histories',
                'ordering': ('key',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='Config',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('app', 'App'), ('src', 'Service')], max_length=5)),
                ('o_id', models.IntegerField()),
                ('key', models.CharField(max_length=50)),
                ('value', models.TextField(null=True)),
                ('desc', models.CharField(max_length=255, null=True)),
                ('is_public', models.BooleanField(default=False)),
                ('updated_at', models.CharField(max_length=20)),
                ('env', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='config.Environment')),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'configs',
                'ordering': ('-key',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
