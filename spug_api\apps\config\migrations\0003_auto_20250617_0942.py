# Generated by Django 2.2.28 on 2025-06-17 09:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0002_add_package_task_models'),
    ]

    operations = [
        migrations.AlterField(
            model_name='config',
            name='desc',
            field=models.Char<PERSON>ield(max_length=255, null=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='config',
            name='key',
            field=models.CharField(max_length=50, verbose_name='版本号'),
        ),
        migrations.AlterField(
            model_name='config',
            name='type',
            field=models.CharField(choices=[('app', 'App'), ('src', 'Service'), ('task', 'Task')], max_length=5),
        ),
        migrations.AlterField(
            model_name='config',
            name='value',
            field=models.TextField(null=True, verbose_name='文件路径'),
        ),
    ]
