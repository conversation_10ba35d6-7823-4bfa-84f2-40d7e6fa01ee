2025-06-28 12:30:39 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 361, in execute
    self.check()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 40, in check_url_namespaces_unique
    all_namespaces = _load_all_namespaces(resolver)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 57, in _load_all_namespaces
    url_patterns = getattr(resolver, 'url_patterns', [])
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 37, in <module>
    path('model-storage/', include('apps.model_storage.urls')),
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py", line 2, in <module>
    from .views import ServerMetricsView, FileTreeView, ReleasePlanView, SvnCompareView, CheckMissingView
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py", line 2, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
2025-06-28 12:31:08 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 381, in execute
    return Database.Cursor.execute(self, query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: r.result_data

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 232, in handle
    post_migrate_state = executor.migrate(
                         ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\executor.py", line 117, in migrate
    state = self._migrate_all_forwards(state, plan, full_plan, fake=fake, fake_initial=fake_initial)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\executor.py", line 147, in _migrate_all_forwards
    state = self.apply_migration(state, migration, fake=fake, fake_initial=fake_initial)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\executor.py", line 245, in apply_migration
    state = migration.apply(state, schema_editor)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\migration.py", line 124, in apply
    operation.database_forwards(self.app_label, schema_editor, old_state, project_state)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\operations\special.py", line 105, in database_forwards
    self._run_sql(schema_editor, self.sql)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\operations\special.py", line 130, in _run_sql
    schema_editor.execute(statement, params=None)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\base\schema.py", line 137, in execute
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 99, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 76, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\utils.py", line 89, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 381, in execute
    return Database.Cursor.execute(self, query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: r.result_data
2025-06-28 12:36:21 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 361, in execute
    self.check()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 37, in <module>
    path('model-storage/', include('apps.model_storage.urls')),
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\urls.py", line 2, in <module>
    from . import views
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py", line 11, in <module>
    from rest_framework.decorators import api_view
ModuleNotFoundError: No module named 'rest_framework'
2025-06-28 15:11:40 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 381, in execute
    return Database.Cursor.execute(self, query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: r.result_data

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 232, in handle
    post_migrate_state = executor.migrate(
                         ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\executor.py", line 117, in migrate
    state = self._migrate_all_forwards(state, plan, full_plan, fake=fake, fake_initial=fake_initial)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\executor.py", line 147, in _migrate_all_forwards
    state = self.apply_migration(state, migration, fake=fake, fake_initial=fake_initial)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\executor.py", line 245, in apply_migration
    state = migration.apply(state, schema_editor)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\migration.py", line 124, in apply
    operation.database_forwards(self.app_label, schema_editor, old_state, project_state)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\operations\special.py", line 105, in database_forwards
    self._run_sql(schema_editor, self.sql)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\operations\special.py", line 130, in _run_sql
    schema_editor.execute(statement, params=None)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\base\schema.py", line 137, in execute
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 99, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 76, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\utils.py", line 89, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 381, in execute
    return Database.Cursor.execute(self, query)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: r.result_data
