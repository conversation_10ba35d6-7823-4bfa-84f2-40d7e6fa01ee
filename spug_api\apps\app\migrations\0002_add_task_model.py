# Generated manually for task management
from django.db import migrations, models
import django.db.models.deletion
from libs import ModelMixin, human_datetime


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
        ('app', '0001_initial'),
    ]

    operations = [
        # 创建配置任务表
        migrations.CreateModel(
            name='ConfigTask',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='任务名称')),
                ('key', models.CharField(max_length=50, unique=True, verbose_name='任务标识')),
                ('desc', models.CharField(max_length=255, null=True, verbose_name='任务描述')),
                ('rel_packages', models.TextField(null=True, verbose_name='关联配套')),
                ('sort_id', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.CharField(default=human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'config_tasks',
                'ordering': ('-sort_id',),
            },
            bases=(models.Model, ModelMixin),
        ),
    ] 