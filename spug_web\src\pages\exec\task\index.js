/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect, useCallback } from 'react';
import { observer } from 'mobx-react';
import { PlusOutlined, ThunderboltOutlined, BulbOutlined, QuestionCircleOutlined, FileOutlined, CodeOutlined } from '@ant-design/icons';
import { Form, Button, Radio, Tooltip, Modal } from 'antd';
import { ACEditor, AuthDiv, Breadcrumb } from 'components';
import HostSelector from 'pages/host/Selector';
import TemplateSelector from './TemplateSelector';
import TestPlanSelector from './TestPlanSelector';
import Parameter from './Parameter';
import Output from './Output';
import TestPlanOutput from './TestPlanOutput';
import ExecutionHistory from './ExecutionHistory';
import { http, cleanCommand } from 'libs';
import moment from 'moment';
import store from './store';
import gStore from 'gStore';
import style from './index.module.less';

function TaskIndex() {
  const [loading, setLoading] = useState(false)
  const [interpreter, setInterpreter] = useState('sh')
  const [command, setCommand] = useState('')
  const [template_id, setTemplateId] = useState()
  const [histories, setHistories] = useState([])
  const [parameters, setParameters] = useState([])
  const [visible, setVisible] = useState(false)
  const [selectedTestPlan, setSelectedTestPlan] = useState(null)
  const [showHistory, setShowHistory] = useState(false)
  const [recentTestPlans, setRecentTestPlans] = useState([])
  const [showRecentPlans, setShowRecentPlans] = useState(false)



  useEffect(() => {
    if (!loading) {
      http.get('/api/exec/do/')
        .then(res => setHistories(res))
    }
  }, [loading])

  useEffect(() => {
    if (!command) {
      setParameters([])
    }
  }, [command])

  useEffect(() => {
    gStore.fetchUserSettings()
    
    // 加载最近选择的测试计划
    const recentPlans = JSON.parse(localStorage.getItem('recent_test_plans') || '[]')
    setRecentTestPlans(recentPlans)
    
    // 自动选择第一个主机（如果还没有选择主机的话）
    if (store.host_ids.length === 0) {
      import('pages/host/store').then(hostStore => {
        hostStore.default.initial().then(() => {
          if (hostStore.default.rawRecords.length > 0 && store.host_ids.length === 0) {
            store.host_ids = [hostStore.default.rawRecords[0].id]
          }
        })
      })
    }
    
    // 检查URL参数中是否有token，如果有则直接显示控制台
    const params = new URLSearchParams(window.location.search);
    const token = params.get('token');
    if (token) {
      store.switchConsole(token);
    }
    
    return () => {
      store.host_ids = []
      if (store.showConsole) {
        store.switchConsole()
      }
    }
  }, [])

  function handleSubmit(params) {
    if (!params && parameters.length > 0) {
      return setVisible(true)
    }
    setLoading(true)
    
    // 如果是测试计划，使用测试计划执行API
    if (selectedTestPlan) {
      const formData = {
        plan_id: selectedTestPlan.id,
        host_ids: store.host_ids,
        params: params || {}
      }
      http.post('/api/exec/test-plan-executions/', formData)
        .then(res => {
          // 兼容原有的控制台显示
          store.switchConsole(res.token || res.execution_id)
        })
        .catch(err => {
          console.error('测试计划执行失败:', err);
        })
        .finally(() => setLoading(false))
    } else {
      // 原有的命令执行逻辑
      const formData = {interpreter, template_id, params, host_ids: store.host_ids, command: cleanCommand(command)}
      http.post('/api/exec/do/', formData)
        .then(store.switchConsole)
        .finally(() => setLoading(false))
    }
  }

  function handleTemplate(tpl) {
    if (tpl.host_ids.length > 0) store.host_ids = tpl.host_ids
    setTemplateId(tpl.id)
    setInterpreter(tpl.interpreter)
    setCommand(tpl.body)
    setParameters(tpl.parameters)
    
    // 如果是测试计划
    if (tpl.isTestPlan) {
      setSelectedTestPlan(tpl.originalPlan)
      saveRecentTestPlan(tpl.originalPlan)
    } else {
      setSelectedTestPlan(null)
    }
  }

  // 保存最近选择的测试计划
  function saveRecentTestPlan(plan) {
    const recentPlans = JSON.parse(localStorage.getItem('recent_test_plans') || '[]')
    
    // 移除重复项
    const filteredPlans = recentPlans.filter(p => p.id !== plan.id)
    
    // 添加到开头，限制为最近10个
    const newRecentPlans = [
      { id: plan.id, name: plan.name, updated_at: new Date().toISOString() },
      ...filteredPlans
    ].slice(0, 10)
    
    localStorage.setItem('recent_test_plans', JSON.stringify(newRecentPlans))
    setRecentTestPlans(newRecentPlans)
  }

  // 快速选择最近的测试计划
  function handleRecentTestPlan(planInfo) {
    // 通过API获取完整的测试计划数据
    http.get(`/api/exec/test-plans/${planInfo.id}/`)
      .then(res => {
        const plan = res.data || res
        const tpl = {
          id: plan.id,
          host_ids: [],
          interpreter: 'sh',
          body: '', 
          parameters: [],
          isTestPlan: true,
          originalPlan: plan
        }
        handleTemplate(tpl)
        setShowRecentPlans(false)
      })
      .catch(err => {
        console.error('加载测试计划失败:', err)
        Modal.error({
          title: '加载失败',
          content: '无法加载该测试计划，可能已被删除。'
        })
      })
  }

  function clearTestPlan() {
    setSelectedTestPlan(null)
    setCommand('')
    setTemplateId(null)
  }

  function handleClick(item) {
    setTemplateId(item.template_id)
    setInterpreter(item.interpreter)
    setCommand(item.command)
    setParameters(item.parameters || [])
    store.host_ids = item.host_ids
    // 清除测试计划选择
    setSelectedTestPlan(null)
  }

  function handleCommandChange(value) {
    setCommand(value)
    // 如果手动编辑命令，清除测试计划选择
    if (selectedTestPlan) {
      setSelectedTestPlan(null)
    }
  }

  // 单独执行某个步骤
  function handleSingleStepExecution(step, stepIndex) {
    if (store.host_ids.length === 0) {
      Modal.warning({
        title: '请先选择目标主机',
        content: '请在上方选择要执行命令的目标主机。'
      });
      return;
    }

    Modal.confirm({
      title: '确认单独执行此步骤',
      content: (
        <div>
          <p><strong>步骤：</strong>{step.label || `步骤 ${stepIndex + 1}`}</p>
          <p><strong>执行路径：</strong>{step.workPath || '/tmp'}</p>
          <p><strong>命令：</strong></p>
          <pre style={{ 
            backgroundColor: '#f6f8fa', 
            padding: '8px', 
            borderRadius: '4px',
            fontSize: '12px',
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            {step.command}
          </pre>
          <p style={{ color: '#666', fontSize: '12px' }}>
            注意：单独执行不会传输文件，如果此步骤依赖文件，请确保文件已存在。变量将在后端自动替换。
          </p>
        </div>
      ),
      onOk() {
        setLoading(true);
        
        // 构造单步骤的临时测试计划
        const singleStepPlan = {
          id: selectedTestPlan.id,
          name: `${selectedTestPlan.name} - ${step.label || `步骤 ${stepIndex + 1}`}`,
          commands: [step] // 只包含当前步骤
        };
        
        const formData = {
          plan_id: selectedTestPlan.id,
          host_ids: store.host_ids,
          single_step: stepIndex, // 标记为单步执行
          step_data: step
        };
        
        http.post('/api/exec/test-plan-executions/', formData)
          .then(res => {
            store.switchConsole(res.token || res.execution_id);
          })
          .catch(err => {
            console.error('单步执行失败:', err);
            Modal.error({
              title: '执行失败',
              content: err.message || '单步执行失败，请查看错误信息。'
            });
          })
          .finally(() => setLoading(false));
      }
    });
  }

  return (
    <AuthDiv auth="exec.task.do">
      <Breadcrumb>
        <Breadcrumb.Item>首页</Breadcrumb.Item>
        <Breadcrumb.Item>批量执行</Breadcrumb.Item>
        <Breadcrumb.Item>执行任务</Breadcrumb.Item>
      </Breadcrumb>
      <div className={style.index} hidden={store.showConsole}>
        <Form layout="vertical" className={style.left}>
          <Form.Item required label="目标主机">
            <HostSelector type="button" value={store.host_ids} onChange={ids => store.host_ids = ids}/>
          </Form.Item>

          {selectedTestPlan ? (
            <Form.Item required label="测试计划步骤">
              <div style={{ marginBottom: 12, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <strong>{selectedTestPlan.name}</strong>
                  <span style={{ marginLeft: 8, color: '#666' }}>({selectedTestPlan.commands?.length || 0} 个步骤)</span>
                </div>
                <div>
                  <Button icon={<PlusOutlined/>} onClick={store.switchTestPlan} style={{ marginRight: 8 }}>重新选择测试计划</Button>
                  <Button onClick={clearTestPlan}>清除选择</Button>
                </div>
              </div>
              <div style={{ 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px', 
                maxHeight: '400px', 
                overflowY: 'auto',
                backgroundColor: '#fafafa'
              }}>
                {selectedTestPlan.commands?.map((step, index) => (
                  <div key={index} style={{ 
                    padding: '12px 16px', 
                    borderBottom: index < selectedTestPlan.commands.length - 1 ? '1px solid #e8e8e8' : 'none',
                    backgroundColor: step.enabled === false ? '#f5f5f5' : '#fff'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                      <span style={{ 
                        display: 'inline-block',
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        backgroundColor: step.enabled === false ? '#ccc' : '#1890ff',
                        color: '#fff',
                        textAlign: 'center',
                        lineHeight: '24px',
                        fontSize: '12px',
                        marginRight: '8px'
                      }}>
                        {index + 1}
                      </span>
                      <strong style={{ color: step.enabled === false ? '#999' : '#333' }}>
                        {step.label || `步骤 ${index + 1}`}
                      </strong>
                      {step.enabled === false && (
                        <span style={{ marginLeft: '8px', color: '#999', fontSize: '12px' }}>(已禁用)</span>
                      )}
                    </div>
                    
                                         {/* 显示关联文件 */}
                     {step.files && step.files.length > 0 && (
                       <div style={{ marginBottom: '8px' }}>
                         <div style={{ 
                           fontSize: '12px', 
                           color: '#666', 
                           marginBottom: '4px',
                           display: 'flex',
                           alignItems: 'center'
                         }}>
                           <FileOutlined style={{ marginRight: '4px' }} />
                           关联文件:
                         </div>
                         {step.files.map((file, fileIndex) => (
                           <div key={fileIndex} style={{ 
                             fontSize: '12px', 
                             color: '#666', 
                             marginLeft: '20px',
                             marginBottom: '2px',
                             padding: '2px 6px',
                             backgroundColor: '#e6f7ff',
                             borderRadius: '3px',
                             display: 'inline-block',
                             marginRight: '8px'
                           }}>
                             {file.name} → {file.targetPath}
                           </div>
                         ))}
                       </div>
                     )}
                     
                     {/* 显示执行路径 */}
                     {step.workPath && (
                       <div style={{ marginBottom: '8px' }}>
                         <div style={{ 
                           fontSize: '12px', 
                           color: '#666', 
                           marginBottom: '4px',
                           display: 'flex',
                           alignItems: 'center'
                         }}>
                           📂 执行路径:
                         </div>
                         <span style={{
                           padding: '2px 6px',
                           backgroundColor: '#fff2e8',
                           border: '1px solid #ffb366',
                           borderRadius: '3px',
                           fontSize: '12px',
                           color: '#d46b08',
                           fontFamily: 'monospace'
                         }}>
                           {step.workPath}
                         </span>
                       </div>
                     )}
                     
                     {/* 显示执行命令 */}
                     {step.command && (
                       <div>
                         <div style={{ 
                           fontSize: '12px', 
                           color: '#666', 
                           marginBottom: '4px',
                           display: 'flex',
                           alignItems: 'center',
                           justifyContent: 'space-between'
                         }}>
                           <span style={{ display: 'flex', alignItems: 'center' }}>
                             <CodeOutlined style={{ marginRight: '4px' }} />
                             执行命令:
                           </span>
                           <Button 
                             type="primary" 
                             size="small"
                             icon={<ThunderboltOutlined />}
                             disabled={step.enabled === false}
                             onClick={() => handleSingleStepExecution(step, index)}
                             style={{ fontSize: '11px', height: '24px', padding: '0 8px' }}
                           >
                             单独执行
                           </Button>
                         </div>
                         <pre style={{ 
                           margin: 0, 
                           padding: '8px', 
                           backgroundColor: '#f6f8fa', 
                           border: '1px solid #e1e4e8',
                           borderRadius: '3px',
                           fontSize: '12px',
                           color: step.enabled === false ? '#999' : '#333',
                           whiteSpace: 'pre-wrap',
                           wordBreak: 'break-all'
                         }}>
                           {step.command}
                         </pre>
                         
                         {selectedTestPlan?.variables && selectedTestPlan.variables.length > 0 && (
                           <div style={{ marginTop: '8px' }}>
                             <div style={{ 
                               padding: '8px', 
                               backgroundColor: '#f0f9ff', 
                               border: '1px solid #bae6fd', 
                               borderRadius: '4px'
                             }}>
                               <div style={{ 
                                 fontSize: '11px', 
                                 color: '#0369a1', 
                                 marginBottom: '6px',
                                 fontWeight: 'bold'
                               }}>
                                 🎯 变量配置 (执行时自动替换):
                               </div>
                               <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                                 {selectedTestPlan.variables.map((variable, varIndex) => (
                                   <div key={varIndex} style={{ 
                                     display: 'flex', 
                                     alignItems: 'center',
                                     fontSize: '11px',
                                     padding: '4px 8px',
                                     backgroundColor: '#fff',
                                     borderRadius: '3px',
                                     border: '1px solid #d1d5db'
                                   }}>
                                     <span style={{ 
                                       color: '#0369a1', 
                                       fontFamily: 'Monaco, Courier New, monospace',
                                       fontWeight: 'bold'
                                     }}>
                                       ${'${' + variable.name + '}'}
                                     </span>
                                     <span style={{ margin: '0 4px', color: '#6b7280' }}>→</span>
                                     <span style={{ 
                                       color: '#059669', 
                                       fontFamily: 'Monaco, Courier New, monospace',
                                       background: '#dcfce7',
                                       padding: '2px 4px',
                                       borderRadius: '2px'
                                     }}>
                                       {variable.value || '<未设置>'}
                                     </span>
                                   </div>
                                 ))}
                               </div>
                             </div>
                           </div>
                         )}
                       </div>
                     )}
                  </div>
                ))}
              </div>
            </Form.Item>
          ) : (
            <Form.Item required label="执行命令" style={{position: 'relative'}}>
              <Radio.Group
                buttonStyle="solid"
                style={{marginBottom: 12}}
                value={interpreter}
                onChange={e => setInterpreter(e.target.value)}>
                <Radio.Button value="sh" style={{width: 80, textAlign: 'center'}}>Shell</Radio.Button>
                <Radio.Button value="python" style={{width: 80, textAlign: 'center'}}>Python</Radio.Button>
              </Radio.Group>
              <a href="https://ops.spug.cc/docs/batch-exec" target="_blank" rel="noopener noreferrer"
                 className={style.tips}><BulbOutlined/> 使用全局变量？</a>
              <Button style={{float: 'right', marginRight: 8}} icon={<PlusOutlined/>} onClick={store.switchTemplate}>从执行模版中选择</Button>
              <div style={{float: 'right', display: 'flex', gap: '8px'}}>
                <Button icon={<PlusOutlined/>} onClick={store.switchTestPlan}>从测试计划中选择</Button>
                {recentTestPlans.length > 0 && (
                  <Button 
                    icon={<BulbOutlined/>} 
                    onClick={() => setShowRecentPlans(true)}
                    title="最近选择的测试计划"
                  >
                    最近选择
                  </Button>
                )}
              </div>
              <ACEditor className={style.editor} mode={interpreter} value={command} width="100%" onChange={handleCommandChange}/>
            </Form.Item>
          )}
          <Button loading={loading} icon={<ThunderboltOutlined/>} type="primary"
                  onClick={() => handleSubmit()}>开始执行</Button>
        </Form>

        <div className={style.right}>
          <div className={style.title}>
            执行记录
            <Tooltip title="多次相同的执行记录将会合并展示，每天自动清理，保留最近30条记录。">
              <QuestionCircleOutlined style={{color: '#999', marginLeft: 8}}/>
            </Tooltip>
            <Button 
              size="small" 
              style={{ float: 'right', marginTop: -2 }}
              onClick={() => setShowHistory(true)}
            >
              测试计划记录
            </Button>
          </div>
          <div className={style.inner}>
            {histories.map((item, index) => (
              <div key={index} className={style.item} onClick={() => handleClick(item)}>
                <div className={style[item.interpreter]}>{item.interpreter.substr(0, 2)}</div>
                <div className={style.number}>{item.host_ids.length}</div>
                {item.template_name ? (
                  <div className={style.tpl}>{item.template_name}</div>
                ) : (
                  <div className={style.command}>{item.command}</div>
                )}
                <div className={style.desc}>{moment(item.updated_at).format('MM.DD HH:mm')}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
      {store.showTemplate && <TemplateSelector onCancel={store.switchTemplate} onOk={handleTemplate}/>}
      {store.showTestPlan && <TestPlanSelector onCancel={store.switchTestPlan} onOk={handleTemplate}/>}
      {store.showConsole && (
        selectedTestPlan ? 
          <TestPlanOutput token={store.token} onBack={store.switchConsole}/> :
          <Output onBack={store.switchConsole}/>
      )}
      {visible && <Parameter parameters={parameters} onCancel={() => setVisible(false)} onOk={v => handleSubmit(v)}/>}
      {showHistory && (
        <Modal
          title="测试计划执行记录"
          visible={showHistory}
          onCancel={() => setShowHistory(false)}
          width="90%"
          style={{ top: 20 }}
          footer={null}
        >
          <ExecutionHistory />
        </Modal>
      )}
      {showRecentPlans && (
        <Modal
          title="最近选择的测试计划"
          visible={showRecentPlans}
          onCancel={() => setShowRecentPlans(false)}
          width={600}
          footer={null}
        >
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {recentTestPlans.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                暂无最近选择的测试计划
              </div>
            ) : (
              recentTestPlans.map((plan, index) => (
                <div 
                  key={plan.id} 
                  style={{ 
                    padding: '12px 16px', 
                    borderBottom: index < recentTestPlans.length - 1 ? '1px solid #e8e8e8' : 'none',
                    cursor: 'pointer',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                  onClick={() => handleRecentTestPlan(plan)}
                  onMouseEnter={e => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                  onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                >
                  <div>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                      {plan.name}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      选择时间: {moment(plan.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                  </div>
                  <div style={{ color: '#1890ff' }}>
                    点击选择 →
                  </div>
                </div>
              ))
            )}
          </div>
        </Modal>
      )}
    </AuthDiv>
  )
}

export default observer(TaskIndex)
