.metricsPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .panelHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  .dropZone {
    flex: 1;
    position: relative;
    transition: all 0.3s ease;
    border: 2px dashed transparent;
    border-radius: 6px;
    padding: 8px;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
    
    /* 确保滚动条可见 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a1a1a1;
      }
    }
    
    /* 确保Ant Design List组件不添加额外高度 */
    :global(.ant-list) {
      height: auto;
      max-height: none;
    }
    
    :global(.ant-list-items) {
      max-height: none;
    }
    
    &.dragOver {
      border-color: #1890ff;
      background: rgba(24, 144, 255, 0.05);
      
      .dropHint {
        display: flex;
      }
    }
    
    .dropHint {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: none;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: white;
      border: 2px solid #1890ff;
      border-radius: 8px;
      padding: 24px;
      color: #1890ff;
      font-size: 16px;
      font-weight: 500;
      z-index: 10;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
    }
  }

  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #999;
    text-align: center;
  }
  
  .metricsCard {
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: column;
    
    :global(.ant-card-body) {
      padding: 16px;
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }
  
  .panelTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 600;
    flex: 1;
    margin-right: 16px;
    
    .progressInfo {
      display: flex;
      align-items: center;
    }
  }
  
  .batchToolbar {
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 16px;
  }
  
  .metricItem {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #d9d9d9;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    &.selected {
      border-color: #1890ff;
      background: rgba(24, 144, 255, 0.05);
    }
    
    &.confirmed {
      background: rgba(82, 196, 26, 0.05);
      border-left: 3px solid #52c41a;
      padding-left: 12px;
    }
    
    :global(.ant-list-item-action) {
      margin-left: 16px;
    }
  }
  
  .metricHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
    
    .metricLabel {
      font-weight: 500;
      color: #262626;
    }
  }
  
  .metricContent {
    display: flex;
    align-items: flex-start;
    width: 100%;
    
    .metricInfo {
      flex: 1;
      
      .metricHeader {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .metricLabel {
          font-weight: 500;
          color: #262626;
          flex: 1;
        }
      }
      
      .metricValue {
        margin-bottom: 8px;
        
        .value {
          font-size: 18px;
          font-weight: 600;
          color: #1890ff;
          
          .unit {
            font-size: 14px;
            color: #666;
            margin-left: 6px;
            font-weight: normal;
          }
        }
      }
      
      .metricMeta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        margin-bottom: 4px;
        
        .confidence {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #666;
        }
      }
      
      .originalText {
        font-size: 11px;
        color: #999;
        margin-top: 4px;
        font-style: italic;
      }
    }
  }

  .editingItem {
    border: 2px solid #1890ff;
    border-radius: 6px;
    margin-bottom: 8px;
    
    .editForm {
      padding: 12px;
    }
  }
} 