2025-07-01 00:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63115]
2025-07-01 00:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63115]
2025-07-01 00:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63118]
2025-07-01 00:00:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63118]
2025-07-01 00:00:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:00:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:63138]
2025-07-01 00:00:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:63138]
2025-07-01 00:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63321]
2025-07-01 00:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63321]
2025-07-01 00:01:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:01:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63327]
2025-07-01 00:01:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63327]
2025-07-01 00:01:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:01:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:63334]
2025-07-01 00:01:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:63334]
2025-07-01 00:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63531]
2025-07-01 00:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63531]
2025-07-01 00:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:63539]
2025-07-01 00:02:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:63539]
2025-07-01 00:02:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:02:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63545]
2025-07-01 00:02:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63545]
2025-07-01 00:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:63718]
2025-07-01 00:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:63718]
2025-07-01 00:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:63725]
2025-07-01 00:03:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:63725]
2025-07-01 00:03:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:03:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63732]
2025-07-01 00:03:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63732]
2025-07-01 00:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:63891]
2025-07-01 00:04:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:63891]
2025-07-01 00:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63899]
2025-07-01 00:04:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63899]
2025-07-01 00:04:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:04:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63905]
2025-07-01 00:04:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63905]
2025-07-01 00:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64066]
2025-07-01 00:05:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64066]
2025-07-01 00:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:64082]
2025-07-01 00:05:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:64082]
2025-07-01 00:05:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:05:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:64101]
2025-07-01 00:05:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:64101]
2025-07-01 00:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.30, 127.0.0.1:64273]
2025-07-01 00:06:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.30, 127.0.0.1:64273]
2025-07-01 00:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:64290]
2025-07-01 00:06:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:64290]
2025-07-01 00:06:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:06:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:64297]
2025-07-01 00:06:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:64297]
2025-07-01 00:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:55281]
2025-07-01 00:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:55281]
2025-07-01 00:07:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:07:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:55295]
2025-07-01 00:07:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:55295]
2025-07-01 00:07:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:07:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:55304]
2025-07-01 00:07:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:55304]
2025-07-01 00:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:55466]
2025-07-01 00:08:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:55466]
2025-07-01 00:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:55480]
2025-07-01 00:08:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:55480]
2025-07-01 00:08:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:08:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:55491]
2025-07-01 00:08:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:55491]
2025-07-01 00:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:55672]
2025-07-01 00:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:55672]
2025-07-01 00:09:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:09:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.92, 127.0.0.1:55686]
2025-07-01 00:09:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.92, 127.0.0.1:55686]
2025-07-01 00:09:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:09:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:55694]
2025-07-01 00:09:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:55694]
2025-07-01 00:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:51912]
2025-07-01 00:10:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:51912]
2025-07-01 00:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.90, 127.0.0.1:51925]
2025-07-01 00:10:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.90, 127.0.0.1:51925]
2025-07-01 00:10:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:10:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.92, 127.0.0.1:51942]
2025-07-01 00:10:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.92, 127.0.0.1:51942]
2025-07-01 00:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:52176]
2025-07-01 00:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:52176]
2025-07-01 00:11:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:11:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:52180]
2025-07-01 00:11:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:52180]
2025-07-01 00:11:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:11:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:52198]
2025-07-01 00:11:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:52198]
2025-07-01 00:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:52399]
2025-07-01 00:12:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:52399]
2025-07-01 00:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:52416]
2025-07-01 00:12:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:52416]
2025-07-01 00:12:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:12:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:52428]
2025-07-01 00:12:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:52428]
2025-07-01 00:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52590]
2025-07-01 00:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52590]
2025-07-01 00:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:52594]
2025-07-01 00:13:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:52594]
2025-07-01 00:13:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:13:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:52611]
2025-07-01 00:13:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:52611]
2025-07-01 00:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.27, 127.0.0.1:52788]
2025-07-01 00:14:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.27, 127.0.0.1:52788]
2025-07-01 00:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:52791]
2025-07-01 00:14:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:52791]
2025-07-01 00:14:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:14:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:52808]
2025-07-01 00:14:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:52808]
2025-07-01 00:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52999]
2025-07-01 00:15:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52999]
2025-07-01 00:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53005]
2025-07-01 00:15:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53005]
2025-07-01 00:15:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:15:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53027]
2025-07-01 00:15:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53027]
2025-07-01 00:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:53213]
2025-07-01 00:16:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:53213]
2025-07-01 00:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:53220]
2025-07-01 00:16:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:53220]
2025-07-01 00:16:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:16:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53236]
2025-07-01 00:16:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53236]
2025-07-01 00:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57120]
2025-07-01 00:17:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57120]
2025-07-01 00:17:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:17:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:57125]
2025-07-01 00:17:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:57125]
2025-07-01 00:17:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:17:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:57141]
2025-07-01 00:17:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:57141]
2025-07-01 00:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:57294]
2025-07-01 00:18:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:57294]
2025-07-01 00:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:57299]
2025-07-01 00:18:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:57299]
2025-07-01 00:18:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:18:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57316]
2025-07-01 00:18:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57316]
2025-07-01 00:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49827]
2025-07-01 00:19:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49827]
2025-07-01 00:19:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:19:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:49834]
2025-07-01 00:19:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:49834]
2025-07-01 00:19:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:19:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:49853]
2025-07-01 00:19:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:49853]
2025-07-01 00:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:50004]
2025-07-01 00:20:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:50004]
2025-07-01 00:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:50013]
2025-07-01 00:20:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:50013]
2025-07-01 00:20:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:20:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50039]
2025-07-01 00:20:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50039]
2025-07-01 00:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:50262]
2025-07-01 00:21:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:50262]
2025-07-01 00:21:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:21:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:50270]
2025-07-01 00:21:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:50270]
2025-07-01 00:21:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:21:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.93, 127.0.0.1:50279]
2025-07-01 00:21:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.93, 127.0.0.1:50279]
2025-07-01 00:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:50466]
2025-07-01 00:22:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:50466]
2025-07-01 00:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:50474]
2025-07-01 00:22:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:50474]
2025-07-01 00:22:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:22:57 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:50483]
2025-07-01 00:22:58 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:50483]
2025-07-01 00:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:50653]
2025-07-01 00:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:50653]
2025-07-01 00:23:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:23:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.94, 127.0.0.1:50657]
2025-07-01 00:23:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.94, 127.0.0.1:50657]
2025-07-01 00:23:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:23:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50667]
2025-07-01 00:23:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50667]
WebSocket DISCONNECT /ws/notify/ [127.0.0.1:62987]
2025-07-01 00:24:36 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:62987]
2025-07-01 00:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:50868]
2025-07-01 00:24:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:50868]
2025-07-01 00:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:50871]
2025-07-01 00:24:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:50871]
2025-07-01 00:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:51060]
2025-07-01 00:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:51060]
2025-07-01 00:25:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:25:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.93, 127.0.0.1:51065]
2025-07-01 00:25:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.93, 127.0.0.1:51065]
2025-07-01 00:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:51317]
2025-07-01 00:26:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:51317]
2025-07-01 00:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:51326]
2025-07-01 00:26:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:51326]
WebSocket DISCONNECT /ws/notify/ [127.0.0.1:62974]
2025-07-01 00:27:20 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:62974]
2025-07-01 00:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:51514]
2025-07-01 00:27:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:51514]
2025-07-01 00:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:51700]
2025-07-01 00:28:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:51700]
2025-07-01 00:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:51877]
2025-07-01 00:29:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:51877]
2025-07-01 00:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:52072]
2025-07-01 00:30:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:52072]
2025-07-01 00:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.27, 127.0.0.1:52268]
2025-07-01 00:31:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.27, 127.0.0.1:52268]
2025-07-01 00:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:55510]
2025-07-01 00:32:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:55510]
2025-07-01 00:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55691]
2025-07-01 00:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55691]
2025-07-01 00:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55881]
2025-07-01 00:34:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55881]
2025-07-01 00:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:51263]
2025-07-01 00:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:51263]
2025-07-01 00:36:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:36:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:51488]
2025-07-01 00:36:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:51488]
2025-07-01 00:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:51652]
2025-07-01 00:37:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:51652]
2025-07-01 00:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:51826]
2025-07-01 00:38:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:51826]
2025-07-01 00:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51995]
2025-07-01 00:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51995]
2025-07-01 00:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:52179]
2025-07-01 00:40:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:52179]
2025-07-01 00:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:52430]
2025-07-01 00:41:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:52430]
2025-07-01 00:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:58501]
2025-07-01 00:42:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:58501]
2025-07-01 00:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:58719]
2025-07-01 00:43:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:58719]
2025-07-01 00:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:62830]
2025-07-01 00:44:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:62830]
2025-07-01 00:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:63041]
2025-07-01 00:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:63041]
2025-07-01 00:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:63249]
2025-07-01 00:46:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:63249]
2025-07-01 00:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63430]
2025-07-01 00:47:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63430]
2025-07-01 00:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:63599]
2025-07-01 00:48:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:63599]
2025-07-01 00:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:63775]
2025-07-01 00:49:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:63775]
2025-07-01 00:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63951]
2025-07-01 00:50:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63951]
2025-07-01 00:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:64144]
2025-07-01 00:51:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:64144]
2025-07-01 00:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:64328]
2025-07-01 00:52:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:64328]
2025-07-01 00:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:64499]
2025-07-01 00:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:64499]
2025-07-01 00:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.34, 127.0.0.1:64677]
2025-07-01 00:54:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.34, 127.0.0.1:64677]
2025-07-01 00:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:64883]
2025-07-01 00:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:64883]
2025-07-01 00:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:65142]
2025-07-01 00:56:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:65142]
2025-07-01 00:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:59005]
2025-07-01 00:57:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:59005]
2025-07-01 00:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:59188]
2025-07-01 00:58:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:59188]
2025-07-01 00:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 00:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:59363]
2025-07-01 00:59:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:59363]
2025-07-01 01:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55601]
2025-07-01 01:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55601]
2025-07-01 01:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:55803]
2025-07-01 01:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:55803]
2025-07-01 01:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:56006]
2025-07-01 01:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:56006]
2025-07-01 01:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:56181]
2025-07-01 01:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:56181]
2025-07-01 01:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:56350]
2025-07-01 01:04:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:56350]
2025-07-01 01:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:56522]
2025-07-01 01:05:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:56522]
2025-07-01 01:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:56719]
2025-07-01 01:06:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:56719]
2025-07-01 01:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:56876]
2025-07-01 01:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:56876]
2025-07-01 01:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:57055]
2025-07-01 01:08:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:57055]
2025-07-01 01:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57215]
2025-07-01 01:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57215]
2025-07-01 01:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57392]
2025-07-01 01:10:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57392]
2025-07-01 01:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57623]
2025-07-01 01:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57623]
2025-07-01 01:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:57829]
2025-07-01 01:12:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:57829]
2025-07-01 01:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:58028]
2025-07-01 01:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:58028]
2025-07-01 01:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:58213]
2025-07-01 01:14:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:58213]
2025-07-01 01:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:58428]
2025-07-01 01:15:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:58428]
2025-07-01 01:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:58655]
2025-07-01 01:16:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:58655]
2025-07-01 01:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:58831]
2025-07-01 01:17:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:58831]
2025-07-01 01:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:59007]
2025-07-01 01:18:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:59007]
2025-07-01 01:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:59184]
2025-07-01 01:19:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:59184]
2025-07-01 01:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:59348]
2025-07-01 01:20:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:59348]
2025-07-01 01:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:59520]
2025-07-01 01:21:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:59520]
2025-07-01 01:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:59698]
2025-07-01 01:22:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:59698]
2025-07-01 01:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:59915]
2025-07-01 01:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:59915]
2025-07-01 01:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:60116]
2025-07-01 01:24:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:60116]
2025-07-01 01:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:60321]
2025-07-01 01:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:60321]
2025-07-01 01:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:60565]
2025-07-01 01:26:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:60565]
2025-07-01 01:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:60729]
2025-07-01 01:27:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:60729]
2025-07-01 01:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:60908]
2025-07-01 01:28:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:60908]
2025-07-01 01:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:61107]
2025-07-01 01:29:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:61107]
2025-07-01 01:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:61294]
2025-07-01 01:30:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:61294]
2025-07-01 01:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:61490]
2025-07-01 01:31:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:61490]
2025-07-01 01:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61682]
2025-07-01 01:32:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61682]
2025-07-01 01:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:61866]
2025-07-01 01:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:61866]
2025-07-01 01:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:62049]
2025-07-01 01:34:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:62049]
2025-07-01 01:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:62246]
2025-07-01 01:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:62246]
2025-07-01 01:36:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:36:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:62428]
2025-07-01 01:36:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:62428]
2025-07-01 01:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:62603]
2025-07-01 01:37:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:62603]
2025-07-01 01:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:62784]
2025-07-01 01:38:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:62784]
2025-07-01 01:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:62945]
2025-07-01 01:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:62945]
2025-07-01 01:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:63131]
2025-07-01 01:40:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:63131]
2025-07-01 01:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:63363]
2025-07-01 01:41:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:63363]
2025-07-01 01:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63582]
2025-07-01 01:42:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63582]
2025-07-01 01:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63767]
2025-07-01 01:43:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63767]
2025-07-01 01:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63947]
2025-07-01 01:44:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63947]
2025-07-01 01:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:64137]
2025-07-01 01:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:64137]
2025-07-01 01:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:64326]
2025-07-01 01:46:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:64326]
2025-07-01 01:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:64513]
2025-07-01 01:47:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:64513]
2025-07-01 01:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:64682]
2025-07-01 01:48:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:64682]
2025-07-01 01:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:64864]
2025-07-01 01:49:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:64864]
2025-07-01 01:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:65037]
2025-07-01 01:50:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:65037]
2025-07-01 01:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65205]
2025-07-01 01:51:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65205]
2025-07-01 01:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:65395]
2025-07-01 01:52:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:65395]
2025-07-01 01:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:49199]
2025-07-01 01:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:49199]
2025-07-01 01:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:49363]
2025-07-01 01:54:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:49363]
2025-07-01 01:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.29, 127.0.0.1:49557]
2025-07-01 01:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.29, 127.0.0.1:49557]
2025-07-01 01:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:49811]
2025-07-01 01:56:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:49811]
2025-07-01 01:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:49993]
2025-07-01 01:57:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:49993]
2025-07-01 01:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:50167]
2025-07-01 01:58:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:50167]
2025-07-01 01:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 01:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50325]
2025-07-01 01:59:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50325]
2025-07-01 02:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:50507]
2025-07-01 02:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:50507]
2025-07-01 02:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:50697]
2025-07-01 02:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:50697]
2025-07-01 02:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:50888]
2025-07-01 02:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:50888]
2025-07-01 02:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:51059]
2025-07-01 02:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:51059]
2025-07-01 02:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:51244]
2025-07-01 02:04:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:51244]
2025-07-01 02:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:51429]
2025-07-01 02:05:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:51429]
2025-07-01 02:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:56983]
2025-07-01 02:06:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:56983]
2025-07-01 02:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57167]
2025-07-01 02:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57167]
2025-07-01 02:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:49832]
2025-07-01 02:08:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:49832]
2025-07-01 02:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:49994]
2025-07-01 02:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:49994]
2025-07-01 02:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:50189]
2025-07-01 02:10:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:50189]
2025-07-01 02:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:50436]
2025-07-01 02:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:50436]
2025-07-01 02:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50646]
2025-07-01 02:12:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50646]
2025-07-01 02:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50832]
2025-07-01 02:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50832]
2025-07-01 02:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:51013]
2025-07-01 02:14:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:51013]
2025-07-01 02:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:51208]
2025-07-01 02:15:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:51208]
2025-07-01 02:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:51390]
2025-07-01 02:16:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:51390]
2025-07-01 02:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:51574]
2025-07-01 02:17:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:51574]
2025-07-01 02:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:51743]
2025-07-01 02:18:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:51743]
2025-07-01 02:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:51920]
2025-07-01 02:19:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:51920]
2025-07-01 02:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:52084]
2025-07-01 02:20:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:52084]
2025-07-01 02:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:52269]
2025-07-01 02:21:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:52269]
2025-07-01 02:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:52449]
2025-07-01 02:22:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:52449]
2025-07-01 02:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:52632]
2025-07-01 02:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:52632]
2025-07-01 02:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:52803]
2025-07-01 02:24:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:52803]
2025-07-01 02:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:52992]
2025-07-01 02:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:52992]
2025-07-01 02:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:53237]
2025-07-01 02:26:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:53237]
2025-07-01 02:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:53431]
2025-07-01 02:27:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:53431]
2025-07-01 02:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:53611]
2025-07-01 02:28:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:53611]
2025-07-01 02:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:53819]
2025-07-01 02:29:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:53819]
2025-07-01 02:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:54004]
2025-07-01 02:30:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:54004]
2025-07-01 02:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:54189]
2025-07-01 02:31:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:54189]
2025-07-01 02:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:59151]
2025-07-01 02:32:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:59151]
2025-07-01 02:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:59333]
2025-07-01 02:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:59333]
2025-07-01 02:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63258]
2025-07-01 02:34:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63258]
2025-07-01 02:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:63446]
2025-07-01 02:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:63446]
2025-07-01 02:36:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:36:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63627]
2025-07-01 02:36:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63627]
2025-07-01 02:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:63801]
2025-07-01 02:37:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:63801]
2025-07-01 02:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:63974]
2025-07-01 02:38:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:63974]
2025-07-01 02:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:64142]
2025-07-01 02:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:64142]
2025-07-01 02:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:64312]
2025-07-01 02:40:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:64312]
2025-07-01 02:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:64545]
2025-07-01 02:41:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:64545]
2025-07-01 02:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64773]
2025-07-01 02:42:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:64773]
2025-07-01 02:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:64965]
2025-07-01 02:43:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:64965]
2025-07-01 02:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:65132]
2025-07-01 02:44:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:65132]
2025-07-01 02:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65324]
2025-07-01 02:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65324]
2025-07-01 02:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:65510]
2025-07-01 02:46:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:65510]
2025-07-01 02:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:49307]
2025-07-01 02:47:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:49307]
2025-07-01 02:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:49472]
2025-07-01 02:48:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:49472]
2025-07-01 02:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:49644]
2025-07-01 02:49:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:49644]
2025-07-01 02:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:49819]
2025-07-01 02:50:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:49819]
2025-07-01 02:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50000]
2025-07-01 02:51:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50000]
2025-07-01 02:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50206]
2025-07-01 02:52:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50206]
2025-07-01 02:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:50387]
2025-07-01 02:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:50387]
2025-07-01 02:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50563]
2025-07-01 02:54:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50563]
2025-07-01 02:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:50757]
2025-07-01 02:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:50757]
2025-07-01 02:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:50999]
2025-07-01 02:56:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:50999]
2025-07-01 02:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:51191]
2025-07-01 02:57:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:51191]
2025-07-01 02:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:51364]
2025-07-01 02:58:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:51364]
2025-07-01 02:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 02:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:51533]
2025-07-01 02:59:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:51533]
2025-07-01 03:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:51706]
2025-07-01 03:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:51706]
2025-07-01 03:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:51895]
2025-07-01 03:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:51895]
2025-07-01 03:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:52096]
2025-07-01 03:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:52096]
2025-07-01 03:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52276]
2025-07-01 03:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52276]
2025-07-01 03:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:52440]
2025-07-01 03:04:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:52440]
2025-07-01 03:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52605]
2025-07-01 03:05:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52605]
2025-07-01 03:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:52788]
2025-07-01 03:06:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:52788]
2025-07-01 03:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:52955]
2025-07-01 03:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:52955]
2025-07-01 03:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53145]
2025-07-01 03:08:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53145]
2025-07-01 03:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:53335]
2025-07-01 03:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:53335]
2025-07-01 03:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:53509]
2025-07-01 03:10:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:53509]
2025-07-01 03:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53723]
2025-07-01 03:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:53723]
2025-07-01 03:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53956]
2025-07-01 03:12:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53956]
2025-07-01 03:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:54143]
2025-07-01 03:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:54143]
2025-07-01 03:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:65340]
2025-07-01 03:14:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:65340]
2025-07-01 03:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:49156]
2025-07-01 03:15:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:49156]
2025-07-01 03:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51155]
2025-07-01 03:16:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51155]
2025-07-01 03:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:51336]
2025-07-01 03:17:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:51336]
2025-07-01 03:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:51506]
2025-07-01 03:18:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:51506]
2025-07-01 03:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51675]
2025-07-01 03:19:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51675]
2025-07-01 03:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:51848]
2025-07-01 03:20:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:51848]
2025-07-01 03:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:52008]
2025-07-01 03:21:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:52008]
2025-07-01 03:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:52201]
2025-07-01 03:22:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:52201]
2025-07-01 03:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:52397]
2025-07-01 03:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:52397]
2025-07-01 03:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:52562]
2025-07-01 03:24:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:52562]
2025-07-01 03:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:52762]
2025-07-01 03:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:52762]
2025-07-01 03:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:53010]
2025-07-01 03:26:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:53010]
2025-07-01 03:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:53202]
2025-07-01 03:27:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:53202]
2025-07-01 03:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:53396]
2025-07-01 03:28:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:53396]
2025-07-01 03:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51571]
2025-07-01 03:29:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51571]
2025-07-01 03:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:51754]
2025-07-01 03:30:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:51754]
2025-07-01 03:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51947]
2025-07-01 03:31:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51947]
2025-07-01 03:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:56763]
2025-07-01 03:32:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:56763]
2025-07-01 03:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56954]
2025-07-01 03:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56954]
2025-07-01 03:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57127]
2025-07-01 03:34:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57127]
2025-07-01 03:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57312]
2025-07-01 03:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57312]
2025-07-01 03:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [2.19, 127.0.0.1:57499]
2025-07-01 03:36:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [2.19, 127.0.0.1:57499]
2025-07-01 03:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:57668]
2025-07-01 03:37:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:57668]
2025-07-01 03:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57838]
2025-07-01 03:38:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:57838]
2025-07-01 03:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63018]
2025-07-01 03:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63018]
2025-07-01 03:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:63216]
2025-07-01 03:40:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:63216]
2025-07-01 03:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:56659]
2025-07-01 03:41:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:56659]
2025-07-01 03:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56881]
2025-07-01 03:42:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56881]
2025-07-01 03:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57086]
2025-07-01 03:43:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57086]
2025-07-01 03:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:57248]
2025-07-01 03:44:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:57248]
2025-07-01 03:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57444]
2025-07-01 03:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57444]
2025-07-01 03:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:46:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:57630]
2025-07-01 03:46:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:57630]
2025-07-01 03:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:57827]
2025-07-01 03:47:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:57827]
2025-07-01 03:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:57989]
2025-07-01 03:48:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:57989]
2025-07-01 03:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:58147]
2025-07-01 03:49:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:58147]
2025-07-01 03:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:58325]
2025-07-01 03:50:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:58325]
2025-07-01 03:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:58525]
2025-07-01 03:51:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:58525]
2025-07-01 03:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.34, 127.0.0.1:58735]
2025-07-01 03:52:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.34, 127.0.0.1:58735]
2025-07-01 03:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:58915]
2025-07-01 03:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:58915]
2025-07-01 03:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:59361]
2025-07-01 03:54:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:59361]
2025-07-01 03:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59571]
2025-07-01 03:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59571]
2025-07-01 03:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:56:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:59818]
2025-07-01 03:56:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:59818]
2025-07-01 03:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:60353]
2025-07-01 03:57:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:60353]
2025-07-01 03:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:60526]
2025-07-01 03:58:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:60526]
2025-07-01 03:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 03:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:60683]
2025-07-01 03:59:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:60683]
2025-07-01 04:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:00:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:60855]
2025-07-01 04:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:60855]
2025-07-01 04:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:61092]
2025-07-01 04:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:61092]
2025-07-01 04:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:02:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61292]
2025-07-01 04:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61292]
2025-07-01 04:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:61461]
2025-07-01 04:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:61461]
2025-07-01 04:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:04:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:61637]
2025-07-01 04:04:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:61637]
2025-07-01 04:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61807]
2025-07-01 04:05:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:61807]
2025-07-01 04:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:06:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.32, 127.0.0.1:61994]
2025-07-01 04:06:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.32, 127.0.0.1:61994]
2025-07-01 04:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62178]
2025-07-01 04:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62178]
2025-07-01 04:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:08:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:62345]
2025-07-01 04:08:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:62345]
2025-07-01 04:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:62493]
2025-07-01 04:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:62493]
2025-07-01 04:10:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:62685]
2025-07-01 04:10:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:62685]
2025-07-01 04:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:62922]
2025-07-01 04:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:62922]
2025-07-01 04:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:12:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:63130]
2025-07-01 04:12:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:63130]
2025-07-01 04:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:63311]
2025-07-01 04:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:63311]
2025-07-01 04:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:14:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:63498]
2025-07-01 04:14:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:63498]
2025-07-01 04:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:63696]
2025-07-01 04:15:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:63696]
2025-07-01 04:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:16:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63891]
2025-07-01 04:16:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63891]
2025-07-01 04:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:64077]
2025-07-01 04:17:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:64077]
2025-07-01 04:18:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:64247]
2025-07-01 04:18:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:64247]
2025-07-01 04:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:64409]
2025-07-01 04:19:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:64409]
2025-07-01 04:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:20:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63139]
2025-07-01 04:20:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63139]
2025-07-01 04:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63319]
2025-07-01 04:21:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63319]
2025-07-01 04:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:22:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:63510]
2025-07-01 04:22:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:63510]
2025-07-01 04:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:54034]
2025-07-01 04:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:54034]
2025-07-01 04:24:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:54196]
2025-07-01 04:24:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:54196]
2025-07-01 04:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:54394]
2025-07-01 04:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:54394]
2025-07-01 04:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:26:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:54653]
2025-07-01 04:26:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:54653]
2025-07-01 04:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:54835]
2025-07-01 04:27:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:54835]
2025-07-01 04:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:28:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:55030]
2025-07-01 04:28:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:55030]
2025-07-01 04:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55190]
2025-07-01 04:29:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:55190]
2025-07-01 04:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:30:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:60010]
2025-07-01 04:30:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:60010]
2025-07-01 04:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:60193]
2025-07-01 04:31:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:60193]
2025-07-01 04:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:32:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53547]
2025-07-01 04:32:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:53547]
2025-07-01 04:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:53730]
2025-07-01 04:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:53730]
2025-07-01 04:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:34:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53946]
2025-07-01 04:34:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53946]
2025-07-01 04:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:54139]
2025-07-01 04:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:54139]
2025-07-01 04:36:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:54326]
2025-07-01 04:36:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:54326]
2025-07-01 04:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:54505]
2025-07-01 04:37:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:54505]
2025-07-01 04:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:38:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:54681]
2025-07-01 04:38:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:54681]
2025-07-01 04:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:54860]
2025-07-01 04:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:54860]
2025-07-01 04:40:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:55062]
2025-07-01 04:40:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:55062]
2025-07-01 04:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:55297]
2025-07-01 04:41:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:55297]
2025-07-01 04:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:42:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:55490]
2025-07-01 04:42:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:55490]
2025-07-01 04:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55681]
2025-07-01 04:43:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55681]
2025-07-01 04:44:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:55849]
2025-07-01 04:44:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:55849]
2025-07-01 04:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56037]
2025-07-01 04:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56037]
2025-07-01 04:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56229]
2025-07-01 04:46:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56229]
2025-07-01 04:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:56422]
2025-07-01 04:47:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:56422]
2025-07-01 04:48:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:56592]
2025-07-01 04:48:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:56592]
2025-07-01 04:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:56767]
2025-07-01 04:49:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:56767]
2025-07-01 04:50:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56929]
2025-07-01 04:50:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:56929]
2025-07-01 04:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:57112]
2025-07-01 04:51:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:57112]
2025-07-01 04:52:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:57292]
2025-07-01 04:52:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:57292]
2025-07-01 04:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:57496]
2025-07-01 04:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:57496]
2025-07-01 04:54:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:57655]
2025-07-01 04:54:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:57655]
2025-07-01 04:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:57847]
2025-07-01 04:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:57847]
2025-07-01 04:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:58098]
2025-07-01 04:56:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:58098]
2025-07-01 04:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:58294]
2025-07-01 04:57:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:58294]
2025-07-01 04:58:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:58493]
2025-07-01 04:58:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:58493]
2025-07-01 04:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 04:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:58698]
2025-07-01 04:59:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:58698]
2025-07-01 05:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:58879]
2025-07-01 05:00:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:58879]
2025-07-01 05:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:59080]
2025-07-01 05:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:59080]
2025-07-01 05:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:59277]
2025-07-01 05:02:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:59277]
2025-07-01 05:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:59469]
2025-07-01 05:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:59469]
2025-07-01 05:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:59638]
2025-07-01 05:04:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:59638]
2025-07-01 05:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59813]
2025-07-01 05:05:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:59813]
2025-07-01 05:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:60024]
2025-07-01 05:06:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:60024]
2025-07-01 05:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:60172]
2025-07-01 05:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:60172]
2025-07-01 05:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:60308]
2025-07-01 05:08:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:60308]
2025-07-01 05:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60448]
2025-07-01 05:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60448]
2025-07-01 05:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:60622]
2025-07-01 05:10:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:60622]
2025-07-01 05:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:60847]
2025-07-01 05:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:60847]
2025-07-01 05:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.94, 127.0.0.1:59585]
2025-07-01 05:12:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.94, 127.0.0.1:59585]
2025-07-01 05:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:59747]
2025-07-01 05:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:59747]
2025-07-01 05:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:62430]
2025-07-01 05:14:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:62430]
2025-07-01 05:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:62624]
2025-07-01 05:15:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:62624]
2025-07-01 05:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:62796]
2025-07-01 05:16:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:62796]
2025-07-01 05:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62958]
2025-07-01 05:17:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62958]
2025-07-01 05:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63113]
2025-07-01 05:18:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:63113]
2025-07-01 05:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63257]
2025-07-01 05:19:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:63257]
2025-07-01 05:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63407]
2025-07-01 05:20:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:63407]
2025-07-01 05:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63555]
2025-07-01 05:21:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63555]
2025-07-01 05:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:63718]
2025-07-01 05:22:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:63718]
2025-07-01 05:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63876]
2025-07-01 05:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63876]
2025-07-01 05:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:64017]
2025-07-01 05:24:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:64017]
2025-07-01 05:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52842]
2025-07-01 05:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52842]
2025-07-01 05:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53067]
2025-07-01 05:26:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53067]
2025-07-01 05:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:53225]
2025-07-01 05:27:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:53225]
2025-07-01 05:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:53385]
2025-07-01 05:28:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:53385]
2025-07-01 05:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:53518]
2025-07-01 05:29:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:53518]
2025-07-01 05:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:53665]
2025-07-01 05:30:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:53665]
2025-07-01 05:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53871]
2025-07-01 05:31:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53871]
2025-07-01 05:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:54048]
2025-07-01 05:32:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:54048]
2025-07-01 05:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:54206]
2025-07-01 05:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:54206]
2025-07-01 05:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:54347]
2025-07-01 05:34:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:54347]
2025-07-01 05:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:54511]
2025-07-01 05:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:54511]
2025-07-01 05:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:54697]
2025-07-01 05:36:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:54697]
2025-07-01 05:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:54843]
2025-07-01 05:37:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:54843]
2025-07-01 05:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:54983]
2025-07-01 05:38:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:54983]
2025-07-01 05:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:55116]
2025-07-01 05:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:55116]
2025-07-01 05:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:55272]
2025-07-01 05:40:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:55272]
2025-07-01 05:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:55490]
2025-07-01 05:41:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:55490]
2025-07-01 05:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:55686]
2025-07-01 05:42:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:55686]
2025-07-01 05:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55852]
2025-07-01 05:43:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55852]
2025-07-01 05:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55993]
2025-07-01 05:44:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55993]
2025-07-01 05:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56157]
2025-07-01 05:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:56157]
2025-07-01 05:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:56334]
2025-07-01 05:46:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:56334]
2025-07-01 05:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:47:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.33, 127.0.0.1:56499]
2025-07-01 05:47:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.33, 127.0.0.1:56499]
2025-07-01 05:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:56641]
2025-07-01 05:48:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:56641]
2025-07-01 05:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:56780]
2025-07-01 05:49:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:56780]
2025-07-01 05:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:56929]
2025-07-01 05:50:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:56929]
2025-07-01 05:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:57090]
2025-07-01 05:51:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:57090]
2025-07-01 05:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:57246]
2025-07-01 05:52:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:57246]
2025-07-01 05:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57402]
2025-07-01 05:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57402]
2025-07-01 05:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50527]
2025-07-01 05:54:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50527]
2025-07-01 05:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:50695]
2025-07-01 05:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:50695]
2025-07-01 05:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63260]
2025-07-01 05:56:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:63260]
2025-07-01 05:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63478]
2025-07-01 05:57:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63478]
2025-07-01 05:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:63627]
2025-07-01 05:58:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:63627]
2025-07-01 05:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 05:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:63767]
2025-07-01 05:59:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:63767]
2025-07-01 06:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:63915]
2025-07-01 06:00:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:63915]
2025-07-01 06:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:64083]
2025-07-01 06:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:64083]
2025-07-01 06:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:64257]
2025-07-01 06:02:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:64257]
2025-07-01 06:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:03:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:64401]
2025-07-01 06:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:64401]
2025-07-01 06:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:64543]
2025-07-01 06:04:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:64543]
2025-07-01 06:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [2.04, 127.0.0.1:64688]
2025-07-01 06:05:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [2.04, 127.0.0.1:64688]
2025-07-01 06:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:64864]
2025-07-01 06:06:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:64864]
2025-07-01 06:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:65005]
2025-07-01 06:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:65005]
2025-07-01 06:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65174]
2025-07-01 06:08:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:65174]
2025-07-01 06:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:53997]
2025-07-01 06:09:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:53997]
2025-07-01 06:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:54153]
2025-07-01 06:10:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:54153]
2025-07-01 06:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:54380]
2025-07-01 06:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:54380]
2025-07-01 06:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:54472]
2025-07-01 06:12:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:54472]
2025-07-01 06:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:13:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:54636]
2025-07-01 06:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:54636]
2025-07-01 06:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:54786]
2025-07-01 06:14:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:54786]
2025-07-01 06:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:15:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54956]
2025-07-01 06:15:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54956]
2025-07-01 06:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:55133]
2025-07-01 06:16:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:55133]
2025-07-01 06:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:55305]
2025-07-01 06:17:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:55305]
2025-07-01 06:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:55452]
2025-07-01 06:18:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:55452]
2025-07-01 06:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:64928]
2025-07-01 06:19:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:64928]
2025-07-01 06:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:65088]
2025-07-01 06:20:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:65088]
2025-07-01 06:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:21:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:61835]
2025-07-01 06:21:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:61835]
2025-07-01 06:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:62004]
2025-07-01 06:22:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:62004]
2025-07-01 06:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:62215]
2025-07-01 06:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:62215]
2025-07-01 06:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:62357]
2025-07-01 06:24:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:62357]
2025-07-01 06:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:62522]
2025-07-01 06:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:62522]
2025-07-01 06:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:62752]
2025-07-01 06:26:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:62752]
2025-07-01 06:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:27:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:62917]
2025-07-01 06:27:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:62917]
2025-07-01 06:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63079]
2025-07-01 06:28:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:63079]
2025-07-01 06:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:29:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63213]
2025-07-01 06:29:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63213]
2025-07-01 06:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:63360]
2025-07-01 06:30:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:63360]
2025-07-01 06:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:31:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63529]
2025-07-01 06:31:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63529]
2025-07-01 06:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63726]
2025-07-01 06:32:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63726]
2025-07-01 06:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:33:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:63884]
2025-07-01 06:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:63884]
2025-07-01 06:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:51547]
2025-07-01 06:34:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:51547]
2025-07-01 06:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:35:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:51704]
2025-07-01 06:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:51704]
2025-07-01 06:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:51889]
2025-07-01 06:36:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:51889]
2025-07-01 06:37:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:37:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:57054]
2025-07-01 06:37:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:57054]
2025-07-01 06:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:57221]
2025-07-01 06:38:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:57221]
2025-07-01 06:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:39:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57369]
2025-07-01 06:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57369]
2025-07-01 06:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57523]
2025-07-01 06:40:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:57523]
2025-07-01 06:41:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:41:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:57717]
2025-07-01 06:41:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.19, 127.0.0.1:57717]
2025-07-01 06:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:57891]
2025-07-01 06:42:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:57891]
2025-07-01 06:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:43:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:58047]
2025-07-01 06:43:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:58047]
2025-07-01 06:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58183]
2025-07-01 06:44:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58183]
2025-07-01 06:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:45:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58355]
2025-07-01 06:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:58355]
2025-07-01 06:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:58565]
2025-07-01 06:46:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:58565]
2025-07-01 06:47:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:47:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:58739]
2025-07-01 06:47:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:58739]
2025-07-01 06:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:58891]
2025-07-01 06:48:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:58891]
2025-07-01 06:49:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:49:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:59019]
2025-07-01 06:49:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.24, 127.0.0.1:59019]
2025-07-01 06:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:59184]
2025-07-01 06:50:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:59184]
2025-07-01 06:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:51:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:59338]
2025-07-01 06:51:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:59338]
2025-07-01 06:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:59501]
2025-07-01 06:52:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:59501]
2025-07-01 06:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:53:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:59665]
2025-07-01 06:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:59665]
2025-07-01 06:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:59801]
2025-07-01 06:54:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:59801]
2025-07-01 06:55:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:55:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:59981]
2025-07-01 06:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:59981]
2025-07-01 06:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:60197]
2025-07-01 06:56:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:60197]
2025-07-01 06:57:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:57:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:60376]
2025-07-01 06:57:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:60376]
2025-07-01 06:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:60526]
2025-07-01 06:58:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:60526]
2025-07-01 06:59:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 06:59:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:60661]
2025-07-01 06:59:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:60661]
2025-07-01 07:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.29, 127.0.0.1:60822]
2025-07-01 07:00:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.29, 127.0.0.1:60822]
2025-07-01 07:01:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:01:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:61003]
2025-07-01 07:01:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:61003]
2025-07-01 07:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:61185]
2025-07-01 07:02:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:61185]
2025-07-01 07:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:61339]
2025-07-01 07:03:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.26, 127.0.0.1:61339]
2025-07-01 07:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:61496]
2025-07-01 07:04:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:61496]
2025-07-01 07:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:05:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:61651]
2025-07-01 07:05:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:61651]
2025-07-01 07:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:61809]
2025-07-01 07:06:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:61809]
2025-07-01 07:07:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:07:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:61986]
2025-07-01 07:07:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:61986]
2025-07-01 07:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.40, 127.0.0.1:62152]
2025-07-01 07:08:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.40, 127.0.0.1:62152]
2025-07-01 07:09:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:09:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:62291]
2025-07-01 07:09:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:62291]
2025-07-01 07:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:62443]
2025-07-01 07:10:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:62443]
2025-07-01 07:11:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:11:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62655]
2025-07-01 07:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62655]
2025-07-01 07:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:62846]
2025-07-01 07:12:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:62846]
2025-07-01 07:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:63025]
2025-07-01 07:13:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:63025]
2025-07-01 07:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:63188]
2025-07-01 07:14:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:63188]
2025-07-01 07:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63357]
2025-07-01 07:15:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:63357]
2025-07-01 07:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:63519]
2025-07-01 07:16:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:63519]
2025-07-01 07:17:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:17:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63699]
2025-07-01 07:17:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:63699]
2025-07-01 07:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63851]
2025-07-01 07:18:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:63851]
2025-07-01 07:19:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:19:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:63990]
2025-07-01 07:19:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:63990]
2025-07-01 07:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:64146]
2025-07-01 07:20:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.08, 127.0.0.1:64146]
2025-07-01 07:21:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:21:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.28, 127.0.0.1:64313]
2025-07-01 07:21:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.28, 127.0.0.1:64313]
2025-07-01 07:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:64474]
2025-07-01 07:22:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:64474]
2025-07-01 07:23:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:23:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:64634]
2025-07-01 07:23:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:64634]
2025-07-01 07:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:64787]
2025-07-01 07:24:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:64787]
2025-07-01 07:25:54 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:25:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:64944]
2025-07-01 07:25:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:64944]
2025-07-01 07:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:65163]
2025-07-01 07:26:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:65163]
2025-07-01 07:27:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:27:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:65331]
2025-07-01 07:27:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:65331]
2025-07-01 07:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:65489]
2025-07-01 07:28:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:65489]
2025-07-01 07:29:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:29:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:49251]
2025-07-01 07:29:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:49251]
2025-07-01 07:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:49403]
2025-07-01 07:30:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:49403]
2025-07-01 07:31:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:31:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:49566]
2025-07-01 07:31:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.17, 127.0.0.1:49566]
2025-07-01 07:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:49760]
2025-07-01 07:32:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:49760]
2025-07-01 07:33:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:33:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:49928]
2025-07-01 07:33:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:49928]
2025-07-01 07:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50095]
2025-07-01 07:34:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:50095]
2025-07-01 07:35:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:35:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:50266]
2025-07-01 07:35:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:50266]
2025-07-01 07:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50421]
2025-07-01 07:36:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50421]
2025-07-01 07:37:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:37:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:50571]
2025-07-01 07:37:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:50571]
2025-07-01 07:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:50716]
2025-07-01 07:38:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:50716]
2025-07-01 07:39:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:39:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50856]
2025-07-01 07:39:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:50856]
2025-07-01 07:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:51025]
2025-07-01 07:40:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.14, 127.0.0.1:51025]
2025-07-01 07:41:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:41:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51251]
2025-07-01 07:41:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:51251]
2025-07-01 07:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:51438]
2025-07-01 07:42:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:51438]
2025-07-01 07:43:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:43:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:49598]
2025-07-01 07:43:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:49598]
2025-07-01 07:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:49757]
2025-07-01 07:44:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:49757]
2025-07-01 07:45:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:45:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:59351]
2025-07-01 07:45:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:59351]
2025-07-01 07:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59527]
2025-07-01 07:46:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:59527]
2025-07-01 07:47:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:47:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:59691]
2025-07-01 07:47:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:59691]
2025-07-01 07:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:59880]
2025-07-01 07:48:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:59880]
2025-07-01 07:49:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:49:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60013]
2025-07-01 07:49:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:60013]
2025-07-01 07:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:60160]
2025-07-01 07:50:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:60160]
2025-07-01 07:51:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:51:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:60307]
2025-07-01 07:51:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.21, 127.0.0.1:60307]
2025-07-01 07:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:60469]
2025-07-01 07:52:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:60469]
2025-07-01 07:53:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:53:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:60655]
2025-07-01 07:53:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:60655]
2025-07-01 07:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:60811]
2025-07-01 07:54:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:60811]
2025-07-01 07:55:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:55:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:61027]
2025-07-01 07:55:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:61027]
2025-07-01 07:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:61247]
2025-07-01 07:56:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:61247]
2025-07-01 07:57:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:57:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:61403]
2025-07-01 07:57:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:61403]
2025-07-01 07:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61543]
2025-07-01 07:58:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:61543]
2025-07-01 07:59:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 07:59:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:61693]
2025-07-01 07:59:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:61693]
2025-07-01 08:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:61844]
2025-07-01 08:00:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:61844]
2025-07-01 08:01:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:01:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:62000]
2025-07-01 08:01:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:62000]
2025-07-01 08:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62208]
2025-07-01 08:02:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:62208]
2025-07-01 08:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:62370]
2025-07-01 08:03:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:62370]
2025-07-01 08:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.94, 127.0.0.1:62508]
2025-07-01 08:04:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.94, 127.0.0.1:62508]
2025-07-01 08:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:62664]
2025-07-01 08:05:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:62664]
2025-07-01 08:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:62823]
2025-07-01 08:06:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:62823]
2025-07-01 08:07:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:07:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:62985]
2025-07-01 08:07:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:62985]
2025-07-01 08:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:63133]
2025-07-01 08:08:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:63133]
2025-07-01 08:09:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:09:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:58691]
2025-07-01 08:09:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:58691]
2025-07-01 08:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:58853]
2025-07-01 08:10:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.25, 127.0.0.1:58853]
2025-07-01 08:11:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:11:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:52099]
2025-07-01 08:11:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:52099]
2025-07-01 08:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:52292]
2025-07-01 08:12:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.12, 127.0.0.1:52292]
2025-07-01 08:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:52466]
2025-07-01 08:13:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:52466]
2025-07-01 08:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:52631]
2025-07-01 08:14:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:52631]
2025-07-01 08:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:52797]
2025-07-01 08:15:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:52797]
2025-07-01 08:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52966]
2025-07-01 08:16:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.10, 127.0.0.1:52966]
2025-07-01 08:17:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:17:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53158]
2025-07-01 08:17:55 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53158]
2025-07-01 08:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53308]
2025-07-01 08:18:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53308]
2025-07-01 08:19:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:19:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:53454]
2025-07-01 08:19:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:53454]
2025-07-01 08:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:20:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:53601]
2025-07-01 08:20:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.95, 127.0.0.1:53601]
2025-07-01 08:21:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:21:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53779]
2025-07-01 08:21:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53779]
2025-07-01 08:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:22:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53967]
2025-07-01 08:22:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:53967]
2025-07-01 08:23:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:23:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54132]
2025-07-01 08:23:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:54132]
2025-07-01 08:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:24:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53126]
2025-07-01 08:24:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53126]
2025-07-01 08:25:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:25:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:53309]
2025-07-01 08:25:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:53309]
2025-07-01 08:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:26:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53540]
2025-07-01 08:26:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:53540]
2025-07-01 08:27:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:27:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:49380]
2025-07-01 08:27:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.99, 127.0.0.1:49380]
2025-07-01 08:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:28:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49520]
2025-07-01 08:28:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:49520]
2025-07-01 08:29:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:29:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49669]
2025-07-01 08:29:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:49669]
2025-07-01 08:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:30:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:49841]
2025-07-01 08:30:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:49841]
2025-07-01 08:31:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:31:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:50006]
2025-07-01 08:31:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:50006]
2025-07-01 08:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:32:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50215]
2025-07-01 08:32:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50215]
2025-07-01 08:33:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:33:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50367]
2025-07-01 08:33:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:50367]
2025-07-01 08:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:34:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50512]
2025-07-01 08:34:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:50512]
2025-07-01 08:35:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:35:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:50684]
2025-07-01 08:35:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.23, 127.0.0.1:50684]
2025-07-01 08:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:36:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:50841]
2025-07-01 08:36:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:50841]
2025-07-01 08:37:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:37:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51036]
2025-07-01 08:37:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:51036]
2025-07-01 08:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:38:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:51188]
2025-07-01 08:38:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:51188]
2025-07-01 08:39:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:39:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51327]
2025-07-01 08:39:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:51327]
2025-07-01 08:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:40:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:51487]
2025-07-01 08:40:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:51487]
2025-07-01 08:41:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:41:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:51702]
2025-07-01 08:41:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.16, 127.0.0.1:51702]
2025-07-01 08:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:42:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:51888]
2025-07-01 08:42:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:51888]
2025-07-01 08:43:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:43:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52051]
2025-07-01 08:43:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:52051]
2025-07-01 08:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:44:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:52188]
2025-07-01 08:44:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:52188]
2025-07-01 08:45:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:45:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:52361]
2025-07-01 08:45:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:52361]
2025-07-01 08:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:46:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:52527]
2025-07-01 08:46:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:52527]
2025-07-01 08:47:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:47:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:52697]
2025-07-01 08:47:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:52697]
2025-07-01 08:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:48:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:52839]
2025-07-01 08:48:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:52839]
2025-07-01 08:49:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:49:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:52972]
2025-07-01 08:49:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:52972]
2025-07-01 08:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:50:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:53139]
2025-07-01 08:50:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:53139]
2025-07-01 08:51:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:51:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:53307]
2025-07-01 08:51:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.20, 127.0.0.1:53307]
2025-07-01 08:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:52:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:53481]
2025-07-01 08:52:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:53481]
2025-07-01 08:53:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:53:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:53625]
2025-07-01 08:53:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:53625]
2025-07-01 08:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:54:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:53809]
2025-07-01 08:54:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:53809]
2025-07-01 08:55:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:55:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53986]
2025-07-01 08:55:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:53986]
2025-07-01 08:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:56:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:54213]
2025-07-01 08:56:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.98, 127.0.0.1:54213]
2025-07-01 08:57:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:57:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:54382]
2025-07-01 08:57:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:54382]
2025-07-01 08:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:58:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:54535]
2025-07-01 08:58:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:54535]
2025-07-01 08:59:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 08:59:56 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [2.01, 127.0.0.1:54681]
2025-07-01 08:59:57 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [2.01, 127.0.0.1:54681]
2025-07-01 09:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:00:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:54842]
2025-07-01 09:00:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.11, 127.0.0.1:54842]
2025-07-01 09:01:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:01:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:55004]
2025-07-01 09:01:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.09, 127.0.0.1:55004]
2025-07-01 09:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:02:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:55199]
2025-07-01 09:02:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:55199]
2025-07-01 09:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:03:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55336]
2025-07-01 09:03:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.00, 127.0.0.1:55336]
2025-07-01 09:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:04:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:55480]
2025-07-01 09:04:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.96, 127.0.0.1:55480]
2025-07-01 09:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:05:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:55636]
2025-07-01 09:05:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:55636]
2025-07-01 09:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:06:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:55809]
2025-07-01 09:06:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.22, 127.0.0.1:55809]
2025-07-01 09:07:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:07:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:55956]
2025-07-01 09:07:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:55956]
2025-07-01 09:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:08:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:56104]
2025-07-01 09:08:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [0.97, 127.0.0.1:56104]
2025-07-01 09:09:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:09:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:56255]
2025-07-01 09:09:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.02, 127.0.0.1:56255]
2025-07-01 09:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:10:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:56427]
2025-07-01 09:10:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.13, 127.0.0.1:56427]
2025-07-01 09:11:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:11:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:56639]
2025-07-01 09:11:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.06, 127.0.0.1:56639]
2025-07-01 09:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:12:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:56805]
2025-07-01 09:12:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.07, 127.0.0.1:56805]
2025-07-01 09:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:13:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:56969]
2025-07-01 09:13:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.15, 127.0.0.1:56969]
2025-07-01 09:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:14:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57133]
2025-07-01 09:14:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.04, 127.0.0.1:57133]
2025-07-01 09:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:15:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:57325]
2025-07-01 09:15:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.01, 127.0.0.1:57325]
WebSocket DISCONNECT /ws/notify/ [127.0.0.1:63045]
2025-07-01 09:16:41 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:160" - log_action - INFO - WebSocket DISCONNECT /ws/notify/ [127.0.0.1:63045]
HTTP GET /notify/ 200 [0.03, 127.0.0.1:57654]
2025-07-01 09:16:42 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /notify/ 200 [0.03, 127.0.0.1:57654]
2025-07-01 09:16:42 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:16:42 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57657]
2025-07-01 09:16:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:162" - log_action - INFO - WebSocket HANDSHAKING /ws/notify/ [127.0.0.1:57657]
WebSocket CONNECT /ws/notify/ [127.0.0.1:57657]
2025-07-01 09:16:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:158" - log_action - INFO - WebSocket CONNECT /ws/notify/ [127.0.0.1:57657]
HTTP GET /model-storage/lazy-load-tree/?path=%2FHDD_Raid%2FSVN_MODEL_REPO&root=true 200 [0.80, 127.0.0.1:57655]
2025-07-01 09:16:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/lazy-load-tree/?path=%2FHDD_Raid%2FSVN_MODEL_REPO&root=true 200 [0.80, 127.0.0.1:57655]
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2229" - get_remote_root_directories - INFO - 正在连接远程URL: http://10.63.30.93/GPU_MODEL_REPO/01.DEV/
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2230" - get_remote_root_directories - INFO - 使用认证用户: sys49169
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2238" - get_remote_root_directories - INFO - HTTP响应状态码: 200
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2239" - get_remote_root_directories - INFO - 响应内容长度: 311 字符
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2243" - get_remote_root_directories - INFO - HTML内容预览: <html><head><title>GPU_MODEL_REPO - Revision 372: /01.DEV</title></head>\n<body>\n <h2>GPU_MODEL_REPO - Revision 372: /01.DEV</h2>\n <ul>\n  <li><a href="../">..</a></li>\n  <li><a href="DataSet/">DataSet/</a></li>\n  <li><a href="Model/">Model/</a></li>\n  <li><a href="Vendor/">Vendor/</a></li>\n </ul>\n</body></html>
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2676" - parse_remote_html - INFO - 找到 4 个链接
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2718" - parse_remote_html - INFO - 解析HTML成功，找到 3 个有效项目
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2722" - parse_remote_html - INFO - 找到的目录: ['DataSet', 'Model', 'Vendor']
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2249" - get_remote_root_directories - INFO - 解析结果: 总共 3 个项目，其中 3 个文件夹
2025-07-01 09:16:43 - model_storage - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\model_storage\views.py:2251" - get_remote_root_directories - INFO - 文件夹名称: ['DataSet', 'Model', 'Vendor']
HTTP GET /model-storage/file-tree-compare/?first_level=true 200 [0.17, 127.0.0.1:57675]
2025-07-01 09:16:43 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/file-tree-compare/?first_level=true 200 [0.17, 127.0.0.1:57675]
2025-07-01 09:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:16:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57722]
2025-07-01 09:16:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.03, 127.0.0.1:57722]
HTTP GET /model-storage/path-compare/?path=%2FHDD_Raid%2FSVN_MODEL_REPO%2FVendor 200 [0.03, 127.0.0.1:57752]
2025-07-01 09:16:59 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/path-compare/?path=%2FHDD_Raid%2FSVN_MODEL_REPO%2FVendor 200 [0.03, 127.0.0.1:57752]
2025-07-01 09:17:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:17:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:57014]
2025-07-01 09:17:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.18, 127.0.0.1:57014]
2025-07-01 09:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Connected (version 2.0, client OpenSSH_8.2p1)
2025-07-01 09:18:55 - paramiko.transport - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:1874" - _log - INFO - Authentication (publickey) successful!
HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57323]
2025-07-01 09:18:56 - django.channels.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\channels\management\commands\runserver.py:141" - log_action - INFO - HTTP GET /model-storage/server-metrics/ 200 [1.05, 127.0.0.1:57323]
