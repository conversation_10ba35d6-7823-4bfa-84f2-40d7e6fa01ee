2025-06-24 14:54:28  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-24 14:54:28  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-24 14:54:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - 

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    
2025-06-24 14:54:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：   https://funboost.readthedocs.io/zh-cn/latest/  
2025-06-24 14:54:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - 14:54:28  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下  下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录  下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    

2025-06-24 14:54:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-24 14:54:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-24 14:54:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-24 14:54:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000022CE2D72510>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-24 14:54:29 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-24 14:54:29 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-24 14:54:29 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-24 14:54:29 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
测试计划 API 测试脚本
包含：创建、获取、更新、删除、变量系统、文件管理
============================================================
[14:54:31] INFO: GET http://localhost:8000/api/exec/test-plans/ -> 404
[14:54:31] ERROR: 错误响应: <!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <title>Page not found at /api/exec/test-plans/</title>
  <meta name="robots" content="NONE,NOARCHIVE">
  <style type="text/css">
    html * { padding:0; margin:0; }
    body * { padding:10px 20px; }
    body * * { padding:0; }
    body { font:small sans-serif; background:#eee; color:#000; }
    body>div { border-bottom:1px solid #ddd; }
    h1 { font-weight:normal; margin-bottom:.4em; }
    h1 span { font-size:60%; color:#666; font-weight:normal; }
    table { border:none; border-collapse: collapse; width:100%; }
    td, th { vertical-align:top; padding:2px 3px; }
    th { width:12em; text-align:right; color:#666; padding-right:.5em; }
    #info { background:#f6f6f6; }
    #info ol { margin: 0.5em 4em; }
    #info ol li { font-family: monospace; }
    #summary { background: #ffc; }
    #explanation { background:#eee; border-bottom: 0px none; }
  </style>
</head>
<body>
  <div id="summary">
    <h1>Page not found <span>(404)</span></h1>
    <table class="meta">
      <tr>
        <th>Request Method:</th>
        <td>GET</td>
      </tr>
      <tr>
        <th>Request URL:</th>
        <td>http://localhost:8000/api/exec/test-plans/</td>
      </tr>
      
    </table>
  </div>
  <div id="info">
    
      <p>
      Using the URLconf defined in <code>spug.urls</code>,
      Django tried these URL patterns, in this order:
      </p>
      <ol>
        
          <li>
            
                account/
                
            
          </li>
        
          <li>
            
                host/
                
            
          </li>
        
          <li>
            
                exec/
                
            
          </li>
        
          <li>
            
                schedule/
                
            
          </li>
        
          <li>
            
                monitor/
                
            
          </li>
        
          <li>
            
                alarm/
                
            
          </li>
        
          <li>
            
                setting/
                
            
          </li>
        
          <li>
            
                config/
                
            
          </li>
        
          <li>
            
                app/
                
            
          </li>
        
          <li>
            
                deploy/
                
            
          </li>
        
          <li>
            
                repository/
                
            
          </li>
        
          <li>
            
                home/
                
            
          </li>
        
          <li>
            
                notify/
                
            
          </li>
        
          <li>
            
                file/
                
            
          </li>
        
          <li>
            
                apis/
                
            
          </li>
        
      </ol>
      <p>
        
        The current path, <code>api/exec/test-plans/</code>, didn't match any of these.
      </p>
    
  </div>

  <div id="explanation">
    <p>
      You're seeing this error because you have <code>DEBUG = True</code> in
      your Django settings file. Change that to <code>False</code>, and Django
      will display a standard 404 page.
    </p>
  </div>
</body>
</html>

无法连接到API服务
   请确保服务器正在运行: python manage.py runserver 0.0.0.0:8000
