#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试文件操作功能
"""

import sys
import os
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_safe_json_operations():
    """测试安全的JSON文件操作"""
    print("测试安全的JSON文件操作...")
    
    try:
        from apps.model_storage.file_utils import safe_file_ops, FileOperationError
        
        # 测试数据
        test_data = {
            'timestamp': time.time(),
            'test_value': 'Hello, World!',
            'numbers': [1, 2, 3, 4, 5],
            'nested': {
                'key1': 'value1',
                'key2': 'value2'
            }
        }
        
        # 获取测试文件路径
        test_file = safe_file_ops.get_safe_file_path('test_data.json', 'unittest')
        print(f"测试文件: {test_file}")
        
        # 测试写入
        print("测试JSON写入...")
        safe_file_ops.safe_json_write(test_file, test_data, indent=2)
        print("✅ JSON写入成功")
        
        # 测试读取
        print("测试JSON读取...")
        loaded_data = safe_file_ops.safe_json_read(test_file)
        print("✅ JSON读取成功")
        
        # 验证数据一致性
        if loaded_data == test_data:
            print("✅ 数据一致性验证通过")
        else:
            print("❌ 数据一致性验证失败")
            return False
        
        # 测试默认值
        print("测试不存在文件的默认值...")
        non_existent_file = safe_file_ops.get_safe_file_path('non_existent.json', 'unittest')
        default_data = safe_file_ops.safe_json_read(non_existent_file, default={'default': True})
        if default_data == {'default': True}:
            print("✅ 默认值测试通过")
        else:
            print("❌ 默认值测试失败")
            return False
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ JSON操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_concurrent_file_access():
    """测试并发文件访问"""
    print("测试并发文件访问...")
    
    try:
        from apps.model_storage.file_utils import safe_file_ops
        
        test_file = safe_file_ops.get_safe_file_path('concurrent_test.json', 'unittest')
        results = []
        errors = []
        
        def write_worker(worker_id):
            """工作线程：写入数据"""
            try:
                data = {
                    'worker_id': worker_id,
                    'timestamp': time.time(),
                    'data': f'Data from worker {worker_id}'
                }
                safe_file_ops.safe_json_write(test_file, data)
                results.append(f"Worker {worker_id} write success")
            except Exception as e:
                errors.append(f"Worker {worker_id} write error: {e}")
        
        def read_worker(worker_id):
            """工作线程：读取数据"""
            try:
                # 等待一点时间确保有数据可读
                time.sleep(0.1)
                data = safe_file_ops.safe_json_read(test_file, default={})
                results.append(f"Worker {worker_id} read success: {data.get('worker_id', 'unknown')}")
            except Exception as e:
                errors.append(f"Worker {worker_id} read error: {e}")
        
        # 启动并发测试
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            
            # 启动写入线程
            for i in range(5):
                futures.append(executor.submit(write_worker, f"W{i}"))
            
            # 启动读取线程
            for i in range(5):
                futures.append(executor.submit(read_worker, f"R{i}"))
            
            # 等待所有任务完成
            for future in futures:
                future.result(timeout=10)
        
        print(f"并发操作结果: {len(results)} 成功, {len(errors)} 错误")
        
        if errors:
            print("错误详情:")
            for error in errors:
                print(f"  - {error}")
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        
        # 如果没有错误或错误很少，认为测试通过
        return len(errors) <= 1  # 允许少量错误（由于并发竞争）
        
    except Exception as e:
        print(f"❌ 并发访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_network_io_tracker():
    """测试网络IO跟踪器"""
    print("测试网络IO跟踪器...")
    
    try:
        from apps.model_storage.file_utils import network_io_tracker
        
        # 测试保存网络数据
        print("测试保存网络数据...")
        timestamp = time.time()
        success = network_io_tracker.save_network_data(1000, 2000, timestamp)
        if success:
            print("✅ 网络数据保存成功")
        else:
            print("❌ 网络数据保存失败")
            return False
        
        # 测试加载网络数据
        print("测试加载网络数据...")
        loaded_data = network_io_tracker.load_network_data()
        if loaded_data and loaded_data['bytes_sent'] == 1000:
            print("✅ 网络数据加载成功")
        else:
            print("❌ 网络数据加载失败")
            return False
        
        # 测试清理
        print("测试数据清理...")
        network_io_tracker.cleanup_old_data()
        print("✅ 数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络IO跟踪器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("文件操作功能测试")
    print("=" * 60)
    
    tests = [
        ("安全JSON操作", test_safe_json_operations),
        ("并发文件访问", test_concurrent_file_access),
        ("网络IO跟踪器", test_network_io_tracker),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
