import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Progress, Table, Button, Modal, Form, Input, DatePicker, Select, Tag, Space, message, Tree, Badge, Statistic, Spin, Switch, Alert, List, Typography, Divider, Radio } from 'antd';
import { 
  DatabaseOutlined, 
  FolderOutlined, 
  SyncOutlined, 
  DownloadOutlined, 
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ExportOutlined,
  FullscreenOutlined,
  FileOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,

  DiffOutlined,
  FileAddOutlined,
  FileExclamationOutlined,
  FileTextOutlined,
  MinusCircleOutlined,
  EyeOutlined,
  CodeOutlined,
  BranchesOutlined,
  AppstoreOutlined,
  UnorderedListOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import styles from './index.module.less';
import moment from 'moment';



const { Option } = Select;
const { Search } = Input;
const { Text } = Typography;

export default function ModelStorageMonitor() {
  const [serverMetrics, setServerMetrics] = useState({});
  const [fileTreeData, setFileTreeData] = useState([]);
  const [filteredTreeData, setFilteredTreeData] = useState([]);
  const [releasePlans, setReleasePlans] = useState([]);
  const [compareData, setCompareData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);


  const [autoRefresh, setAutoRefresh] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [, setMetricsHistory] = useState([]);
  const [diffModalVisible, setDiffModalVisible] = useState(false);
  const [currentDiffFile, setCurrentDiffFile] = useState(null);
  const [viewMode, setViewMode] = useState('tree'); // 'tree' or 'list'
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [lazyLoading, setLazyLoading] = useState(false);
  const [loadedNodes, setLoadedNodes] = useState(new Set());
  const [form] = Form.useForm();



  // 获取服务器指标
  const fetchServerMetrics = () => {
    http.get('/api/model-storage/server-metrics/')
      .then(res => {
        setServerMetrics(res);
        setMetricsHistory(prev => {
          const newHistory = [...prev, { ...res, time: moment().format('HH:mm:ss') }];
          return newHistory.slice(-20);
        });
      })
      .catch(err => console.error('获取服务器指标失败:', err));
  };

  // 获取文件树对比数据（优先使用真实远程数据）
  const fetchFileTreeData = () => {
    setLoading(true);
    
    // 优先使用新的文件树对比API（包含真实远程数据）
    http.get('/api/model-storage/file-tree-compare/')
      .then(res => {
        if (res && res.length > 0) {
          console.log('获取到真实远程数据:', res);
          setFileTreeData(res);
          setFilteredTreeData(res);
          const keys = getAllNodeKeys(res);
          setExpandedKeys(keys);
          setLoading(false);
          
          // 显示数据来源提示
          message.success('已获取远程仓库真实数据');
          return;
        }
        
        // 如果新API没有数据，使用懒加载API
        return http.get('/api/model-storage/lazy-load-tree/', {
          params: { path: '/HDD_Raid/SVN_MODEL_REPO', depth: 1 }
        });
      })
      .then(res => {
        if (res && res.children) {
          console.log('使用懒加载API数据');
          const rootData = res.children.map(child => ({
            ...child,
            data: {
              path: child.key,
              name: child.title,
              type: child.type,
              size: child.size,
              lastModified: child.lastModified,
              status: child.status || 'synced'
            }
          }));
          setFileTreeData(rootData);
          setFilteredTreeData(rootData);
          setLoading(false);
          
          message.info('使用本地数据（远程数据获取失败）');
        }
      })
      .catch(err => {
        console.error('获取文件树数据失败:', err);
        message.error('获取文件数据失败，请检查网络连接');
        setLoading(false);
      });
  };

  // 懒加载子节点
  const loadChildrenNodes = async (parentKey) => {
    if (loadedNodes.has(parentKey) || lazyLoading) {
      return;
    }

    setLazyLoading(true);
    setLoadedNodes(prev => new Set([...prev, parentKey]));

    try {
      const res = await http.get('/api/model-storage/lazy-load-tree/', {
        params: { path: parentKey, depth: 1 }
      });

      const childrenData = res.children.map(child => ({
        ...child,
        data: {
          path: child.key,
          name: child.title,
          type: child.type,
          size: child.size,
          lastModified: child.lastModified,
          status: child.status || 'synced'
        }
      }));

      // 更新树结构数据
      setFileTreeData(prevData => {
        const updateNode = (nodes) => {
          return nodes.map(node => {
            if (node.key === parentKey) {
              return { ...node, children: childrenData };
            } else if (node.children) {
              return { ...node, children: updateNode(node.children) };
            }
            return node;
          });
        };
        return updateNode(prevData);
      });

      setFilteredTreeData(prevData => {
        const updateNode = (nodes) => {
          return nodes.map(node => {
            if (node.key === parentKey) {
              return { ...node, children: childrenData };
            } else if (node.children) {
              return { ...node, children: updateNode(node.children) };
            }
            return node;
          });
        };
        return updateNode(prevData);
      });

    } catch (error) {
      console.error('加载子节点失败:', error);
      message.error('加载子目录失败');
    } finally {
      setLazyLoading(false);
    }
  };

  // 获取所有节点的key
  const getAllNodeKeys = (treeData) => {
    const keys = [];
    const traverse = (nodes) => {
      nodes.forEach(node => {
        keys.push(node.key);
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    traverse(treeData);
    return keys;
  };

  // 获取发布计划
  const fetchReleasePlans = () => {
    http.get('/api/model-storage/release-plans/')
      .then(res => setReleasePlans(res))
      .catch(err => console.error('获取发布计划失败:', err));
  };

  // 获取远程对比数据
  const fetchCompareData = () => {
    setLoading(true);
    http.get('/api/model-storage/svn-compare/')
      .then(res => {
        setCompareData(res);
        setLoading(false);
      })
      .catch(err => {
        console.error('获取对比数据失败:', err);
        setLoading(false);
      });
  };

  // 检查文件差异
  const checkFileDifferences = () => {
    setLoading(true);
    http.get('/api/model-storage/check-differences/')
      .then(res => {
        message.success(`差异检查完成，发现 ${res.total_differences} 个文件差异`);
        fetchFileTreeData();
        setLoading(false);
      })
      .catch(err => {
        console.error('检查文件差异失败:', err);
        setLoading(false);
      });
  };

  // 同步单个文件
  const syncSingleFile = (filePath) => {
    setLoading(true);
    http.post('/api/model-storage/sync-single-file/', { file_path: filePath })
      .then(() => {
        message.success(`文件 ${filePath} 同步完成`);
        fetchFileTreeData();
        setLoading(false);
      })
      .catch(err => {
        console.error('同步文件失败:', err);
        message.error('同步失败');
        setLoading(false);
      });
  };

  // 查看文件差异详情
  const viewFileDiff = (file) => {
    setCurrentDiffFile(file);
    setDiffModalVisible(true);
  };

  // 批量同步选中文件
  const syncSelectedFiles = () => {
    const modifiedFiles = getModifiedFilesFromTree(fileTreeData);
    
    if (modifiedFiles.length === 0) {
      message.info('没有需要同步的文件');
      return;
    }

    Modal.confirm({
      title: '批量同步确认',
      content: `确定要同步 ${modifiedFiles.length} 个文件吗？`,
      onOk: () => {
        setLoading(true);
        http.post('/api/model-storage/batch-sync/', { 
          files: modifiedFiles 
        })
          .then(() => {
            message.success('批量同步完成');
            fetchFileTreeData();
            setLoading(false);
          })
          .catch(err => {
            console.error('批量同步失败:', err);
            message.error('批量同步失败');
            setLoading(false);
          });
      }
    });
  };

  // 从树结构中提取需要同步的文件
  const getModifiedFilesFromTree = (treeData) => {
    const modifiedFiles = [];
    const traverse = (nodes) => {
      nodes.forEach(node => {
        if (node.data && ['modified', 'deleted', 'missing', 'conflict'].includes(node.data.status)) {
          modifiedFiles.push(node.data.path);
        }
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(treeData);
    return modifiedFiles;
  };

  // 将树结构转换为列表数据
  const getListDataFromTree = (treeData) => {
    const listData = [];
    const traverse = (nodes, level = 0) => {
      nodes.forEach(node => {
        if (node.data) {
          listData.push({
            ...node.data,
            level,
            key: node.key
          });
        }
        if (node.children) {
          traverse(node.children, level + 1);
        }
      });
    };
    traverse(treeData);
    return listData;
  };

  // 保存发布计划
  const handleSavePlan = (values) => {
    const data = {
      card_model: values.card_model,
      model_name: values.model_name,
      release_date: values.release_date.format('YYYY-MM-DD'),
      model_status: values.model_status || 'inProgress',
      vendor_status: values.vendor_status || 'inProgress',
      overall_status: values.overall_status || 'preparing'
    };

    if (editingPlan) {
      data.id = editingPlan.id;
    }

    const request = editingPlan 
      ? http.put('/api/model-storage/release-plans/', data)
      : http.post('/api/model-storage/release-plans/', data);

    request.then(() => {
      message.success(editingPlan ? '更新成功' : '创建成功');
      setModalVisible(false);
      setEditingPlan(null);
      form.resetFields();
      fetchReleasePlans();
    }).catch(err => {
      console.error('保存发布计划失败:', err);
      message.error('保存失败');
    });
  };

  // 删除发布计划
  const handleDeletePlan = (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个发布计划吗？',
      onOk: () => {
        http.delete('/api/model-storage/release-plans/', {
          data: { id }
        })
          .then(() => {
            message.success('删除成功');
            fetchReleasePlans();
          })
          .catch(err => {
            console.error('删除发布计划失败:', err);
            message.error('删除失败');
          });
      }
    });
  };

  // 编辑发布计划
  const handleEditPlan = (plan) => {
    setEditingPlan(plan);
    form.setFieldsValue({
      card_model: plan.card_model,
      model_name: plan.model_name,
      release_date: moment(plan.release_date),
      model_status: plan.model_status,
      vendor_status: plan.vendor_status,
      overall_status: plan.overall_status
    });
    setModalVisible(true);
  };

  // 刷新所有数据
  const refreshAllData = () => {
    setLoading(true);
    Promise.all([
      fetchServerMetrics(),
      fetchFileTreeData(),
      fetchReleasePlans(),
      fetchCompareData()
    ]).finally(() => {
      setLoading(false);
      message.success('数据刷新完成');
    });
  };

  // 搜索文件
  const handleSearch = (value) => {
    setSearchKeyword(value);
    filterTreeData(value, statusFilter);
  };

  // 状态过滤
  const handleStatusFilter = (status) => {
    setStatusFilter(status);
    filterTreeData(searchKeyword, status);
  };

  // 过滤树数据
  const filterTreeData = (keyword, status) => {
    if (!keyword && status === 'all') {
      setFilteredTreeData(fileTreeData);
      return;
    }

    const filterNode = (nodes) => {
      return nodes.map(node => {
        const matchKeyword = !keyword || node.title.toLowerCase().includes(keyword.toLowerCase());
        const matchStatus = status === 'all' || (node.data && node.data.status === status);
        
        let filteredChildren = [];
        if (node.children) {
          filteredChildren = filterNode(node.children);
        }
        
        if (matchKeyword && matchStatus) {
          return { ...node, children: filteredChildren };
        } else if (filteredChildren.length > 0) {
          return { ...node, children: filteredChildren };
        }
        
        return null;
      }).filter(Boolean);
    };
    
    const filtered = filterNode(fileTreeData);
    setFilteredTreeData(filtered);
  };

  // 递归处理树数据，添加状态标签
  const processTreeData = (nodes) => {
    return nodes.map(node => ({
      ...node,
      title: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
          <span>{node.title}</span>
          {node.data && getGitStatusTag(node.data.status)}
        </div>
      ),
      children: node.children ? processTreeData(node.children) : undefined
    }));
  };

  // 树节点选择
  const onTreeSelect = (selectedKeys, info) => {
    if (selectedKeys.length > 0 && info.node.data) {
      console.log('选中文件:', info.node.data);
      // 在全屏模式中会处理文件选择逻辑
    }
  };

  // 处理树节点展开（触发懒加载）
  const onTreeExpand = (expandedKeys, { expanded, node }) => {
    setExpandedKeys(expandedKeys);
    
    // 如果是展开操作且节点有子目录但未加载，则触发懒加载
    if (expanded && node.type === 'folder' && !node.isLeaf && !loadedNodes.has(node.key)) {
      loadChildrenNodes(node.key);
    }
  };

  useEffect(() => {
    fetchServerMetrics();
    fetchFileTreeData();
    fetchReleasePlans();
    fetchCompareData();

    let interval;
    if (autoRefresh) {
      interval = setInterval(() => {
        fetchServerMetrics();
      }, 30000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  // 清除了旧的全屏监听器（已不需要）

  // Git风格的状态标签
  const getGitStatusTag = (status) => {
    const statusMap = {
      'synced': { color: 'green', text: '已同步', icon: <CheckCircleOutlined />, bgColor: '#f6ffed' },
      'modified': { color: 'orange', text: '已修改', icon: <FileTextOutlined />, bgColor: '#fff7e6' },
      'added': { color: 'blue', text: '新增', icon: <FileAddOutlined />, bgColor: '#e6f7ff' },
      'deleted': { color: 'red', text: '已删除', icon: <MinusCircleOutlined />, bgColor: '#fff2f0' },
      'missing': { color: 'red', text: '缺失', icon: <FileExclamationOutlined />, bgColor: '#fff2f0' },
      'conflict': { color: 'purple', text: '冲突', icon: <ExclamationCircleOutlined />, bgColor: '#f9f0ff' },
      'untracked': { color: 'gray', text: '未跟踪', icon: <FileOutlined />, bgColor: '#fafafa' },
      'partial': { color: 'blue', text: '部分缺失', icon: <ExclamationCircleOutlined />, bgColor: '#e6f7ff' }
    };
    const config = statusMap[status] || { color: 'default', text: status || '未知', icon: null, bgColor: '#fafafa' };
    
    return (
      <Tag color={config.color} icon={config.icon} style={{ backgroundColor: config.bgColor }}>
        {config.text}
      </Tag>
    );
  };

  // 获取服务器状态颜色
  const getServerStatusColor = (metrics) => {
    if (!metrics.timestamp) return 'red';
    
    const now = moment();
    const lastUpdate = moment(metrics.timestamp);
    const diffMinutes = now.diff(lastUpdate, 'minutes');
    
    if (diffMinutes > 5) return 'red';
    if (metrics.cpu_usage > 80 || metrics.disk_usage > 80) return 'orange';
    return 'green';
  };

  // 统计文件状态
  const getFileStats = (treeData) => {
    let stats = { synced: 0, modified: 0, added: 0, deleted: 0, missing: 0, conflict: 0, partial: 0, total: 0 };
    
    const countNodes = (nodes) => {
      nodes.forEach(node => {
        if (node.data && node.data.type === 'file') {
          stats.total++;
          stats[node.data.status] = (stats[node.data.status] || 0) + 1;
        }
        if (node.children) {
          countNodes(node.children);
        }
      });
    };
    
    countNodes(treeData);
    return stats;
  };

  // 全屏功能 - 打开文件对比新标签页
  const toggleFullscreen = () => {
    openDetailedCompareModal();
  };

  // 打开文件详细对比新标签页
  const openDetailedCompareModal = () => {
    const compareUrl = `/model-storage/file-compare?basePath=${encodeURIComponent('/HDD_Raid/SVN_MODEL_REPO')}&remoteUrl=${encodeURIComponent('http://10.63.30.93/GPU_MODEL_REPO/01.DEV/')}`;
    window.open(compareUrl, '_blank');
  };

  const fileStats = getFileStats(fileTreeData);
  const serverStatusColor = getServerStatusColor(serverMetrics);

  const releaseColumns = [
    {
      title: '卡型号',
      dataIndex: 'card_model',
      key: 'card_model',
      width: 100,
    },
    {
      title: '模型名称',
      dataIndex: 'model_name',
      key: 'model_name',
      width: 150,
    },
    {
      title: '发布时间',
      dataIndex: 'release_date',
      key: 'release_date',
      width: 120,
    },
    {
      title: 'Model状态',
      dataIndex: 'model_status',
      key: 'model_status',
      width: 100,
      render: (status) => getGitStatusTag(status)
    },
    {
      title: 'Vendor状态',
      dataIndex: 'vendor_status',
      key: 'vendor_status',
      width: 100,
      render: (status) => getGitStatusTag(status)
    },
    {
      title: '总体状态',
      dataIndex: 'overall_status',
      key: 'overall_status',
      width: 100,
      render: (status) => getGitStatusTag(status)
    },
    {
      title: '操作',
      key: 'actions',
      width: 160,
      render: (_, record) => (
        <Space>
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditPlan(record)}
          >
            编辑
          </Button>
          <Button 
            danger 
            icon={<DeleteOutlined />} 
            size="small"
            onClick={() => handleDeletePlan(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  const compareColumns = [
    {
      title: '文件路径',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
    },
    {
      title: '本地状态',
      dataIndex: 'local_status',
      key: 'local_status',
      width: 100,
      render: (status) => getGitStatusTag(status)
    },
    {
      title: '远程状态',
      dataIndex: 'remote_status',
      key: 'remote_status',
      width: 100,
      render: (status) => getGitStatusTag(status)
    },
    {
      title: '差异',
      dataIndex: 'diff',
      key: 'diff',
      width: 120,
    },
    {
      title: '操作',
      key: 'actions',
      width: 160,
      render: (_, record) => (
        <Space>
          <Button type="primary" size="small">查看</Button>
          {record.local_status !== 'synced' && (
            <Button type="primary" size="small" icon={<DownloadOutlined />}>
              同步
            </Button>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>🚀 大模型文件存储监控</h1>
        <Space size="large">
          <Space>
            <Badge status={serverStatusColor} />
            <span>服务器状态</span>
          </Space>
          <Switch 
            checked={autoRefresh}
            onChange={setAutoRefresh}
            checkedChildren="自动刷新"
            unCheckedChildren="手动刷新"
          />
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={refreshAllData}
          >
            刷新数据
          </Button>
                          <Button 
                  type="default"
                  icon={<FullscreenOutlined />}
                  onClick={toggleFullscreen}
                >
                  全屏模式
                </Button>
        </Space>
      </div>

      {serverMetrics.cpu_usage > 80 && (
        <Alert
          message="CPU使用率过高"
          description={`当前CPU使用率为 ${serverMetrics.cpu_usage}%，请注意监控系统性能`}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Row gutter={[24, 24]}>
        {/* 服务器监控 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <DatabaseOutlined /> 服务器监控 (**********)
              </span>
            }
            className={styles.card}
            extra={
              <Space>
                <Badge status={serverStatusColor} />
                <Tag color={serverStatusColor}>
                  {serverStatusColor === 'green' ? '正常' : serverStatusColor === 'orange' ? '警告' : '离线'}
                </Tag>
              </Space>
            }
          >
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Statistic
                  title="CPU使用率"
                  value={Math.round(serverMetrics.cpu_usage || 0)}
                  suffix="%"
                  valueStyle={{ 
                    color: serverMetrics.cpu_usage > 80 ? '#cf1322' : '#3f8600' 
                  }}
                />
                <Progress 
                  percent={Math.round(serverMetrics.cpu_usage || 0)}
                  status={serverMetrics.cpu_usage > 80 ? 'exception' : 'normal'}
                  size="small"
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="内存使用率"
                  value={Math.round(serverMetrics.memory_usage || 0)}
                  suffix="%"
                  valueStyle={{ 
                    color: serverMetrics.memory_usage > 80 ? '#cf1322' : '#3f8600' 
                  }}
                />
                <Progress 
                  percent={Math.round(serverMetrics.memory_usage || 0)}
                  status={serverMetrics.memory_usage > 80 ? 'exception' : 'normal'}
                  size="small"
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="磁盘使用率"
                  value={Math.round(serverMetrics.disk_usage || 0)}
                  suffix="%"
                  valueStyle={{ 
                    color: serverMetrics.disk_usage > 80 ? '#cf1322' : '#3f8600' 
                  }}
                />
                <Progress 
                  percent={Math.round(serverMetrics.disk_usage || 0)} 
                  status={serverMetrics.disk_usage > 80 ? 'exception' : 'normal'}
                  size="small"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="网络上传"
                  value={serverMetrics.network_upload || '0MB'}
                  valueStyle={{ fontSize: '14px', color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="网络下载"
                  value={serverMetrics.network_download || '0MB'}
                  valueStyle={{ fontSize: '14px', color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="磁盘读速率"
                  value={((serverMetrics.disk_read_bytes || 0) / 1024 / 1024).toFixed(2) + ' MB'}
                  valueStyle={{ fontSize: '14px', color: '#722ed1' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="磁盘写速率"
                  value={((serverMetrics.disk_write_bytes || 0) / 1024 / 1024).toFixed(2) + ' MB'}
                  valueStyle={{ fontSize: '14px', color: '#fa8c16' }}
                />
              </Col>
            </Row>
            {serverMetrics.timestamp && (
              <div className={styles.timestamp}>
                最后更新: {moment(serverMetrics.timestamp).format('YYYY-MM-DD HH:mm:ss')}
              </div>
            )}
          </Card>
        </Col>

        {/* 文件差异统计 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <BranchesOutlined /> 文件差异统计
              </span>
            }
            className={styles.card}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="已同步"
                  value={fileStats.synced}
                  valueStyle={{ color: '#3f8600' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="已修改"
                  value={fileStats.modified}
                  valueStyle={{ color: '#d48806' }}
                  prefix={<FileTextOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="缺失/删除"
                  value={(fileStats.missing || 0) + (fileStats.deleted || 0)}
                  valueStyle={{ color: '#cf1322' }}
                  prefix={<FileExclamationOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="总文件数"
                  value={fileStats.total}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<FileOutlined />}
                />
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <Progress 
                percent={Math.round((fileStats.synced / fileStats.total) * 100)}
                format={() => `同步率: ${Math.round((fileStats.synced / fileStats.total) * 100)}%`}
              />
            </div>
          </Card>
        </Col>

        {/* 文件状态对比 - 树状结构 */}
        <Col span={24}>
          <Card 
            title={
              <span>
                <DiffOutlined /> 文件状态对比 (类似 git status)
              </span>
            }
            className={styles.card}
            extra={
              <Space>
                <Radio.Group 
                  value={viewMode} 
                  onChange={(e) => setViewMode(e.target.value)}
                  buttonStyle="solid"
                  size="small"
                >
                  <Radio.Button value="tree">
                    <AppstoreOutlined /> 树状视图
                  </Radio.Button>
                  <Radio.Button value="list">
                    <UnorderedListOutlined /> 列表视图
                  </Radio.Button>
                </Radio.Group>
                <Search
                  placeholder="搜索文件..."
                  onSearch={handleSearch}
                  style={{ width: 200 }}
                  allowClear
                />
                <Select
                  value={statusFilter}
                  onChange={handleStatusFilter}
                  style={{ width: 120 }}
                >
                  <Option value="all">全部状态</Option>
                  <Option value="synced">已同步</Option>
                  <Option value="modified">已修改</Option>
                  <Option value="added">新增</Option>
                  <Option value="deleted">已删除</Option>
                  <Option value="missing">缺失</Option>
                  <Option value="conflict">冲突</Option>
                </Select>
                <Button 
                  type="primary"
                  icon={<SyncOutlined />}
                  loading={loading}
                  onClick={checkFileDifferences}
                >
                  检查差异
                </Button>
                <Button 
                  type="primary"
                  icon={<DownloadOutlined />}
                  loading={loading}
                  onClick={syncSelectedFiles}
                >
                  批量同步
                </Button>
                <Button 
                  type="default"
                  icon={<FullscreenOutlined />}
                  onClick={toggleFullscreen}
                >
                  全屏查看
                </Button>
              </Space>
            }
          >
            <div className={styles.fileTreeContainer}>
              <Spin spinning={loading}>
                {viewMode === 'tree' ? (
                  <Tree
                    showIcon
                    expandedKeys={expandedKeys}
                    onExpand={onTreeExpand}
                    treeData={processTreeData(filteredTreeData)}
                    onSelect={onTreeSelect}
                    className={styles.fileTree}
                    loading={lazyLoading}
                  />
                ) : (
                  <List
                    dataSource={getListDataFromTree(filteredTreeData)}
                    renderItem={(item) => (
                      <List.Item
                        className={styles.fileItem}
                        actions={[
                          <Button 
                            type="link" 
                            icon={<EyeOutlined />}
                            onClick={() => viewFileDiff(item)}
                          >
                            查看差异
                          </Button>,
                          item.status !== 'synced' && (
                            <Button 
                              type="link" 
                              icon={<DownloadOutlined />}
                              onClick={() => syncSingleFile(item.path)}
                            >
                              同步
                            </Button>
                          )
                        ].filter(Boolean)}
                      >
                        <List.Item.Meta
                          avatar={
                            <div className={styles.fileStatusIcon}>
                              {item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />}
                            </div>
                          }
                          title={
                            <Space>
                              <Text code>{item.name}</Text>
                              {getGitStatusTag(item.status)}
                              {item.size && <Tag color="blue">{item.size}</Tag>}
                            </Space>
                          }
                          description={
                            <div>
                              <Text type="secondary">{item.path}</Text>
                              {item.lastModified && (
                                <div>
                                  <Text type="secondary" style={{ fontSize: '12px' }}>
                                    最后修改: {item.lastModified}
                                  </Text>
                                </div>
                              )}
                              {item.diff_summary && (
                                <div>
                                  <Text type="secondary" style={{ fontSize: '12px' }}>
                                    {item.diff_summary}
                                  </Text>
                                </div>
                              )}
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                    pagination={{
                      pageSize: 20,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 个文件`
                    }}
                  />
                )}
              </Spin>
            </div>
          </Card>
        </Col>

        {/* 远程仓库对比 */}
        <Col span={24}>
          <Card 
            title={
              <span>
                <SyncOutlined /> 远程仓库对比 (http://10.63.30.93/GPU_MODEL_REPO/01.DEV/)
              </span>
            }
            extra={
              <Space>
                <Button 
                  type="primary" 
                  icon={<BranchesOutlined />}
                  onClick={openDetailedCompareModal}
                >
                  详细对比
                </Button>
                <Button 
                  icon={<SyncOutlined />}
                  loading={loading}
                  onClick={fetchCompareData}
                >
                  检查更新
                </Button>
                <Button 
                  icon={<SearchOutlined />}
                  loading={loading}
                  onClick={checkFileDifferences}
                >
                  检测差异
                </Button>
              </Space>
            }
            className={styles.card}
          >
            <Table 
              columns={compareColumns}
              dataSource={compareData}
              rowKey="path"
              loading={loading}
              pagination={{ pageSize: 10 }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </Col>

        {/* 发布计划管理 */}
        <Col span={24}>
          <Card 
            title={
              <span>
                <DatabaseOutlined /> 月度模型发布计划
              </span>
            }
            extra={
              <Space>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingPlan(null);
                    form.resetFields();
                    setModalVisible(true);
                  }}
                >
                  新增计划
                </Button>
                <Button icon={<ExportOutlined />}>导出计划</Button>
              </Space>
            }
            className={styles.card}
          >
            <Table 
              columns={releaseColumns}
              dataSource={releasePlans}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              scroll={{ x: 800 }}
            />
          </Card>
        </Col>
      </Row>





      {/* 文件差异详情模态框 */}
      <Modal
        title={
          <span>
            <CodeOutlined /> 文件差异详情
          </span>
        }
        open={diffModalVisible}
        onCancel={() => setDiffModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setDiffModalVisible(false)}>
            关闭
          </Button>,
          currentDiffFile && currentDiffFile.status !== 'synced' && (
            <Button 
              key="sync" 
              type="primary" 
              icon={<DownloadOutlined />}
              onClick={() => {
                syncSingleFile(currentDiffFile.path);
                setDiffModalVisible(false);
              }}
            >
              同步此文件
            </Button>
          )
        ].filter(Boolean)}
        width={800}
      >
        {currentDiffFile && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Text strong>文件路径:</Text>
                <Text code>{currentDiffFile.path}</Text>
                {getGitStatusTag(currentDiffFile.status)}
              </Space>
            </div>
            
            <Divider />
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>差异信息:</Text>
              <div style={{ marginTop: 8, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                <Text type="secondary">
                  {currentDiffFile.diff_summary || '暂无详细差异信息'}
                </Text>
              </div>
            </div>
            
            {currentDiffFile.lastModified && (
              <div>
                <Text strong>最后修改时间:</Text>
                <Text style={{ marginLeft: 8 }}>{currentDiffFile.lastModified}</Text>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 发布计划编辑模态框 */}
      <Modal
        title={editingPlan ? '编辑发布计划' : '新增发布计划'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingPlan(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSavePlan}
        >
          <Form.Item
            name="card_model"
            label="卡型号"
            rules={[{ required: true, message: '请输入卡型号' }]}
          >
            <Select placeholder="选择卡型号">
              <Option value="P800">P800</Option>
              <Option value="P800-PCIe">P800-PCIe</Option>
              <Option value="RG800">RG800</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="model_name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="输入模型名称" />
          </Form.Item>
          
          <Form.Item
            name="release_date"
            label="发布时间"
            rules={[{ required: true, message: '请选择发布时间' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="model_status"
                label="Model状态"
              >
                <Select placeholder="选择Model状态">
                  <Option value="inProgress">进行中</Option>
                  <Option value="complete">已完成</Option>
                  <Option value="missing">缺失</Option>
                  <Option value="partial">部分缺失</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="vendor_status"
                label="Vendor状态"
              >
                <Select placeholder="选择Vendor状态">
                  <Option value="inProgress">进行中</Option>
                  <Option value="complete">已完成</Option>
                  <Option value="missing">缺失</Option>
                  <Option value="partial">部分缺失</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="overall_status"
                label="总体状态"
              >
                <Select placeholder="选择总体状态">
                  <Option value="preparing">准备中</Option>
                  <Option value="released">已发布</Option>
                  <Option value="delayed">延期</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>


    </div>
  );
}