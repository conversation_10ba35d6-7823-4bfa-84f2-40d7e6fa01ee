2025-06-20 14:17:37 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\test_fix.py", line 36, in <module>
    print("✅ 所有测试通过！修复成功！")
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\monkey_sys_std.py", line 70, in monkey_sys_stdout
    stdout_raw(msg)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\test_fix.py", line 39, in <module>
    print(f"❌ 测试失败: {e}")
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\monkey_sys_std.py", line 70, in monkey_sys_stdout
    stdout_raw(msg)
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 0: illegal multibyte sequence
