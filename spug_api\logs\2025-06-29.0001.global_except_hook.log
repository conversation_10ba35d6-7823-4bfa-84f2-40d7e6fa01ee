2025-06-29 11:04:49 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 83, in wrapped
    res = handle_func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\makemigrations.py", line 164, in handle
    changes = autodetector.changes(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 43, in changes
    changes = self._detect_changes(convert_apps, graph)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 185, in _detect_changes
    self.generate_added_fields()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 850, in generate_added_fields
    self._generate_added_field(app_label, model_name, field_name)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\autodetector.py", line 871, in _generate_added_field
    field.default = self.questioner.ask_not_null_addition(field_name, model_name)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 146, in ask_not_null_addition
    choice = self._choice_input(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\db\migrations\questioner.py", line 107, in _choice_input
    result = input("Please select a valid option: ")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
EOFError: EOF when reading a line
2025-06-29 11:40:19 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\add_model_storage_permissions.py", line 66, in <module>
    add_model_storage_permissions()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\add_model_storage_permissions.py", line 62, in add_model_storage_permissions
    print("✓ 权限添加完成！")
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\monkey_sys_std.py", line 70, in monkey_sys_stdout
    stdout_raw(msg)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence
