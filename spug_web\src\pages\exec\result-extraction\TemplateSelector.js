/**
 * 智能模板选择器 - 提供预定义的提取模板
 */
import React, { useState } from 'react';
import { 
  Modal, 
  List, 
  Card, 
  Tag, 
  Button, 
  Space, 
  Input,
  Empty,
  Tooltip,
  Badge
} from 'antd';
import { 
  RobotOutlined,
  ThunderboltOutlined,
  FireOutlined,
  CloudOutlined,
  ToolOutlined,
  SearchOutlined
} from '@ant-design/icons';
import styles from './TemplateSelector.module.less';

const { Search } = Input;

// 预定义的提取模板
const TEMPLATES = [
  {
    id: 'gpu-performance',
    name: 'GPU性能测试',
    description: '提取GPU计算性能、内存带宽、温度等关键指标',
    icon: <FireOutlined />,
    color: '#ff4d4f',
    category: 'performance',
    metrics: [
      { label: 'GPU计算性能', unit: 'GFLOPS', pattern: /(\d+\.?\d*)\s*(GFLOPS|gflops)/i },
      { label: '内存带宽', unit: 'GB/s', pattern: /(\d+\.?\d*)\s*(GB\/s|MB\/s)/i },
      { label: 'GPU温度', unit: '°C', pattern: /(\d+\.?\d*)\s*°?C/i },
      { label: '功耗', unit: 'W', pattern: /(\d+\.?\d*)\s*(W|Watt|watts?)/i }
    ]
  },
  {
    id: 'network-benchmark',
    name: '网络性能测试',
    description: '提取网络吞吐量、延迟、丢包率等网络指标',
    icon: <CloudOutlined />,
    color: '#1890ff',
    category: 'network',
    metrics: [
      { label: '网络吞吐量', unit: 'Mbps', pattern: /(\d+\.?\d*)\s*(Mbps|Gbps)/i },
      { label: '延迟', unit: 'ms', pattern: /(\d+\.?\d*)\s*(ms|us)/i },
      { label: '丢包率', unit: '%', pattern: /(\d+\.?\d*)\s*%.*loss/i },
      { label: 'RTT', unit: 'ms', pattern: /rtt.*?(\d+\.?\d*)\s*ms/i }
    ]
  },
  {
    id: 'cpu-stress',
    name: 'CPU压力测试',
    description: '提取CPU使用率、负载、温度等系统指标',
    icon: <ThunderboltOutlined />,
    color: '#52c41a',
    category: 'system',
    metrics: [
      { label: 'CPU使用率', unit: '%', pattern: /cpu.*?(\d+\.?\d*)\s*%/i },
      { label: 'CPU温度', unit: '°C', pattern: /cpu.*?(\d+\.?\d*)\s*°?C/i },
      { label: '系统负载', unit: '', pattern: /load.*?(\d+\.?\d*)/i },
      { label: '内存使用率', unit: '%', pattern: /mem.*?(\d+\.?\d*)\s*%/i }
    ]
  },
  {
    id: 'storage-io',
    name: '存储I/O测试',
    description: '提取磁盘读写速度、IOPS等存储性能指标',
    icon: <ToolOutlined />,
    color: '#722ed1',
    category: 'storage',
    metrics: [
      { label: '读取速度', unit: 'MB/s', pattern: /read.*?(\d+\.?\d*)\s*(MB\/s|GB\/s)/i },
      { label: '写入速度', unit: 'MB/s', pattern: /write.*?(\d+\.?\d*)\s*(MB\/s|GB\/s)/i },
      { label: 'IOPS', unit: '', pattern: /(\d+\.?\d*)\s*iops/i },
      { label: '延迟', unit: 'ms', pattern: /latency.*?(\d+\.?\d*)\s*(ms|us)/i }
    ]
  },
  {
    id: 'custom-template',
    name: '自定义模板',
    description: '根据您的需求创建个性化的提取规则',
    icon: <RobotOutlined />,
    color: '#faad14',
    category: 'custom',
    metrics: []
  }
];

function TemplateSelector({ visible, onCancel, onApply }) {
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [filteredTemplates, setFilteredTemplates] = useState(TEMPLATES);

  const handleSearch = (value) => {
    setSearchText(value);
    if (!value) {
      setFilteredTemplates(TEMPLATES);
    } else {
      const filtered = TEMPLATES.filter(template => 
        template.name.toLowerCase().includes(value.toLowerCase()) ||
        template.description.toLowerCase().includes(value.toLowerCase()) ||
        template.category.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredTemplates(filtered);
    }
  };

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
  };

  const handleApply = () => {
    if (selectedTemplate) {
      onApply(selectedTemplate);
    }
  };

  const getCategoryIcon = (category) => {
    const icons = {
      performance: <FireOutlined />,
      network: <CloudOutlined />,
      system: <ThunderboltOutlined />,
      storage: <ToolOutlined />,
      custom: <RobotOutlined />
    };
    return icons[category] || <ToolOutlined />;
  };

  const getCategoryColor = (category) => {
    const colors = {
      performance: '#ff4d4f',
      network: '#1890ff',
      system: '#52c41a',
      storage: '#722ed1',
      custom: '#faad14'
    };
    return colors[category] || '#d9d9d9';
  };

  return (
    <Modal
      title={
        <div className={styles.modalTitle}>
          <RobotOutlined style={{ marginRight: '8px' }} />
          智能提取模板
        </div>
      }
      visible={visible}
      onCancel={onCancel}
      onOk={handleApply}
      okText="应用模板"
      cancelText="取消"
      width={800}
      okButtonProps={{ 
        disabled: !selectedTemplate,
        type: 'primary'
      }}
      className={styles.templateSelector}
    >
      <div className={styles.selectorContent}>
        <div className={styles.searchSection}>
          <Search
            placeholder="搜索模板..."
            allowClear
            onChange={(e) => handleSearch(e.target.value)}
            style={{ marginBottom: '16px' }}
            prefix={<SearchOutlined />}
          />
        </div>

        {filteredTemplates.length === 0 ? (
          <Empty
            description="没有找到匹配的模板"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <List
            grid={{ gutter: 16, column: 2 }}
            dataSource={filteredTemplates}
            renderItem={(template) => (
              <List.Item>
                <Card
                  hoverable
                  className={`${styles.templateCard} ${
                    selectedTemplate?.id === template.id ? styles.selected : ''
                  }`}
                  onClick={() => handleTemplateSelect(template)}
                  bodyStyle={{ padding: '16px' }}
                >
                  <div className={styles.templateHeader}>
                    <div className={styles.templateIcon} style={{ color: template.color }}>
                      {template.icon}
                    </div>
                    <div className={styles.templateInfo}>
                      <div className={styles.templateName}>
                        {template.name}
                        {selectedTemplate?.id === template.id && (
                          <Badge 
                            status="processing" 
                            style={{ marginLeft: '8px' }}
                          />
                        )}
                      </div>
                      <Tag 
                        color={getCategoryColor(template.category)}
                        size="small"
                        icon={getCategoryIcon(template.category)}
                      >
                        {template.category}
                      </Tag>
                    </div>
                  </div>
                  
                  <div className={styles.templateDescription}>
                    {template.description}
                  </div>
                  
                  <div className={styles.templateMetrics}>
                    <div className={styles.metricsTitle}>
                      包含指标 ({template.metrics.length})：
                    </div>
                    <div className={styles.metricsTags}>
                      {template.metrics.slice(0, 3).map((metric, index) => (
                        <Tag key={index} size="small">
                          {metric.label}
                        </Tag>
                      ))}
                      {template.metrics.length > 3 && (
                        <Tag size="small" color="blue">
                          +{template.metrics.length - 3} 更多
                        </Tag>
                      )}
                    </div>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        )}

        {selectedTemplate && (
          <div className={styles.templatePreview}>
            <Card 
              title="模板详情"
              size="small"
              className={styles.previewCard}
            >
              <div className={styles.previewContent}>
                <div className={styles.previewHeader}>
                  <Space>
                    <div style={{ color: selectedTemplate.color }}>
                      {selectedTemplate.icon}
                    </div>
                    <span className={styles.previewName}>
                      {selectedTemplate.name}
                    </span>
                  </Space>
                </div>
                
                <div className={styles.previewDescription}>
                  {selectedTemplate.description}
                </div>
                
                <div className={styles.previewMetrics}>
                  <div className={styles.metricsTitle}>
                    将提取以下指标：
                  </div>
                  <Space wrap>
                    {selectedTemplate.metrics.map((metric, index) => (
                      <Tooltip 
                        key={index}
                        title={`匹配模式: ${metric.pattern.toString()}`}
                      >
                        <Tag color="blue">
                          {metric.label} ({metric.unit})
                        </Tag>
                      </Tooltip>
                    ))}
                  </Space>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </Modal>
  );
}

export default TemplateSelector; 