#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据库中的主机配置
"""

import sqlite3
import os

def check_hosts_in_db():
    """检查数据库中的主机配置"""
    
    # 查找数据库文件
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db') or file == 'db.sqlite3':
                db_files.append(os.path.join(root, file))
    
    print("找到的数据库文件:")
    for db_file in db_files:
        print(f"  - {db_file}")
    
    if not db_files:
        print("未找到数据库文件")
        return
    
    # 使用第一个找到的数据库文件
    db_file = db_files[0]
    print(f"\n使用数据库文件: {db_file}")
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 检查hosts表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='hosts';")
        if not cursor.fetchone():
            print("hosts表不存在")
            return
        
        # 查询所有主机
        cursor.execute("SELECT id, name, hostname, port, username, is_verified, desc FROM hosts")
        hosts = cursor.fetchall()
        
        print(f"\n数据库中的主机配置 (共{len(hosts)}台):")
        print("-" * 80)
        
        target_found = False
        for host in hosts:
            host_id, name, hostname, port, username, is_verified, desc = host
            print(f"ID: {host_id}")
            print(f"名称: {name}")
            print(f"主机名: {hostname}")
            print(f"端口: {port}")
            print(f"用户名: {username}")
            print(f"已验证: {'是' if is_verified else '否'}")
            print(f"描述: {desc}")
            print("-" * 40)
            
            if hostname == '**********':
                target_found = True
        
        if target_found:
            print("✅ 找到目标主机 **********")
        else:
            print("❌ 未找到目标主机 **********")
            print("\n建议添加主机配置:")
            print("INSERT INTO hosts (name, hostname, port, username, is_verified, desc, created_at, created_by_id)")
            print("VALUES ('GPU服务器', '**********', 22, 'root', 1, '大模型文件存储监控服务器', datetime('now'), 1);")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库操作失败: {e}")

if __name__ == "__main__":
    check_hosts_in_db()
