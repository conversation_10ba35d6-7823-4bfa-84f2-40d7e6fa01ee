
大模型管理文档
基本信息
远程地址 root@192.2.29.9  pass: 123456
路径 大模型文件  /HDD_Raid/SVN_MODEL_REPO/Model
     model 下面是具体的模型文件夹
     Baichuan2-13B                     DeepSeek-V3-0324-P800  Qwen2.5-7B               Qwen3-4B
     模型文件夹里面就是具体的文件  一定要包含一个md5txt
     DeepSeek-Prover-V2-671B-tokenizer.tar.gz  md5sums.txt
     配套测试文件  /HDD_Raid/SVN_MODEL_REPO/Vendor
     Vendor 下面有很多厂商 
     AMD  Biren  Cambricon  Enflame  Hygon  Iluvatar  Kunlunxin  MetaX  Moffett  Moore_Threads
     以Kunlunxin为例
     下面是卡型号
     P800  P800-PCIe  RG800
     卡下面是 模型名称 代表这张卡测过这些模型
     Bert-base  ResNet50  Yolov5
     模型下面是模型类型 比如说 推理 训练等等
     Inference
     类型下面就是版本号 格式要求 vx.x.x等等
     v1.0   
    最后一层就是具体的文件了 包括doc文件夹（内含指导文件） 以及其他配套文件
    doc  imagenet.tar.gz  modelzoo_ubuntu_x86.tar.gz  ResNet50.tar.gz
远程仓库地址 http://10.63.30.93/GPU_MODEL_REPO/01.DEV/ 
我的svn 账密  sys49169  Aa123,.,.
