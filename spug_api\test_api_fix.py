#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试API修复是否成功
"""

import requests
import json

def test_file_tree_compare_api():
    """测试文件树对比API"""
    url = "http://localhost:8000/api/model-storage/file-tree-compare/"
    
    print("测试远程文件树API...")
    print(f"URL: {url}")
    
    try:
        # 测试第一级目录
        response = requests.get(url, params={'first_level': 'true'}, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ API返回成功")
                print(f"数据类型: {type(data)}")
                if isinstance(data, list):
                    print(f"数组长度: {len(data)}")
                    if data:
                        print("第一个元素结构:")
                        print(json.dumps(data[0], indent=2, ensure_ascii=False))
                else:
                    print("数据内容:")
                    print(json.dumps(data, indent=2, ensure_ascii=False)[:500] + "...")
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON")
                print(f"响应内容: {response.text[:200]}...")
                return False
        elif response.status_code == 401:
            print("⚠️  需要认证 (这是正常的)")
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保spug服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_lazy_load_tree_api():
    """测试懒加载树API"""
    url = "http://localhost:8000/api/model-storage/lazy-load-tree/"
    
    print("\n测试本地目录懒加载API...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, params={'root': 'true'}, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ API返回成功")
                print(f"数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON")
                return False
        elif response.status_code == 401:
            print("⚠️  需要认证 (这是正常的)")
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("API修复验证测试")
    print("=" * 50)
    
    tests = [
        ("远程文件树API", test_file_tree_compare_api),
        ("本地懒加载API", test_lazy_load_tree_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有API修复验证通过!")
    else:
        print("⚠️  部分API仍有问题")
    
    print("\n注意:")
    print("- 401错误是正常的，因为需要登录认证")
    print("- 主要检查是否没有500错误和语法错误")
    print("- 如果返回200状态码，说明修复成功")

if __name__ == "__main__":
    main()
