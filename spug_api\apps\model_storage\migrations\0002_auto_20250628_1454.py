# Generated by Django 2.2.28 on 2025-06-28 14:54

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='filestatus',
            options={'ordering': ('file_path',), 'verbose_name': '文件状态', 'verbose_name_plural': '文件状态'},
        ),
        migrations.AlterModelOptions(
            name='releaseplan',
            options={'ordering': ('-created_at',), 'verbose_name': '发布计划', 'verbose_name_plural': '发布计划'},
        ),
        migrations.AlterModelOptions(
            name='servermetrics',
            options={'ordering': ('-timestamp',), 'verbose_name': '服务器指标', 'verbose_name_plural': '服务器指标'},
        ),
    ]
