# Generated by Django 2.2.28 on 2025-06-28 12:30

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FileStatus',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_path', models.CharField(max_length=512, verbose_name='文件路径')),
                ('file_type', models.CharField(max_length=32, verbose_name='文件类型')),
                ('status', models.CharField(max_length=32, verbose_name='状态')),
                ('size', models.BigIntegerField(blank=True, null=True, verbose_name='文件大小')),
                ('md5_hash', models.CharField(blank=True, max_length=32, null=True, verbose_name='MD5哈希')),
                ('last_modified', models.DateTimeField(blank=True, null=True, verbose_name='最后修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'model_storage_file_status',
                'ordering': ('file_path',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='ReleasePlan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_model', models.CharField(max_length=64, verbose_name='卡型号')),
                ('model_name', models.CharField(max_length=128, verbose_name='模型名称')),
                ('release_date', models.DateField(verbose_name='计划发布时间')),
                ('model_status', models.CharField(default='inProgress', max_length=32, verbose_name='Model状态')),
                ('vendor_status', models.CharField(default='inProgress', max_length=32, verbose_name='Vendor状态')),
                ('overall_status', models.CharField(default='preparing', max_length=32, verbose_name='总体状态')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'model_storage_release_plans',
                'ordering': ('-created_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='ServerMetrics',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('disk_usage', models.FloatField(verbose_name='磁盘使用率')),
                ('cpu_usage', models.FloatField(verbose_name='CPU使用率')),
                ('io_speed', models.CharField(max_length=32, verbose_name='磁盘IO速度')),
                ('network_speed', models.CharField(max_length=32, verbose_name='网络速度')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'model_storage_server_metrics',
                'ordering': ('-timestamp',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
