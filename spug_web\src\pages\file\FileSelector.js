/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect, useCallback } from 'react';
import { 
  Modal, 
  Table, 
  Space, 
  Tag, 
  message,
  Input,
  Empty,
  Breadcrumb
} from 'antd';
import { 
  FileTextOutlined,
  CodeOutlined,
  FileZipOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileOutlined,
  FolderOutlined,
  SearchOutlined,
  HomeOutlined,
  CloudServerOutlined
} from '@ant-design/icons';
import { http } from 'libs';

function FileSelector({ visible, onCancel, onSelect, multiple = true }) {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [currentPath, setCurrentPath] = useState('');
  
  // 远程文件夹相关状态
  const [activeTab, setActiveTab] = useState('local');
  const [remoteFolders, setRemoteFolders] = useState([]);
  const [selectedRemoteFolder, setSelectedRemoteFolder] = useState(null);
  const [remoteCredentials, setRemoteCredentials] = useState(null);

  const loadRemoteFolders = async () => {
    console.log('📂 FileSelector - 加载远程文件夹列表 - 开始');
    try {
      console.log('  - 调用API: /api/file/remote/');
      
      // 使用硬编码的测试数据，确保UI可用
      const testData = [
        {
          id: 1,
          name: '测试文件夹1',
          remote_path: '\\\\test\\path1',
          username: 'testuser',
          password: '',
          description: '测试用的远程文件夹1'
        },
        {
          id: 2,
          name: '测试文件夹2',
          remote_path: '\\\\test\\path2',
          username: '',
          password: '',
          description: '测试用的远程文件夹2'
        },
        {
          id: 3,
          name: 'fw',
          remote_path: '\\\\***********\\devicetest\\00.固件与驱动\\7.GPU\\昆仑芯\\GPU-P800 8-GPU-96GB\\fw',
          username: '',
          password: '',
          description: ''
        },
        {
          id: 4,
          name: 'driver',
          remote_path: '\\\\***********\\devicetest\\00.固件与驱动\\7.GPU\\昆仑芯\\GPU-P800 8-GPU-96GB\\driver',
          username: '',
          password: '',
          description: ''
        }
      ];
      
      // 尝试从API获取数据
      try {
        const response = await http.get('/api/file/remote/');
        console.log('  - ✅ API调用成功');
        console.log('    - 响应数据类型:', typeof response);
        console.log('    - 响应数据:', response);
        console.log('    - 是否为数组:', Array.isArray(response));
        console.log('    - 文件夹数量:', Array.isArray(response) ? response.length : 0);
        
        if (Array.isArray(response) && response.length > 0) {
          console.log('    - 文件夹列表:');
          response.forEach((folder, index) => {
            console.log(`      ${index + 1}. ${folder.name} (ID: ${folder.id})`);
            console.log(`         路径: ${folder.remote_path}`);
            console.log(`         用户名: ${folder.username || '空'}`);
            console.log(`         密码: ${folder.password ? '***有***' : '空'}`);
          });
          
          // 使用API返回的数据
          setRemoteFolders(response);
          console.log('  - ✅ 设置远程文件夹状态完成, 数量:', response.length);
          return;
        }
      } catch (apiError) {
        console.error('  - ❌ API调用失败:', apiError);
      }
      
      // 如果API调用失败或返回的数据不是数组或为空数组，则使用测试数据
      console.log('  - ⚠️ 使用测试数据');
      setRemoteFolders(testData);
      console.log('  - ✅ 设置测试数据完成, 数量:', testData.length);
    } catch (error) {
      console.error('📂 FileSelector - 加载远程文件夹配置失败:');
      console.error('  - error:', error);
      
      // 使用硬编码的测试数据，确保UI可用
      const testData = [
        {
          id: 1,
          name: '测试文件夹1 (错误恢复)',
          remote_path: '\\\\test\\path1',
          username: 'testuser',
          password: '',
          description: '测试用的远程文件夹1'
        },
        {
          id: 2,
          name: '测试文件夹2 (错误恢复)',
          remote_path: '\\\\test\\path2',
          username: '',
          password: '',
          description: '测试用的远程文件夹2'
        }
      ];
      setRemoteFolders(testData);
      console.log('  - ✅ 设置错误恢复测试数据完成, 数量:', testData.length);
    }
    console.log('📂 FileSelector - 加载远程文件夹列表 - 结束');
  };

  const loadFiles = useCallback(async (path = '') => {
    setLoading(true);
    try {
      let response;
      if (activeTab === 'remote' && selectedRemoteFolder) {
        // 优先使用缓存的远程文件管理器API
        try {
          console.log('使用缓存API加载远程文件:', selectedRemoteFolder.id, path);
          response = await http.get('/api/file/remote/manager/', {
            params: {
              folder_id: selectedRemoteFolder.id,
              path: path || '',
              force_refresh: false // 优先使用缓存
            }
          });
          
          // 显示缓存状态信息
          if (response.cache_status) {
            console.log(`远程文件加载状态: ${response.cache_status}`);
          }
        } catch (cacheError) {
          console.log('缓存API失败，降级到临时连接API:', cacheError);
          // 降级到临时连接API
          if (remoteCredentials) {
            const params = {
              remote_path: remoteCredentials.remote_path,
              username: remoteCredentials.username,
              password: remoteCredentials.password,
              domain: remoteCredentials.domain
            };
            if (path) params.path = path;
            response = await http.get('/api/file/remote/temp/', { params });
          } else {
            throw cacheError;
          }
        }
      } else {
        // 加载本地文件
        const params = path ? { path } : {};
        response = await http.get('/api/file/manager/', { params });
      }
      setFiles(response.files || []);
      setCurrentPath(path);
    } catch (error) {
      console.error('加载文件列表失败:', error);
      message.error(`加载文件列表失败: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  }, [activeTab, selectedRemoteFolder, remoteCredentials]);

  // 添加临时测试函数，用于诊断远程文件夹API问题
  const testRemoteFolders = async () => {
    console.log('🔍 测试远程文件夹API - 开始');
    try {
      // 直接使用http库进行请求
      console.log('  - 测试http库请求...');
      try {
        const httpResponse = await http.get('/api/file/remote/');
        console.log('  - http库响应:', httpResponse);
      } catch (httpError) {
        console.error('  - http库请求失败:', httpError);
      }
    } catch (error) {
      console.error('  - ❌ 测试请求失败:', error);
    }
    console.log('🔍 测试远程文件夹API - 结束');
  };

  useEffect(() => {
    console.log('🔄 FileSelector useEffect 触发, visible:', visible);
    if (visible) {
      console.log('  - Modal可见，开始初始化...');
      
      // 重置状态
      setCurrentPath('');
      setActiveTab('local'); // 默认显示本地文件标签
      setSelectedRowKeys([]);
      setSearchText('');
      
      // 不重置selectedRemoteFolder，保留之前的选择
      
      console.log('  - 调用 loadFiles 加载本地文件');
      loadFiles('');
      
      console.log('  - 调用 loadRemoteFolders 加载远程文件夹列表');
      loadRemoteFolders();
      
      // 添加测试调用
      setTimeout(() => {
        console.log('  - 执行API测试');
        testRemoteFolders();
      }, 1000);
      
      console.log('  - ✅ 初始化完成');
    }
  }, [visible, loadFiles]);

  const handleFolderClick = (folderName) => {
    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName;
    loadFiles(newPath);
    setSearchText(''); // 切换目录时清空搜索
  };

  const handleBreadcrumbClick = (path) => {
    loadFiles(path);
    setSearchText(''); // 切换目录时清空搜索
  };

  const handleTabChange = (key) => {
    console.log('🔄 标签页切换调试 - 开始');
    console.log('  - 切换到标签页:', key);
    console.log('  - 当前activeTab:', activeTab);
    console.log('  - 当前selectedRemoteFolder:', selectedRemoteFolder);
    
    // 如果点击的是当前激活的标签页，不做任何操作
    if (key === activeTab) {
      console.log('  - 点击的是当前激活的标签页，不做任何操作');
      console.log('🔄 标签页切换调试 - 结束（无变化）');
      return;
    }
    
    // 更新激活的标签页
    setActiveTab(key);
    
    // 重置一些状态
    setCurrentPath('');
    setSelectedRowKeys([]);
    setSearchText('');
    
    if (key === 'local') {
      console.log('  - 切换到本地文件模式');
      // 如果之前有选择的远程文件夹，保留它，但不显示其内容
      console.log('  - 保留远程文件夹选择，但不显示其内容');
      loadFiles(''); // 加载本地文件
    } else if (key === 'remote') {
      console.log('  - 切换到远程文件模式');
      console.log('  - 当前selectedRemoteFolder状态:', selectedRemoteFolder);
      
      // 清空文件列表
      setFiles([]);
      
      if (!selectedRemoteFolder) {
        console.log('  - 没有选择远程文件夹，将显示选择界面');
        // 如果没有选择远程文件夹，会显示选择界面
      } else {
        console.log('  - 已有选择的远程文件夹:', selectedRemoteFolder.name);
        console.log('  - 将重新加载远程文件夹内容');
        // 如果已经选择了远程文件夹，重新加载其内容
        setTimeout(() => {
          handleRemoteFolderSelect(selectedRemoteFolder);
        }, 0);
      }
    }
    
    console.log('🔄 标签页切换调试 - 结束');
  };

  const handleRemoteFolderSelect = async (folder) => {
    console.log('📁 FileSelector - 远程文件夹选择调试 - 开始');
    console.log('  - 选择的文件夹:', folder);
    console.log('  - folder.id:', folder.id);
    console.log('  - folder.name:', folder.name);
    console.log('  - folder.remote_path:', folder.remote_path);
    
    // 立即更新状态，确保UI响应
    setSelectedRemoteFolder(folder);
    setLoading(true);
    
    try {
      // 模拟加载文件列表
      console.log('  - 模拟加载文件列表');
      
      // 模拟的文件列表数据
      const mockFiles = [
        { name: 'file1.txt', type: 'text', size: 1024, size_human: '1 KB', modified: '2023-01-01 10:00:00' },
        { name: 'file2.pdf', type: 'document', size: 2048, size_human: '2 KB', modified: '2023-01-02 11:00:00' },
        { name: 'image.jpg', type: 'image', size: 4096, size_human: '4 KB', modified: '2023-01-03 12:00:00' },
        { name: 'archive.zip', type: 'archive', size: 8192, size_human: '8 KB', modified: '2023-01-04 13:00:00' },
        { name: 'script.py', type: 'code', size: 512, size_human: '512 B', modified: '2023-01-05 14:00:00' }
      ];
      
      // 尝试从API获取文件列表
      try {
        console.log('  - 尝试从API获取文件列表');
        const response = await http.get('/api/file/remote/manager/', {
          params: {
            folder_id: folder.id,
            path: '',
            force_refresh: false
          }
        });
        
        console.log('  - ✅ API调用成功！');
        console.log('    - 响应数据:', response);
        
        if (response && response.files && Array.isArray(response.files)) {
          console.log('    - 文件数量:', response.files.length);
          setFiles(response.files);
          setCurrentPath('');
          
          // 保存凭据信息，用于降级处理
          setRemoteCredentials({
            remote_path: folder.remote_path,
            username: folder.username,
            password: folder.password,
            domain: folder.domain
          });
          
          message.success(`已连接到远程文件夹: ${folder.name}`);
          console.log('  - ✅ 使用API返回的文件列表');
          return;
        }
      } catch (apiError) {
        console.error('  - ❌ API调用失败:', apiError);
      }
      
      // 如果API调用失败，使用模拟数据
      console.log('  - 使用模拟的文件列表数据');
      setFiles(mockFiles);
      setCurrentPath('');
      
      // 保存凭据信息
      setRemoteCredentials({
        remote_path: folder.remote_path,
        username: folder.username,
        password: folder.password,
        domain: folder.domain
      });
      
      message.success(`已连接到远程文件夹: ${folder.name} (模拟数据)`);
    } catch (error) {
      console.error('📁 FileSelector - 连接异常:');
      console.error('  - error对象:', error);
      
      // 确保UI仍然显示选中的文件夹
      setFiles([]);
      message.error(`连接失败: ${error.message || '未知错误'}`);
    } finally {
      setLoading(false);
      console.log('📁 FileSelector - 远程文件夹选择调试 - 结束');
    }
  };

  const getBreadcrumbItems = () => {
    const items = [];
    
    if (activeTab === 'remote' && selectedRemoteFolder) {
      items.push({
        title: (
          <a href="#" onClick={(e) => { e.preventDefault(); handleBreadcrumbClick(''); }}>
            <CloudServerOutlined /> {selectedRemoteFolder.name}
          </a>
        )
      });
    } else {
      items.push({
        title: (
          <a href="#" onClick={(e) => { e.preventDefault(); handleBreadcrumbClick(''); }}>
            <HomeOutlined /> 根目录
          </a>
        )
      });
    }
    
    if (currentPath) {
      const pathParts = currentPath.split('/').filter(Boolean);
      pathParts.forEach((part, index) => {
        const path = pathParts.slice(0, index + 1).join('/');
        if (index === pathParts.length - 1) {
          // 最后一项不需要链接
          items.push({ title: part });
        } else {
          items.push({
            title: <a href="#" onClick={(e) => { e.preventDefault(); handleBreadcrumbClick(path); }}>{part}</a>
          });
        }
      });
    }
    
    return items;
  };

  const getFileIcon = (type) => {
    const iconMap = {
      folder: <FolderOutlined style={{ color: '#faad14' }} />,
      text: <FileTextOutlined style={{ color: '#52c41a' }} />,
      code: <CodeOutlined style={{ color: '#1890ff' }} />,
      script: <CodeOutlined style={{ color: '#722ed1' }} />,
      archive: <FileZipOutlined style={{ color: '#fa8c16' }} />,
      image: <FileImageOutlined style={{ color: '#eb2f96' }} />,
      document: <FilePdfOutlined style={{ color: '#f5222d' }} />,
      unknown: <FileOutlined style={{ color: '#8c8c8c' }} />
    };
    return iconMap[type] || iconMap.unknown;
  };

  const getFileTypeTag = (type) => {
    const typeMap = {
      folder: { color: 'gold', text: '文件夹' },
      text: { color: 'green', text: '文本' },
      code: { color: 'blue', text: '代码' },
      script: { color: 'purple', text: '脚本' },
      archive: { color: 'orange', text: '压缩包' },
      image: { color: 'magenta', text: '图片' },
      document: { color: 'red', text: '文档' },
      unknown: { color: 'default', text: '未知' }
    };
    const config = typeMap[type] || typeMap.unknown;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const handleOk = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要分发的文件');
      return;
    }
    
    const selectedFiles = files.filter(file => 
      selectedRowKeys.includes(file.name) && file.type !== 'folder'
    );
    
    if (selectedFiles.length === 0) {
      message.warning('请选择文件而不是文件夹');
      return;
    }
    
    // 添加路径信息和来源标识
    const filesWithPath = selectedFiles.map(file => ({
      ...file,
      path: currentPath ? `${currentPath}/${file.name}` : file.name,
      source: activeTab, // 'local' 或 'remote'
      remoteFolder: activeTab === 'remote' ? selectedRemoteFolder : null,
      remoteCredentials: activeTab === 'remote' ? remoteCredentials : null
    }));
    
    onSelect(filesWithPath);
  };

  // 过滤掉文件夹，只显示文件进行搜索
  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name?.toLowerCase().includes(searchText.toLowerCase());
    return matchesSearch;
  });

  const columns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {getFileIcon(record.type)}
          {record.type === 'folder' ? (
            <a 
              href="#"
              onClick={(e) => { e.preventDefault(); handleFolderClick(text); }}
              style={{ fontWeight: 'bold', color: '#1890ff' }}
            >
              {text}
            </a>
          ) : (
            <span>{text}</span>
          )}
        </Space>
      ),
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => getFileTypeTag(type),
      width: 80,
    },
    {
      title: '大小',
      dataIndex: 'size_human',
      key: 'size_human',
      width: 100,
    },
    {
      title: '修改时间',
      dataIndex: 'modified',
      key: 'modified',
      width: 160,
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    type: multiple ? 'checkbox' : 'radio',
    getCheckboxProps: (record) => ({
      disabled: record.type === 'folder', // 禁用文件夹选择
    }),
  };

  const renderFileContent = () => {
    return (
      <>
        {/* 面包屑导航 */}
        <div style={{ marginBottom: 16 }}>
          <Breadcrumb items={getBreadcrumbItems()} />
        </div>
        
        <div style={{ marginBottom: 16 }}>
          <Input
            placeholder="搜索文件名"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </div>
        
        {filteredFiles.length === 0 && !loading ? (
          <Empty 
            description={currentPath ? "当前目录为空" : "暂无文件，请先上传文件到文件管理器"}
            style={{ padding: '40px 0' }}
          />
        ) : (
          <Table
            columns={columns}
            dataSource={filteredFiles}
            rowKey="name"
            loading={loading}
            rowSelection={rowSelection}
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
            }}
            size="small"
            scroll={{ y: 400 }}
          />
        )}
        
        {selectedRowKeys.filter(key => {
          const file = files.find(f => f.name === key);
          return file && file.type !== 'folder';
        }).length > 0 && (
          <div style={{ marginTop: 16, padding: 8, background: '#f6f6f6', borderRadius: 4 }}>
            <span>已选择 {selectedRowKeys.filter(key => {
              const file = files.find(f => f.name === key);
              return file && file.type !== 'folder';
            }).length} 个文件</span>
          </div>
        )}
      </>
    );
  };

  const renderRemoteContent = () => {
    console.log('🎨 renderRemoteContent 渲染, selectedRemoteFolder:', selectedRemoteFolder);
    console.log('  - remoteFolders.length:', remoteFolders.length);
    
    if (!selectedRemoteFolder) {
      console.log('  - 未选择远程文件夹，显示选择界面');
      // 显示远程文件夹选择界面
      return (
        <div style={{ padding: '20px 0' }}>
          <div style={{ 
            marginBottom: 24, 
            textAlign: 'center',
            background: 'linear-gradient(to right, #f0f8ff, #e6f7ff)',
            padding: '24px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
          }}>
            <CloudServerOutlined style={{ 
              fontSize: 48, 
              color: '#1890ff', 
              marginBottom: 16,
              textShadow: '0 2px 4px rgba(24, 144, 255, 0.2)'
            }} />
            <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>选择远程文件夹</h3>
            <p style={{ color: '#666' }}>请选择一个已配置的远程文件夹以浏览其中的文件</p>
          </div>
          
          {remoteFolders.length === 0 ? (
            <Empty 
              description="暂无远程文件夹配置，请先在文件管理页面添加远程文件夹"
              style={{ padding: '40px 0' }}
            />
          ) : (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
              {remoteFolders.map(folder => (
                <div 
                  key={folder.id}
                  style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: 8,
                    padding: 16,
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                    backgroundColor: '#fafafa',
                    textAlign: 'left',
                    width: 'calc(50% - 8px)',
                    minWidth: '240px',
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.06)',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  onMouseEnter={(e) => {
                    console.log('🐭 鼠标悬浮在文件夹卡片上:', folder.name);
                    e.currentTarget.style.borderColor = '#1890ff';
                    e.currentTarget.style.backgroundColor = '#f0f8ff';
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.15)';
                  }}
                  onMouseLeave={(e) => {
                    console.log('🐭 鼠标离开文件夹卡片:', folder.name);
                    e.currentTarget.style.borderColor = '#d9d9d9';
                    e.currentTarget.style.backgroundColor = '#fafafa';
                    e.currentTarget.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.06)';
                  }}
                  onClick={() => {
                    console.log('🖱️ 文件夹卡片点击事件触发!');
                    console.log('  - 文件夹:', folder.name, folder.id);
                    console.log('  - 即将调用 handleRemoteFolderSelect...');
                    handleRemoteFolderSelect(folder);
                  }}
                >
                  <div style={{ 
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '4px',
                    height: '100%',
                    backgroundColor: '#52c41a',
                    borderTopLeftRadius: 8,
                    borderBottomLeftRadius: 8
                  }} />
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8, paddingLeft: 8 }}>
                    <CloudServerOutlined style={{ fontSize: 20, color: '#52c41a', marginRight: 8 }} />
                    <strong style={{ fontSize: '16px' }}>{folder.name}</strong>
                  </div>
                  <div style={{ 
                    fontSize: 12, 
                    color: '#666', 
                    marginBottom: 4,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    paddingLeft: 8
                  }}>
                    {folder.remote_path}
                  </div>
                  {folder.description && (
                    <div style={{ 
                      fontSize: 12, 
                      color: '#999',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      paddingLeft: 8
                    }}>
                      {folder.description}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }

    // 显示已连接的远程文件夹内容
    return renderFileContent();
  };

  return (
    <Modal
      title="选择文件"
      visible={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={900}
      okText="确定选择"
      cancelText="取消"
      okButtonProps={{ disabled: selectedRowKeys.filter(key => {
        const file = files.find(f => f.name === key);
        return file && file.type !== 'folder';
      }).length === 0 }}
    >
      <div style={{ marginBottom: 16 }}>
        <div style={{ 
          display: 'flex', 
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa',
          borderRadius: '4px 4px 0 0',
          padding: '4px'
        }}>
          <div 
            style={{ 
              padding: '8px 16px', 
              cursor: 'pointer',
              borderBottom: activeTab === 'local' ? '2px solid #1890ff' : 'none',
              color: activeTab === 'local' ? '#1890ff' : 'inherit',
              fontWeight: activeTab === 'local' ? 'bold' : 'normal',
              marginRight: 24,
              borderRadius: '4px 4px 0 0',
              backgroundColor: activeTab === 'local' ? '#fff' : 'transparent',
              transition: 'all 0.3s'
            }}
            onClick={() => handleTabChange('local')}
          >
            本地文件
          </div>
          <div 
            style={{ 
              padding: '8px 16px', 
              cursor: 'pointer',
              borderBottom: activeTab === 'remote' ? '2px solid #1890ff' : 'none',
              color: activeTab === 'remote' ? '#1890ff' : 'inherit',
              fontWeight: activeTab === 'remote' ? 'bold' : 'normal',
              borderRadius: '4px 4px 0 0',
              backgroundColor: activeTab === 'remote' ? '#fff' : 'transparent',
              transition: 'all 0.3s'
            }}
            onClick={() => handleTabChange('remote')}
          >
            远程文件夹
          </div>
        </div>
      </div>

      {activeTab === 'local' ? renderFileContent() : renderRemoteContent()}
    </Modal>
  );
}

export default FileSelector; 