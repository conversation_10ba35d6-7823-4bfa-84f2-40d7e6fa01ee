# Generated by Django 2.2.28 on 2025-06-12 10:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0004_auto_20250611_2004'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='remotefoldercache',
            options={'ordering': ('-id',), 'verbose_name': '远程文件夹缓存元数据', 'verbose_name_plural': '远程文件夹缓存元数据'},
        ),
        migrations.RemoveField(
            model_name='remotefoldercache',
            name='files_data',
        ),
        migrations.AddField(
            model_name='remotefoldercache',
            name='cache_size',
            field=models.BigIntegerField(default=0, verbose_name='缓存大小（字节）'),
        ),
        migrations.AddField(
            model_name='remotefoldercache',
            name='file_count',
            field=models.IntegerField(default=0, verbose_name='文件数量'),
        ),
    ]
