# Generated by Django 2.2.28 on 2025-06-20 15:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('exec', '0010_auto_20250620_1552'),
    ]

    operations = [
        # 首先删除现有的外键约束
        migrations.RunSQL(
            "PRAGMA foreign_keys=OFF;",
            reverse_sql="PRAGMA foreign_keys=ON;",
        ),
        
        # 删除并重新创建表以修改外键关系
        migrations.RunSQL(
            """
            CREATE TABLE exec_test_results_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plan_name VARCHAR(255) NOT NULL,
                execution_id INTEGER NOT NULL REFERENCES exec_test_plan_executions(id),
                task_name VARCHAR(255),
                result_data TEXT NOT NULL,
                created_by_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                confirmed_metrics_count INTEGER DEFAULT 0
            );
            """,
            reverse_sql="DROP TABLE exec_test_results_new;",
        ),
        
        # 复制数据（只复制execution_id在test_plan_executions表中存在的记录）
        migrations.RunSQL(
            """
            INSERT INTO exec_test_results_new 
            (id, plan_name, execution_id, task_name, result_data, created_by_id, created_at, confirmed_metrics_count)
            SELECT 
                r.id, r.plan_name, r.execution_id, r.task_name, r.result_data, 
                r.created_by_id, r.created_at, r.confirmed_metrics_count
            FROM exec_test_results r
            WHERE EXISTS (SELECT 1 FROM exec_test_plan_executions e WHERE e.id = r.execution_id);
            """,
            reverse_sql="",
        ),
        
        # 删除旧表，重命名新表
        migrations.RunSQL(
            """
            DROP TABLE exec_test_results;
            ALTER TABLE exec_test_results_new RENAME TO exec_test_results;
            """,
            reverse_sql="",
        ),
        
        # 重新启用外键约束
        migrations.RunSQL(
            "PRAGMA foreign_keys=ON;",
            reverse_sql="PRAGMA foreign_keys=OFF;",
        ),
    ]
