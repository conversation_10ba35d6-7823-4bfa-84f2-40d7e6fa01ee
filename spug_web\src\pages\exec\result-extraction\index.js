/**
 * 智能结果收集页面
 */
import React, { useState, useEffect } from 'react';
import { 
  Layout, 
  Card, 
  Button, 
  Space, 
  Tag, 
  Progress, 
  message, 
  Tooltip,
  Alert,
  Spin,
  Modal
} from 'antd';
import { 
  ExperimentOutlined, 
  RobotOutlined, 
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  DownloadOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import LogViewer from './LogViewer';
import MetricsPanel from './MetricsPanel';
import TemplateSelector from './TemplateSelector';
import styles from './index.module.less';

const { Header, Content, Sider } = Layout;

function ResultExtraction() {
  const [loading, setLoading] = useState(true);
  const [logContent, setLogContent] = useState('');
  const [executionInfo, setExecutionInfo] = useState({});
  const [extractedMetrics, setExtractedMetrics] = useState([]);
  const [selectedLines, setSelectedLines] = useState([]);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [saving, setSaving] = useState(false);

  // 从URL参数获取执行记录信息
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const executionId = urlParams.get('execution_id');
    const planName = urlParams.get('plan_name');
    const logFile = urlParams.get('log_file');

    if (executionId && planName && logFile) {
      setExecutionInfo({
        id: executionId, // 使用真实的execution_id
        planName: decodeURIComponent(planName),
        logFile: decodeURIComponent(logFile)
      });
      
      loadLogContent(logFile);
    } else {
      message.error('缺少必要的参数');
      setLoading(false);
    }
  }, []);

  const loadLogContent = async (logFile) => {
    try {
      setLoading(true);
      const res = await http.get('/api/file/manager/edit/', { 
        params: { filename: logFile } 
      });
      
      const content = res.content || '日志文件为空';
      setLogContent(content);
      
      // 自动进行AI分析
      await performAIAnalysis(content);
      
    } catch (err) {
      message.error('读取日志文件失败: ' + (err.response?.data?.error || err.message));
      setLogContent('读取日志文件失败');
    } finally {
      setLoading(false);
    }
  };

  const performAIAnalysis = async (content) => {
    try {
      // 模拟AI分析过程
      setExtractionProgress(20);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟识别性能指标
      const suggestions = analyzeLogContent(content);
      setAiSuggestions(suggestions);
      setExtractionProgress(60);
      
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 生成初始提取的指标
      const initialMetrics = suggestions.map(suggestion => ({
        id: Date.now() + Math.random(),
        label: suggestion.label,
        value: suggestion.value,
        unit: suggestion.unit,
        confidence: suggestion.confidence,
        status: suggestion.confidence > 0.8 ? 'confirmed' : 'pending',
        lineNumbers: suggestion.lineNumbers
      }));
      
      setExtractedMetrics(initialMetrics);
      setExtractionProgress(100);
      
      message.success(`AI分析完成，发现 ${suggestions.length} 个潜在指标`);
      
    } catch (err) {
      message.error('AI分析失败: ' + err.message);
      setExtractionProgress(0);
    }
  };

  // 增强的日志内容分析函数 - 支持多种格式识别
  const analyzeLogContent = (content) => {
    const suggestions = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // 模式1: 冒号格式 + 数据 + 单位 (如: AverageUserSpeed: 30.137904 GB/s)
      const pattern1 = /([^:]+):\s*([0-9.,]+)\s*([A-Za-z\/°%]+)/gi;
      let match1;
      while ((match1 = pattern1.exec(line)) !== null) {
        const unit = match1[3];
        const value = match1[2].replace(',', '');
        
        if (isValidUnit(unit) && parseFloat(value) > 0) {
          suggestions.push({
            label: match1[1].trim(),
            value: value,
            unit: unit,
            confidence: 0.95,
            lineNumbers: [index + 1],
            originalText: match1[0]
          });
        }
      }
      
      // 模式2: 括号格式 (如: AverageUserSpeed(GB/s): 30.137904)
      const pattern2 = /([^:\(]+)\(([^)]+)\):\s*([0-9.,]+)/gi;
      let match2;
      while ((match2 = pattern2.exec(line)) !== null) {
        const unit = match2[2];
        const value = match2[3].replace(',', '');
        
        if (isValidUnit(unit) && parseFloat(value) > 0) {
          // 避免重复添加
          const exists = suggestions.some(s => 
            s.lineNumbers.includes(index + 1) && 
            Math.abs(parseFloat(s.value) - parseFloat(value)) < 0.001 &&
            s.unit === unit
          );
          
          if (!exists) {
            suggestions.push({
              label: match2[1].trim(),
              value: value,
              unit: unit,
              confidence: 0.98,
              lineNumbers: [index + 1],
              originalText: match2[0]
            });
          }
        }
      }
      
      // 模式3: 数据 + 单位 在同一行 (如: 30.5 GB/s, 85°C, 150W)
      const pattern3 = /\b([0-9.,]+)\s*([A-Za-z\/°%]{2,})\b/gi;
      let match3;
      while ((match3 = pattern3.exec(line)) !== null) {
        const unit = match3[2];
        const value = match3[1].replace(',', '');
        
        // 只识别明确的单位格式
        if (isValidUnit(unit) && parseFloat(value) > 0) {
          // 避免重复添加（可能已经被前面的模式匹配了）
          const exists = suggestions.some(s => 
            s.lineNumbers.includes(index + 1) && 
            Math.abs(parseFloat(s.value) - parseFloat(value)) < 0.001 &&
            s.unit === unit
          );
          
          if (!exists) {
            const label = inferLabelFromContext(line, unit);
            suggestions.push({
              label: label,
              value: value,
              unit: unit,
              confidence: 0.85,
              lineNumbers: [index + 1],
              originalText: match3[0]
            });
          }
        }
      }
      
      // 模式4: 纯数字行，可能单位在其他地方（图片中的情况）
      const pattern4 = /^\s*([0-9.,]+)\s*$/;
      const match4 = line.match(pattern4);
      if (match4) {
        const value = match4[1].replace(',', '');
        if (parseFloat(value) > 0) {
          // 查找上下文中的单位线索
          const contextLines = [
            lines[Math.max(0, index - 2)],
            lines[Math.max(0, index - 1)], 
            line,
            lines[Math.min(lines.length - 1, index + 1)],
            lines[Math.min(lines.length - 1, index + 2)]
          ].join(' ');
          
          // 在上下文中查找可能的单位和标签
          const unitMatch = contextLines.match(/([A-Za-z\/°%]{2,})/i);
          const labelMatch = contextLines.match(/(Average[A-Za-z]+|[A-Za-z]+Time|[A-Za-z]+Speed|[A-Za-z]+Bandwidth)/i);
          
          if (unitMatch && isValidUnit(unitMatch[1])) {
            const unit = unitMatch[1];
            const label = labelMatch ? labelMatch[1] : inferLabelFromContext(contextLines, unit);
            
            // 避免重复添加
            const exists = suggestions.some(s => 
              s.lineNumbers.includes(index + 1) && 
              Math.abs(parseFloat(s.value) - parseFloat(value)) < 0.001
            );
            
            if (!exists) {
              suggestions.push({
                label: label,
                value: value,
                unit: unit,
                confidence: 0.75,
                lineNumbers: [index + 1],
                originalText: line.trim()
              });
            }
          }
        }
      }
    });
    
    // 去重和清理
    const uniqueSuggestions = [];
    const seen = new Set();
    
    suggestions.forEach(suggestion => {
      const key = `${suggestion.lineNumbers[0]}-${suggestion.value}-${suggestion.unit}`;
      if (!seen.has(key)) {
        seen.add(key);
        uniqueSuggestions.push(suggestion);
      }
    });
    
    // 按置信度和行号排序
    return uniqueSuggestions.sort((a, b) => {
      if (b.confidence !== a.confidence) {
        return b.confidence - a.confidence;
      }
      return a.lineNumbers[0] - b.lineNumbers[0];
    });
  };

  // 验证是否为有效单位
  const isValidUnit = (unit) => {
    const validUnits = [
      // 传输速度
      'GB/s', 'MB/s', 'KB/s', 'Gb/s', 'Mb/s', 'Kb/s',
      // // 温度
      // '°C', '°F', 'C', 'F', 'celsius', 'fahrenheit',
      // 功耗
      'W', 'KW', 'MW', 'watt', 'watts',
      // 性能
      'GFLOPS', 'TFLOPS', 'MFLOPS', 'gflops', 'tflops',
      // // 时间
      // 'ms', 'us', 'ns', 's', 'sec', 'seconds',
      // 频率
      'Hz', 'KHz', 'MHz', 'GHz', 'hz', 'khz', 'mhz', 'ghz',
      // 百分比
      '%', 'percent',
      // 存储
      'GB', 'MB', 'KB', 'TB', 'gb', 'mb', 'kb', 'tb',
      // I/O
      'IOPS', 'iops', 'ops'
    ];
    
    return validUnits.some(validUnit => 
      unit.toLowerCase() === validUnit.toLowerCase()
    );
  };

  // 根据上下文和单位推断标签
  const inferLabelFromContext = (line, unit) => {
    const lowerLine = line.toLowerCase();
    const lowerUnit = unit.toLowerCase();
    
    // GPU/显卡相关
    if (lowerLine.includes('gpu') || lowerLine.includes('cuda') || lowerLine.includes('graphics')) {
      if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s')) {
        return 'GPU内存带宽';
      }
      if (lowerUnit.includes('gflops') || lowerUnit.includes('tflops')) {
        return 'GPU计算性能';
      }
      if (lowerUnit.includes('°c') || lowerUnit.includes('celsius')) {
        return 'GPU温度';
      }
      if (lowerUnit.includes('w') || lowerUnit.includes('watt')) {
        return 'GPU功耗';
      }
      return 'GPU指标';
    }
    
    // PCIe相关
    if (lowerLine.includes('pcie') || lowerLine.includes('pci')) {
      if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s')) {
        return 'PCIe带宽';
      }
      return 'PCIe指标';
    }
    
    // 内存相关
    if (lowerLine.includes('memory') || lowerLine.includes('mem') || lowerLine.includes('ram')) {
      if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s')) {
        return '内存带宽';
      }
      if (lowerUnit.includes('gb') || lowerUnit.includes('mb')) {
        return '内存容量';
      }
      return '内存指标';
    }
    
    // 温度相关
    if (lowerUnit.includes('°c') || lowerUnit.includes('celsius') || lowerUnit.includes('fahrenheit')) {
      if (lowerLine.includes('cpu')) return 'CPU温度';
      if (lowerLine.includes('gpu')) return 'GPU温度';
      return '温度';
    }
    
    // 功耗相关
    if (lowerUnit.includes('w') || lowerUnit.includes('watt')) {
      if (lowerLine.includes('cpu')) return 'CPU功耗';
      if (lowerLine.includes('gpu')) return 'GPU功耗';
      return '功耗';
    }
    
    // 性能相关
    if (lowerUnit.includes('gflops') || lowerUnit.includes('tflops')) {
      return '计算性能';
    }
    
    // 延迟相关
    if (lowerUnit.includes('ms') || lowerUnit.includes('us') || lowerUnit.includes('ns')) {
      if (lowerLine.includes('latency') || lowerLine.includes('delay')) {
        return '延迟';
      }
      return '时间';
    }
    
    // 频率相关
    if (lowerUnit.includes('hz') || lowerUnit.includes('mhz') || lowerUnit.includes('ghz')) {
      if (lowerLine.includes('cpu')) return 'CPU频率';
      if (lowerLine.includes('gpu')) return 'GPU频率';
      if (lowerLine.includes('memory') || lowerLine.includes('mem')) return '内存频率';
      return '频率';
    }
    
    // 百分比相关
    if (lowerUnit.includes('%')) {
      if (lowerLine.includes('usage') || lowerLine.includes('util')) return '使用率';
      if (lowerLine.includes('load')) return '负载';
      return '百分比';
    }
    
    // 传输速度相关
    if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s') || lowerUnit.includes('kb/s')) {
      return '传输速度';
    }
    
    // 通用标签
    return '性能指标';
  };

  const handleLineSelection = (lineNumbers) => {
    setSelectedLines(lineNumbers);
  };

  const handleMetricUpdate = (updatedMetrics) => {
    setExtractedMetrics(updatedMetrics);
    
    // 更新进度
    const confirmedCount = updatedMetrics.filter(m => m.status === 'confirmed').length;
    const totalCount = updatedMetrics.length;
    const progress = totalCount > 0 ? Math.round((confirmedCount / totalCount) * 100) : 0;
    setExtractionProgress(progress);
  };

  const handleSaveResults = async () => {
    const confirmedMetrics = extractedMetrics.filter(m => m.status === 'confirmed');
    
    if (confirmedMetrics.length === 0) {
      message.warning('请至少确认一个指标后再保存');
      return;
    }
    
    try {
      setSaving(true);
      
      // 构建保存数据
      const resultData = {
        execution_id: executionInfo.id,
        plan_name: executionInfo.planName,
        metrics: confirmedMetrics.map(metric => ({
          label: metric.label,
          value: metric.value,
          unit: metric.unit,
          confidence: metric.confidence
        })),
        extraction_time: new Date().toISOString(),
        total_metrics: confirmedMetrics.length
      };
      
      // 调用后端API保存结果
      await http.post('/api/exec/test-results/', resultData);
      
      message.success(`成功保存 ${confirmedMetrics.length} 个性能指标`);
      
      // 显示成功提示
      Modal.success({
        title: '🎉 结果收集完成',
        content: (
          <div>
            <p>已成功提取并保存测试结果：</p>
            <ul>
              {confirmedMetrics.map((metric, index) => (
                <li key={index}>
                  {metric.label}: {metric.value} {metric.unit}
                </li>
              ))}
            </ul>
          </div>
        ),
        onOk: () => {
          // 可以选择关闭页面或跳转到结果展示页面
        }
      });
      
    } catch (err) {
      message.error('保存失败: ' + err.message);
    } finally {
      setSaving(false);
    }
  };

  const confirmedCount = extractedMetrics.filter(m => m.status === 'confirmed').length;
  const totalCount = extractedMetrics.length;

  return (
    <Layout className={styles.extractionLayout}>
      <Header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <ExperimentOutlined style={{ fontSize: '20px', marginRight: '8px' }} />
            <span className={styles.title}>智能结果收集器</span>
            <Tag color="blue" style={{ marginLeft: '12px' }}>
              {executionInfo.planName}
            </Tag>
          </div>
          
          <div className={styles.headerRight}>
            <Space>
              <div className={styles.progressInfo}>
                已确认: {confirmedCount}/{totalCount}
              </div>
              <Progress 
                percent={extractionProgress} 
                size="small" 
                style={{ width: '120px' }}
                strokeColor="#722ed1"
              />
              <Button 
                icon={<RobotOutlined />}
                onClick={() => setTemplateModalVisible(true)}
              >
                智能模板
              </Button>
              <Button 
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveResults}
                loading={saving}
                disabled={confirmedCount === 0}
              >
                保存结果 ({confirmedCount})
              </Button>
            </Space>
          </div>
        </div>
      </Header>
      
      <Layout>
        <Content className={styles.mainContent}>
          <Card 
            title="📋 执行日志分析"
            extra={
              <Space>
                <Tooltip title="重新分析">
                  <Button 
                    icon={<ReloadOutlined />}
                    onClick={() => performAIAnalysis(logContent)}
                    loading={loading}
                  >
                    重新分析
                  </Button>
                </Tooltip>
                <Tooltip title="查看原始日志">
                  <Button icon={<EyeOutlined />}>
                    原始日志
                  </Button>
                </Tooltip>
              </Space>
            }
            className={styles.logCard}
          >
            {loading ? (
              <div className={styles.loadingContainer}>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>正在分析日志内容...</div>
              </div>
            ) : (
              <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                {aiSuggestions.length > 0 && (
                  <Alert
                    message="AI分析结果"
                    description={`发现 ${aiSuggestions.length} 个潜在的性能指标，请在右侧面板中确认或修改`}
                    type="info"
                    showIcon
                    style={{ marginBottom: '16px', flexShrink: 0 }}
                  />
                )}
                
                <div style={{ flex: 1, overflow: 'hidden' }}>
                  <LogViewer
                    content={logContent}
                    suggestions={aiSuggestions}
                    selectedLines={selectedLines}
                    onLineSelection={handleLineSelection}
                  />
                </div>
              </div>
            )}
          </Card>
        </Content>
        
        <Sider width={400} className={styles.sidebar}>
          <MetricsPanel
            metrics={extractedMetrics}
            onMetricsUpdate={handleMetricUpdate}
            suggestions={aiSuggestions}
          />
        </Sider>
      </Layout>
      
      <TemplateSelector
        visible={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        onApply={(template) => {
          // 应用模板逻辑
          setTemplateModalVisible(false);
          message.success(`已应用模板: ${template.name}`);
        }}
      />
    </Layout>
  );
}

export default ResultExtraction; 