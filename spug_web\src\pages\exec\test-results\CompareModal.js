import React, { useState, useEffect } from 'react';
import { Modal, Table, Card, Row, Col, Statistic, Tag, Progress, Select, Button, Empty, Spin } from 'antd';
import { BarChartOutlined, LineChartOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import store from './store';
import styles from './CompareModal.module.less';

const { Option } = Select;

export default function CompareModal({ visible, onCancel, records }) {
  const [loading, setLoading] = useState(false);
  const [compareData, setCompareData] = useState([]);
  const [compareMode, setCompareMode] = useState('metrics'); // metrics | stats
  const [selectedMetric, setSelectedMetric] = useState('');

  useEffect(() => {
    if (visible && records && records.length > 0) {
      loadCompareData();
    }
  }, [visible, records]);

  const loadCompareData = async () => {
    setLoading(true);
    try {
      const promises = records.map(record => store.fetchResultDetail(record.id));
      const results = await Promise.all(promises);
      setCompareData(results);
      
      // 自动选择第一个通用指标进行对比
      if (results.length > 0 && results[0].metrics && results[0].metrics.length > 0) {
        setSelectedMetric(results[0].metrics[0].name);
      }
    } catch (error) {
      console.error('加载对比数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取所有指标名称（用于筛选）
  const getAllMetricNames = () => {
    const metricNames = new Set();
    compareData.forEach(data => {
      if (data.metrics) {
        data.metrics.forEach(metric => {
          metricNames.add(metric.name);
        });
      }
    });
    return Array.from(metricNames);
  };

  // 基础统计对比表格列
  const statsColumns = [
    {
      title: '指标',
      dataIndex: 'metric',
      key: 'metric',
      width: 150,
      fixed: 'left'
    },
    ...records.map((record, index) => ({
      title: (
        <div className={styles.columnHeader}>
          <div className={styles.planName}>{record.plan_name}</div>
          <div className={styles.taskName}>
            {record.task_name ? (
              <a 
                href="/config/task" 
                target="_blank" 
                rel="noopener noreferrer"
                style={{ color: '#1890ff', textDecoration: 'none' }}
                onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
                onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
              >
                {record.task_name}
              </a>
            ) : (
              <span style={{ color: '#999' }}>未关联</span>
            )}
          </div>
        </div>
      ),
      dataIndex: `value_${index}`,
      key: `value_${index}`,
      width: 200,
      render: (value, row) => {
        if (row.type === 'rate') {
          const numValue = parseFloat(value) || 0;
          const color = numValue >= 80 ? '#52c41a' : numValue >= 60 ? '#faad14' : '#ff4d4f';
          return (
            <Progress
              percent={numValue}
              size="small"
              strokeColor={color}
              format={(percent) => `${percent}%`}
            />
          );
        }
        if (row.type === 'confidence') {
          const numValue = Math.round(parseFloat(value) * 100) || 0;
          const color = numValue >= 80 ? 'green' : numValue >= 60 ? 'orange' : 'red';
          return <Tag color={color}>{numValue}%</Tag>;
        }
        return <span className={styles.statValue}>{value}</span>;
      }
    }))
  ];

  // 生成基础统计对比数据
  const generateStatsData = () => {
    return [
      {
        key: 'total_metrics',
        metric: '总指标数',
        type: 'number',
        ...compareData.reduce((acc, data, index) => {
          acc[`value_${index}`] = data.total_metrics;
          return acc;
        }, {})
      },
      {
        key: 'confirmed_metrics',
        metric: '已确认指标',
        type: 'number',
        ...compareData.reduce((acc, data, index) => {
          acc[`value_${index}`] = data.confirmed_metrics;
          return acc;
        }, {})
      },
      {
        key: 'success_rate',
        metric: '成功率',
        type: 'rate',
        ...compareData.reduce((acc, data, index) => {
          acc[`value_${index}`] = data.success_rate;
          return acc;
        }, {})
      },
      {
        key: 'ai_confidence',
        metric: 'AI置信度',
        type: 'confidence',
        ...compareData.reduce((acc, data, index) => {
          acc[`value_${index}`] = data.ai_confidence;
          return acc;
        }, {})
      }
    ];
  };

  // 指标详细对比表格列
  const metricsColumns = [
    {
      title: '测试计划',
      dataIndex: 'planName',
      key: 'planName',
      width: 200,
      fixed: 'left'
    },
    {
      title: '指标值',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (value, record) => (
        <span className={styles.metricValue}>
          {value} {record.unit && <small>{record.unit}</small>}
        </span>
      )
    },
    {
      title: 'AI置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 120,
      render: (confidence) => {
        const value = Math.round((confidence || 0) * 100);
        return (
          <Progress
            percent={value}
            size="small"
            format={(percent) => `${percent}%`}
          />
        );
      }
    },
    {
      title: '相对表现',
      key: 'performance',
      width: 120,
      render: (_, record, index) => {
        if (index === 0) return <span>基准</span>;
        
        // 简单的数值比较逻辑
        const currentValue = parseFloat(record.value) || 0;
        const baseValue = parseFloat(generateMetricsData()[0]?.value) || 0;
        
        if (currentValue > baseValue) {
          const improvement = ((currentValue - baseValue) / baseValue * 100).toFixed(1);
          return (
            <Tag color="green" icon={<ArrowUpOutlined />}>
              +{improvement}%
            </Tag>
          );
        } else if (currentValue < baseValue) {
          const decline = ((baseValue - currentValue) / baseValue * 100).toFixed(1);
          return (
            <Tag color="red" icon={<ArrowDownOutlined />}>
              -{decline}%
            </Tag>
          );
        } else {
          return <Tag color="default">持平</Tag>;
        }
      }
    }
  ];

  // 生成指标详细对比数据
  const generateMetricsData = () => {
    if (!selectedMetric) return [];
    
    return compareData.map((data, index) => {
      const metric = data.metrics?.find(m => m.name === selectedMetric);
      return {
        key: `metric_${index}`,
        planName: data.plan_name,
        taskName: data.task_name,
        value: metric?.value || 'N/A',
        unit: metric?.unit || '',
        confidence: metric?.confidence || 0,
        confirmed: metric?.confirmed || false
      };
    });
  };

  // 计算整体性能提升
  const calculateOverallImprovement = () => {
    if (compareData.length < 2) return null;
    
    const baseSuccess = compareData[0].success_rate || 0;
    const improvements = compareData.slice(1).map(data => {
      const currentSuccess = data.success_rate || 0;
      return currentSuccess - baseSuccess;
    });
    
    const avgImprovement = improvements.reduce((sum, imp) => sum + imp, 0) / improvements.length;
    return avgImprovement;
  };

  if (!records || records.length === 0) return null;

  return (
    <Modal
      title={`测试结果对比 (${records.length}个结果)`}
      visible={visible}
      onCancel={onCancel}
      width={1400}
      footer={null}
      bodyStyle={{ padding: '16px' }}
    >
      <Spin spinning={loading}>
        <div className={styles.compareContent}>
          {/* 对比模式选择 */}
          <Card className={styles.modeCard}>
            <Row gutter={16} align="middle">
              <Col span={12}>
                <span className={styles.modeLabel}>对比模式：</span>
                <Select
                  value={compareMode}
                  onChange={setCompareMode}
                  style={{ width: 200 }}
                >
                  <Option value="stats">基础统计对比</Option>
                  <Option value="metrics">指标详细对比</Option>
                </Select>
              </Col>
              {compareMode === 'metrics' && (
                <Col span={12}>
                  <span className={styles.modeLabel}>选择指标：</span>
                  <Select
                    value={selectedMetric}
                    onChange={setSelectedMetric}
                    style={{ width: 200 }}
                    placeholder="选择要对比的指标"
                  >
                    {getAllMetricNames().map(name => (
                      <Option key={name} value={name}>{name}</Option>
                    ))}
                  </Select>
                </Col>
              )}
            </Row>
          </Card>

          {/* 整体性能摘要 */}
          <Row gutter={16} className={styles.summaryRow}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="对比结果数"
                  value={records.length}
                  prefix={<BarChartOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="平均成功率"
                  value={
                    compareData.length > 0
                      ? (compareData.reduce((sum, d) => sum + (d.success_rate || 0), 0) / compareData.length).toFixed(1)
                      : 0
                  }
                  suffix="%"
                  prefix={<LineChartOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="性能变化"
                  value={calculateOverallImprovement()?.toFixed(1) || 0}
                  suffix="%"
                  prefix={
                    (calculateOverallImprovement() || 0) >= 0 
                      ? <ArrowUpOutlined /> 
                      : <ArrowDownOutlined />
                  }
                  valueStyle={{
                    color: (calculateOverallImprovement() || 0) >= 0 ? '#3f8600' : '#cf1322'
                  }}
                />
              </Card>
            </Col>
          </Row>

          {/* 对比表格 */}
          <Card title={compareMode === 'stats' ? '基础统计对比' : `指标对比: ${selectedMetric}`}>
            {compareData.length > 0 ? (
              <Table
                columns={compareMode === 'stats' ? statsColumns : metricsColumns}
                dataSource={compareMode === 'stats' ? generateStatsData() : generateMetricsData()}
                pagination={false}
                scroll={{ x: 800 }}
                size="middle"
              />
            ) : (
              <Empty description="暂无对比数据" />
            )}
          </Card>

          {/* 对比结论 */}
          {compareData.length > 1 && (
            <Card title="对比结论" className={styles.conclusionCard}>
              <div className={styles.conclusion}>
                <p>
                  基于 <strong>{records.length}</strong> 个测试结果的对比分析：
                </p>
                <ul>
                  <li>
                    最高成功率：<strong>{Math.max(...compareData.map(d => d.success_rate || 0)).toFixed(1)}%</strong>
                    （{compareData.find(d => d.success_rate === Math.max(...compareData.map(d => d.success_rate || 0)))?.plan_name}）
                  </li>
                  <li>
                    最高AI置信度：<strong>{Math.max(...compareData.map(d => (d.ai_confidence || 0) * 100)).toFixed(1)}%</strong>
                  </li>
                  <li>
                    总确认指标：<strong>{compareData.reduce((sum, d) => sum + (d.confirmed_metrics || 0), 0)}</strong> 个
                  </li>
                </ul>
              </div>
            </Card>
          )}
        </div>
      </Spin>
    </Modal>
  );
} 