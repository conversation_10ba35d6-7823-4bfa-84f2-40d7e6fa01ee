#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试API响应格式
"""

import json
import requests

def test_lazy_load_tree_api():
    """测试本地仓库懒加载API"""
    print("=" * 60)
    print("测试本地仓库懒加载API")
    print("=" * 60)
    
    url = "http://localhost:8000/api/model-storage/lazy-load-tree/"
    params = {
        'path': '/HDD_Raid/SVN_MODEL_REPO',
        'root': 'true'
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"JSON数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return True
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                return False
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_file_tree_compare_api():
    """测试远程仓库文件树对比API"""
    print("\n" + "=" * 60)
    print("测试远程仓库文件树对比API")
    print("=" * 60)
    
    url = "http://localhost:8000/api/model-storage/file-tree-compare/"
    params = {
        'first_level': 'true'
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"JSON数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 检查数据格式
                if isinstance(data, list):
                    print(f"返回数组，长度: {len(data)}")
                    for i, item in enumerate(data):
                        print(f"项目 {i+1}: {item.get('title', 'N/A')} (key: {item.get('key', 'N/A')})")
                else:
                    print(f"返回对象，类型: {type(data)}")
                
                return True
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                return False
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_with_auth():
    """使用认证测试API"""
    print("\n" + "=" * 60)
    print("使用认证测试API")
    print("=" * 60)
    
    # 首先尝试登录获取session
    login_url = "http://localhost:8000/api/account/login/"
    login_data = {
        'username': 'admin',  # 替换为实际的用户名
        'password': 'spug.dev'  # 替换为实际的密码
    }
    
    session = requests.Session()
    
    try:
        # 尝试登录
        login_response = session.post(login_url, json=login_data)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            print("登录成功")
            
            # 测试远程仓库API
            api_url = "http://localhost:8000/api/model-storage/file-tree-compare/"
            params = {'first_level': 'true'}
            
            api_response = session.get(api_url, params=params)
            print(f"API状态码: {api_response.status_code}")
            print(f"API响应: {api_response.text}")
            
            if api_response.status_code == 200:
                try:
                    data = api_response.json()
                    print(f"API数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    return False
            else:
                print(f"API调用失败")
                return False
        else:
            print(f"登录失败: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"认证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("API响应格式测试")
    print("=" * 60)
    
    tests = [
        ("本地仓库懒加载API", test_lazy_load_tree_api),
        ("远程仓库文件树对比API", test_file_tree_compare_api),
        ("认证API测试", test_with_auth),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 所有测试通过")
    else:
        print("\n❌ 部分测试失败")
