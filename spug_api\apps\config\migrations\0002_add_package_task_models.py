# Generated manually for package and task management
from django.db import migrations, models
import django.db.models.deletion
from libs import ModelMixin, human_datetime


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
        ('config', '0001_initial'),
    ]

    operations = [
        # 创建配套表
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='配套名称')),
                ('key', models.CharField(max_length=50, verbose_name='配套标识')),
                ('desc', models.CharField(max_length=255, null=True, verbose_name='配套描述')),
                ('sort_id', models.IntegerField(db_index=True, default=0)),
                ('created_at', models.Char<PERSON>ield(default=human_datetime, max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
            ],
            options={
                'db_table': 'packages',
                'ordering': ('-sort_id',),
            },
            bases=(models.Model, ModelMixin),
        ),
        
        # 创建任务版本配套表
        migrations.CreateModel(
            name='TaskVersionPackage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.IntegerField(verbose_name='任务ID')),
                ('version', models.CharField(max_length=50, verbose_name='版本号')),
                ('file_path', models.TextField(verbose_name='文件路径')),
                ('desc', models.CharField(max_length=255, null=True, verbose_name='备注')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.CharField(default=human_datetime, max_length=20)),
                ('updated_at', models.CharField(max_length=20, null=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='config.Package', verbose_name='配套')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.User')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User')),
            ],
            options={
                'db_table': 'task_version_packages',
                'ordering': ('-id',),
            },
            bases=(models.Model, ModelMixin),
        ),
        
        # 为Config表添加新字段
        migrations.AddField(
            model_name='config',
            name='package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='config.Package', verbose_name='关联配套'),
        ),
        
        # 修改Config表的env字段允许为空
        migrations.AlterField(
            model_name='config',
            name='env',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='config.Environment'),
        ),
        
        # 修改ConfigHistory表添加package_id字段
        migrations.AddField(
            model_name='confighistory',
            name='package_id',
            field=models.IntegerField(null=True),
        ),
        
        # 修改ConfigHistory表的env_id字段允许为空
        migrations.AlterField(
            model_name='confighistory',
            name='env_id',
            field=models.IntegerField(null=True),
        ),
        
        # 为TaskVersionPackage添加唯一约束
        migrations.AlterUniqueTogether(
            name='taskversionpackage',
            unique_together={('task_id', 'package', 'version')},
        ),
    ] 