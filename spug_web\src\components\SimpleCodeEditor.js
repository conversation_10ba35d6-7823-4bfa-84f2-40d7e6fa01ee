/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

export default function SimpleCodeEditor(props) {
  const {
    mode = 'sh',
    theme = 'tomorrow',
    width = '100%',
    height = '80px',
    value,
    onChange,
    placeholder,
    style = {},
    ...otherProps
  } = props;

  const editorStyle = {
    fontFamily: 'Monaco, Courier New, Courier, monospace',
    fontSize: '13px',
    lineHeight: '1.4',
    backgroundColor: theme === 'tomorrow' ? '#ffffff' : '#1e1e1e',
    color: theme === 'tomorrow' ? '#4d4d4c' : '#d4d4d4',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    width,
    height,
    resize: 'vertical',
    padding: '8px 12px',
    ...style
  };

  return (
    <TextArea
      value={value}
      onChange={(e) => onChange && onChange(e.target.value)}
      placeholder={placeholder}
      style={editorStyle}
      rows={Math.max(3, parseInt(height) / 20)}
      {...otherProps}
    />
  );
} 