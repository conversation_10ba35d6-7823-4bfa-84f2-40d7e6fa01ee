#!/bin/bash

# XPU Management Tool
# Version: 1.0
# Author: Your Name
# Description: A user-friendly tool for XPU driver/firmware management and testing

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 全局变量
XPU_TOOLS_PATH="/usr/local/xpu/tools"
LOG_DIR="/var/log/xpu_tool"
CURRENT_USER=$(whoami)

# 初始化日志系统
init_logging() {
    mkdir -p "$LOG_DIR"
    LOG_FILE="$LOG_DIR/xpu_tool_$(date +%Y%m%d_%H%M%S).log"
    touch "$LOG_FILE"
    chmod 644 "$LOG_FILE"
}

# 记录日志
log() {
    local msg="$1"
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - $msg" | tee -a "$LOG_FILE"
}

# 检查root权限
check_root() {
    if [ "$CURRENT_USER" != "root" ]; then
        log "${RED}错误：此脚本需要root权限执行${NC}"
        exit 1
    fi
}

# 检查XPU驱动是否加载
check_driver_loaded() {
    if ! lsmod | grep -q kunlun; then
        log "${YELLOW}警告：未检测到kunlun驱动加载${NC}"
        return 1
    fi
    return 0
}

# 检查xpu-smi工具
check_xpu_smi() {
    if ! command -v xpu-smi &> /dev/null; then
        log "${RED}错误：未找到xpu-smi工具${NC}"
        return 1
    fi
    return 0
}

# 显示XPU信息
show_xpu_info() {
    log "${GREEN}=== 当前XPU信息 ===${NC}"
    if ! check_xpu_smi; then
        return
    fi
    
    log "驱动版本信息:"
    cat /usr/local/xpu/version.txt 2>/dev/null || log "${YELLOW}未找到version.txt${NC}"
    echo
    cat /proc/kunlun/version 2>/dev/null || log "${YELLOW}未找到/proc/kunlun/version${NC}"
    echo
    modinfo kunlun 2>/dev/null || log "${YELLOW}无法获取modinfo信息${NC}"
    echo
    
    log "固件版本信息:"
    xpu-smi | grep -A 4 "Firmware Version" || log "${YELLOW}无法获取固件信息${NC}"
}

# 安装驱动
install_driver() {
    local driver_file="$1"
    
    if [ -z "$driver_file" ]; then
        log "${YELLOW}请输入驱动文件路径: ${NC}"
        read -r driver_file
    fi
    
    if [ ! -f "$driver_file" ]; then
        log "${RED}错误：驱动文件不存在${NC}"
        return 1
    fi
    
    log "${GREEN}正在安装驱动: $driver_file${NC}"
    
    # 检查并卸载旧驱动
    if check_driver_loaded; then
        log "${YELLOW}检测到已加载驱动，尝试卸载...${NC}"
        rmmod kunlun || modprobe -r kunlun || {
            log "${RED}无法卸载现有驱动${NC}"
            return 1
        }
    fi
    
    # 执行安装
    chmod +x "$driver_file"
    if "$driver_file"; then
        log "${GREEN}驱动安装成功${NC}"
        show_xpu_info
        return 0
    else
        log "${RED}驱动安装失败${NC}"
        return 1
    fi
}

# 升级固件
upgrade_firmware() {
    local ota_file="$1"
    
    if [ -z "$ota_file" ]; then
        log "${YELLOW}请输入固件文件路径: ${NC}"
        read -r ota_file
    fi
    
    if [ ! -f "$ota_file" ]; then
        log "${RED}错误：固件文件不存在${NC}"
        return 1
    fi
    
    if [ ! -d "$XPU_TOOLS_PATH" ]; then
        log "${RED}错误：未找到XPU工具目录${NC}"
        return 1
    fi
    
    log "${GREEN}正在升级固件: $ota_file${NC}"
    
    # 获取设备数量
    local device_count
    device_count=$(xpu-smi -l | grep -c "Device ID")
    
    if [ "$device_count" -eq 0 ]; then
        log "${RED}未检测到XPU设备${NC}"
        return 1
    fi
    
    log "检测到 $device_count 个XPU设备"
    
    # 逐个升级
    for ((i=0; i<device_count; i++)); do
        log "正在升级设备 $i..."
        if "$XPU_TOOLS_PATH"/mcu_util_refactor "$i" ota "$ota_file"; then
            log "设备 $i 固件升级成功"
        else
            log "${RED}设备 $i 固件升级失败${NC}"
            return 1
        fi
    done
    
    log "${GREEN}所有设备固件升级完成，请重启系统使更改生效${NC}"
    return 0
}

# PCIe带宽测试
test_pcie_bandwidth() {
    local device_id="$1"
    local size="$2"
    
    if [ -z "$device_id" ]; then
        log "${YELLOW}请输入要测试的设备ID (0开始): ${NC}"
        read -r device_id
    fi
    
    if [ -z "$size" ]; then
        log "${YELLOW}请选择测试数据大小:${NC}"
        log "1) 16M"
        log "2) 32M"
        log "3) 64M"
        log "4) 128M"
        read -r -p "请选择(1-4): " size_choice
        
        case $size_choice in
            1) size="0x1000000" ;;
            2) size="0x2000000" ;;
            3) size="0x4000000" ;;
            4) size="0x8000000" ;;
            *) 
                log "${RED}无效选择${NC}"
                return 1
                ;;
        esac
    fi
    
    if [ ! -d "$XPU_TOOLS_PATH" ]; then
        log "${RED}错误：未找到XPU工具目录${NC}"
        return 1
    fi
    
    log "${GREEN}正在测试设备 $device_id 的PCIe带宽，数据大小 $size${NC}"
    
    cd "$XPU_TOOLS_PATH" || {
        log "${RED}无法进入工具目录${NC}"
        return 1
    }
    
    ./test_dma --loop 100 "$device_id" "$size"
}

# C2C带宽测试
test_c2c_bandwidth() {
    if [ ! -d "$XPU_TOOLS_PATH" ]; then
        log "${RED}错误：未找到XPU工具目录${NC}"
        return 1
    fi
    
    log "${GREEN}正在执行C2C带宽测试${NC}"
    
    cd "$XPU_TOOLS_PATH" || {
        log "${RED}无法进入工具目录${NC}"
        return 1
    }
    
    # 检查是否需要初始化
    local driver_version
    driver_version=$(cat /usr/local/xpu/version.txt | awk '{print $3}')
    if [ "$(echo "$driver_version < 5.0.1" | bc)" -eq 1 ]; then
        log "${YELLOW}驱动版本低于5.0.1，需要手动初始化C2C${NC}"
        ./xpulink_init || {
            log "${RED}C2C初始化失败${NC}"
            return 1
        }
    fi
    
    ./test_xpulink.sh
}

# 主菜单
main_menu() {
    while true; do
        clear
        log "${GREEN}=== XPU管理工具 ===${NC}"
        log "1. 显示XPU信息"
        log "2. 安装驱动"
        log "3. 升级固件"
        log "4. PCIe带宽测试"
        log "5. C2C带宽测试"
        log "6. 退出"
        echo
        read -r -p "请选择操作(1-6): " choice
        
        case $choice in
            1)
                show_xpu_info
                ;;
            2)
                install_driver ""
                ;;
            3)
                upgrade_firmware ""
                ;;
            4)
                test_pcie_bandwidth "" ""
                ;;
            5)
                test_c2c_bandwidth
                ;;
            6)
                log "感谢使用，再见！"
                exit 0
                ;;
            *)
                log "${RED}无效选择，请重新输入${NC}"
                ;;
        esac
        
        read -r -p "按Enter键继续..."
    done
}

# 主函数
main() {
    check_root
    init_logging
    log "${GREEN}XPU管理工具启动${NC}"
    log "当前用户: $CURRENT_USER"
    log "日志文件: $LOG_FILE"
    
    main_menu
}

# 执行主函数
main