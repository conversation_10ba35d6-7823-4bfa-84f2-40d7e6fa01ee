#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试懒加载功能
"""

import requests
import json

def test_local_root():
    """测试本地仓库根目录"""
    print("=" * 60)
    print("测试本地仓库根目录")
    print("=" * 60)
    
    url = "http://localhost:8000/api/model-storage/lazy-load-tree/"
    params = {
        'path': '/HDD_Raid/SVN_MODEL_REPO',
        'root': 'true'
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据结构: {type(data)}")
            
            if 'data' in data and 'children' in data['data']:
                children = data['data']['children']
                print(f"根目录子项数量: {len(children)}")
                
                for i, child in enumerate(children):
                    print(f"{i+1}. {child.get('title', 'N/A')} (key: {child.get('key', 'N/A')})")
                    if child.get('data'):
                        print(f"   路径: {child['data'].get('path', 'N/A')}")
                        print(f"   类型: {child['data'].get('type', 'N/A')}")
                
                return True
            else:
                print("❌ 数据格式不正确")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_local_lazy_load():
    """测试本地仓库懒加载"""
    print("\n" + "=" * 60)
    print("测试本地仓库懒加载")
    print("=" * 60)
    
    # 测试加载Model目录
    url = "http://localhost:8000/api/model-storage/lazy-load-tree/"
    params = {
        'path': '/HDD_Raid/SVN_MODEL_REPO/Model'
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据结构: {type(data)}")
            
            if 'data' in data and 'children' in data['data']:
                children = data['data']['children']
                print(f"Model目录子项数量: {len(children)}")
                
                for i, child in enumerate(children[:5]):  # 只显示前5个
                    print(f"{i+1}. {child.get('title', 'N/A')} (key: {child.get('key', 'N/A')})")
                    if child.get('data'):
                        print(f"   路径: {child['data'].get('path', 'N/A')}")
                        print(f"   类型: {child['data'].get('type', 'N/A')}")
                
                return True
            else:
                print("❌ 数据格式不正确")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_remote_root():
    """测试远程仓库根目录"""
    print("\n" + "=" * 60)
    print("测试远程仓库根目录")
    print("=" * 60)
    
    url = "http://localhost:8000/api/model-storage/file-tree-compare/"
    params = {
        'first_level': 'true'
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据结构: {type(data)}")
            
            if 'data' in data:
                children = data['data']
                print(f"远程根目录子项数量: {len(children)}")
                
                for i, child in enumerate(children):
                    print(f"{i+1}. {child.get('title', 'N/A')} (key: {child.get('key', 'N/A')})")
                    if child.get('data'):
                        print(f"   路径: {child['data'].get('path', 'N/A')}")
                        print(f"   类型: {child['data'].get('type', 'N/A')}")
                
                return True
            else:
                print("❌ 数据格式不正确")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_remote_lazy_load():
    """测试远程仓库懒加载"""
    print("\n" + "=" * 60)
    print("测试远程仓库懒加载")
    print("=" * 60)
    
    # 测试加载DataSet目录
    url = "http://localhost:8000/api/model-storage/remote-lazy-load/"
    params = {
        'path': 'DataSet'
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据结构: {type(data)}")
            
            if 'data' in data:
                children = data['data']
                print(f"DataSet目录子项数量: {len(children)}")
                
                for i, child in enumerate(children[:5]):  # 只显示前5个
                    print(f"{i+1}. {child.get('title', 'N/A')} (key: {child.get('key', 'N/A')})")
                    if child.get('data'):
                        print(f"   路径: {child['data'].get('path', 'N/A')}")
                        print(f"   类型: {child['data'].get('type', 'N/A')}")
                
                return True
            else:
                print("❌ 数据格式不正确")
                print(f"实际数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("懒加载功能测试")
    print("=" * 60)
    
    tests = [
        ("本地仓库根目录", test_local_root),
        ("本地仓库懒加载", test_local_lazy_load),
        ("远程仓库根目录", test_remote_root),
        ("远程仓库懒加载", test_remote_lazy_load),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 所有测试通过")
    else:
        print("\n❌ 部分测试失败")
