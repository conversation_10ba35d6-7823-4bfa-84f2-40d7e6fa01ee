2025-06-11 18:09:42 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 18:09:42 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:29:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:29:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:29:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:29:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-11 20:29:25 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:29:25 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:29:26 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:29:26 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-11 20:32:00 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:32:00 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:32:01 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:32:01 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-11 20:32:01 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:68" - __init__ - ERROR - funboost初始化失败: Field 'broker_kind' defined on a base class was overridden by a non-annotated attribute. All field definitions, including overrides, require a type annotation.

For further information visit https://errors.pydantic.dev/2.11/u/model-field-overridden
2025-06-11 20:32:17 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:17 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:20 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:22 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:32:22 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:199" - refresh_cache - ERROR - 刷新缓存失败: database is locked
2025-06-11 20:38:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 20:38:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 20:38:12 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 20:38:12 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
2025-06-11 20:38:12 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:68" - __init__ - ERROR - funboost初始化失败: Field 'broker_kind' defined on a base class was overridden by a non-annotated attribute. All field definitions, including overrides, require a type annotation.

For further information visit https://errors.pydantic.dev/2.11/u/model-field-overridden
2025-06-11 20:38:21 - apps.file.remote_cache_service - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:270" - cache_entire_file_tree - INFO - 开始缓存整个文件树: GPU-P800 (ID: 2)
