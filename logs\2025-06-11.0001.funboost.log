2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m18:09:41[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/Documents/GitHub/spug 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录  的funboost_config.py文件，
    如果没有 /funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/Documents/GitHub/spug 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/Documents/GitHub/spug/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-11 18:09:41 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001E8D8A95890>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
