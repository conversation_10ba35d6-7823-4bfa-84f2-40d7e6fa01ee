# Generated by Django 2.2.28 on 2025-06-11 16:52

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('file', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RemoteFolderCache',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('path', models.CharField(default='', max_length=1000, verbose_name='相对路径')),
                ('files_data', models.TextField(verbose_name='文件列表JSON数据')),
                ('cache_time', models.DateTimeField(auto_now=True, verbose_name='缓存时间')),
                ('is_valid', models.BooleanField(default=True, verbose_name='缓存是否有效')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('remote_folder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='file.RemoteFolder', verbose_name='远程文件夹')),
            ],
            options={
                'verbose_name': '远程文件夹缓存',
                'verbose_name_plural': '远程文件夹缓存',
                'db_table': 'remote_folder_cache',
                'unique_together': {('remote_folder', 'path')},
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
