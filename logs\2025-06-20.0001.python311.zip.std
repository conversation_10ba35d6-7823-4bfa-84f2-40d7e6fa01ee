2025-06-20 14:17:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-20 14:17:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-20 14:17:37 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-20 14:17:37 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-20 14:17:37 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-20 14:17:37 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
TestResult查询成功，共有 1 条记录
TestResult使用select_related查询成功，共有 1 条记录
第一条记录信息:
  ID: 2
  计划名称: P900
  创建者ID: 1
  创建时间: 2025-06-18 08:51:16
  执行记录ID: 1
2025-06-20 14:17:37 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\test_fix.py", line 36, in <module>
    print("✅ 所有测试通过！修复成功！")
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\monkey_sys_std.py", line 70, in monkey_sys_stdout
    stdout_raw(msg)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\test_fix.py", line 39, in <module>
    print(f"❌ 测试失败: {e}")
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\monkey_sys_std.py", line 70, in monkey_sys_stdout
    stdout_raw(msg)
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 0: illegal multibyte sequence
2025-06-20 14:17:54  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-20 14:17:54  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-20 14:17:54 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-20 14:17:54 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-20 14:17:54 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-20 14:17:54 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
TestResult查询成功，共有 1 条记录
TestResult使用select_related查询成功，共有 1 条记录
第一条记录信息:
  ID: 2
  计划名称: P900
  创建者ID: 1
  创建时间: 2025-06-18 08:51:16
  执行记录ID: 1
SUCCESS: 所有测试通过！修复成功！
2025-06-20 14:18:12  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-20 14:18:12  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-20 14:18:12 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-20 14:18:12 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-20 14:18:12 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-20 14:18:12 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
Total records: 1
Test passed
2025-06-20 14:49:33  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-20 14:49:33  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-20 14:49:34 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-20 14:49:34 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-20 14:49:34 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:44" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-20 14:49:34 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:20" - ready - INFO - 远程文件夹缓存服务已加载
=== 测试结果修改验证 ===
1. 测试结果查询: 共 1 条记录
2. 指标数据测试:
   - 测试计划: P900
   - 关联任务: 
   - 总指标数: 4
   - 已确认指标: 0
   - 指标详情: 4 个指标
     1. AverageUserSpeed: 30.137904
     2. AveragePCIeSpeed: 30.284781
     3. AverageUserSpeed: 58.241268
3. 数据库操作测试: 通过
4. 修改验证: 成功完成!

前端修改点:
- ExecutionHistory.js: 添加了执行记录ID列
- test-results/index.js: 添加了编辑功能和指标名称列
- test-results/store.js: 添加了updateResult方法
- exec/views.py: 添加了PUT方法和指标数据返回
