import sqlite3
import os

def main():
    db_file = 'db.sqlite3'
    
    if not os.path.exists(db_file):
        print(f"数据库文件 {db_file} 不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='exec_test_plan_executions'
        """)
        
        if not cursor.fetchone():
            print("表 exec_test_plan_executions 不存在")
            return False
        
        # 检查字段是否存在
        cursor.execute('PRAGMA table_info(exec_test_plan_executions)')
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        
        if 'step_logs_folder' in column_names:
            print("step_logs_folder 字段已存在")
            return True
        
        # 添加字段
        cursor.execute("""
            ALTER TABLE exec_test_plan_executions 
            ADD COLUMN step_logs_folder VARCHAR(255)
        """)
        
        conn.commit()
        
        # 验证字段是否添加成功
        cursor.execute('PRAGMA table_info(exec_test_plan_executions)')
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'step_logs_folder' in column_names:
            print("SUCCESS: step_logs_folder 字段添加成功")
            return True
        else:
            print("FAILED: step_logs_folder 字段添加失败")
            return False
            
    except Exception as e:
        print(f"错误: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1) 