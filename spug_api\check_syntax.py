#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查Python文件语法
"""

import ast
import sys
import os

def check_file_syntax(file_path):
    """检查单个文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print(f"✅ {file_path} - 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {file_path} - 语法错误:")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path} - 检查失败: {e}")
        return False

def main():
    """主函数"""
    files_to_check = [
        'apps/model_storage/views.py',
        'apps/model_storage/models.py',
        'apps/model_storage/urls.py',
        'apps/model_storage/exceptions.py',
        'apps/model_storage/file_utils.py',
        'apps/model_storage/config.py'
    ]
    
    print("检查Python文件语法...")
    print("=" * 50)
    
    passed = 0
    total = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            total += 1
            if check_file_syntax(file_path):
                passed += 1
        else:
            print(f"⚠️  {file_path} - 文件不存在")
    
    print("=" * 50)
    print(f"语法检查结果: {passed}/{total} 文件通过")
    
    if passed == total:
        print("🎉 所有文件语法正确!")
    else:
        print("⚠️  存在语法错误，请修复后重试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
