# Generated by Django 2.2.28 on 2025-06-10 10:11

from django.db import migrations, models
import django.db.models.deletion
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
        ('exec', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TestPlan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='计划名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='计划描述')),
                ('category', models.CharField(blank=True, max_length=50, null=True, verbose_name='计划分类')),
                ('commands', models.TextField(default='[]', verbose_name='执行步骤JSON')),
                ('files', models.TextField(default='[]', verbose_name='关联文件JSON')),
                ('steps', models.TextField(default='[]', verbose_name='步骤配置JSON')),
                ('created_at', models.CharField(default=libs.utils.human_datetime, max_length=20, verbose_name='创建时间')),
                ('updated_at', models.CharField(max_length=20, null=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User', verbose_name='创建人')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='account.User', verbose_name='更新人')),
            ],
            options={
                'verbose_name': '测试计划',
                'verbose_name_plural': '测试计划',
                'db_table': 'exec_test_plans',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='TestPlanExecution',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_id', models.IntegerField(default=0, verbose_name='测试计划ID')),
                ('plan_name', models.CharField(default='未知计划', max_length=100, verbose_name='测试计划名称')),
                ('token', models.CharField(max_length=32, unique=True, verbose_name='执行令牌')),
                ('host_ids', models.TextField(verbose_name='目标主机JSON')),
                ('executor_id', models.IntegerField(default=1, verbose_name='执行用户ID')),
                ('executor_name', models.CharField(default='系统管理员', max_length=50, verbose_name='执行用户名')),
                ('status', models.CharField(default='running', max_length=20, verbose_name='执行状态')),
                ('start_time', models.CharField(default=libs.utils.human_datetime, max_length=20, verbose_name='开始时间')),
                ('end_time', models.CharField(max_length=20, null=True, verbose_name='结束时间')),
                ('total_steps', models.IntegerField(default=0, verbose_name='总步骤数')),
                ('completed_steps', models.IntegerField(default=0, verbose_name='已完成步骤')),
                ('log_file', models.CharField(blank=True, max_length=255, null=True, verbose_name='日志文件路径')),
                ('step_logs_folder', models.CharField(blank=True, max_length=255, null=True, verbose_name='步骤日志文件夹路径')),
                ('test_plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='exec.TestPlan', verbose_name='测试计划')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='account.User', verbose_name='执行用户')),
            ],
            options={
                'verbose_name': '测试计划执行记录',
                'verbose_name_plural': '测试计划执行记录',
                'db_table': 'exec_test_plan_executions',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
        migrations.CreateModel(
            name='TestPlanStepResult',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_type', models.CharField(max_length=20, verbose_name='步骤类型')),
                ('step_name', models.CharField(max_length=200, verbose_name='步骤名称')),
                ('command', models.TextField(blank=True, null=True, verbose_name='执行命令')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='文件路径')),
                ('start_time', models.CharField(default=libs.utils.human_datetime, max_length=20, verbose_name='开始时间')),
                ('end_time', models.CharField(max_length=20, null=True, verbose_name='结束时间')),
                ('exit_code', models.IntegerField(null=True, verbose_name='退出码')),
                ('output', models.TextField(default='', verbose_name='输出内容')),
                ('status', models.CharField(default='running', max_length=20, verbose_name='执行状态')),
                ('execution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exec.TestPlanExecution', verbose_name='执行记录')),
            ],
            options={
                'verbose_name': '测试计划步骤结果',
                'verbose_name_plural': '测试计划步骤结果',
                'db_table': 'exec_test_plan_step_results',
                'ordering': ('id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
