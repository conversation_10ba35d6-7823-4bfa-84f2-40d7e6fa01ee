from django.db import models
from django.utils import timezone
from libs.mixins import ModelMixin


class ReleasePlan(models.Model, ModelMixin):
    """发布计划模型"""
    CARD_CHOICES = [
        ('P800', 'P800'),
        ('P800-PCIe', 'P800-PCIe'),
        ('RG800', 'RG800'),
    ]
    
    STATUS_CHOICES = [
        ('complete', '已完成'),
        ('partial', '部分缺失'),
        ('missing', '缺失'),
        ('inProgress', '进行中'),
        ('released', '已发布'),
        ('delayed', '延期'),
        ('preparing', '准备中')
    ]
    
    card_model = models.CharField('卡型号', max_length=64)
    model_name = models.CharField('模型名称', max_length=128)
    release_date = models.DateField('计划发布时间')
    model_status = models.CharField('Model状态', max_length=32, default='inProgress')
    vendor_status = models.CharField('Vendor状态', max_length=32, default='inProgress')
    overall_status = models.CharField('总体状态', max_length=32, default='preparing')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'model_storage_release_plans'
        verbose_name = '发布计划'
        verbose_name_plural = '发布计划'
        ordering = ('-created_at',)
    
    def __str__(self):
        return f"{self.card_model} - {self.model_name}"


class ServerMetrics(models.Model, ModelMixin):
    """服务器指标模型"""
    disk_usage = models.FloatField('磁盘使用率')
    cpu_usage = models.FloatField('CPU使用率')
    memory_usage = models.FloatField('内存使用率', default=0.0)
    network_upload = models.CharField('网络上传', max_length=32, default='0MB')
    network_download = models.CharField('网络下载', max_length=32, default='0MB')
    network_total = models.CharField('网络总计', max_length=32, default='0MB')
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'model_storage_server_metrics'
        verbose_name = '服务器指标'
        verbose_name_plural = '服务器指标'
        ordering = ('-timestamp',)
    
    def __str__(self):
        return f"服务器指标 - {self.timestamp}"


class FileStatus(models.Model, ModelMixin):
    """文件状态模型"""
    STATUS_CHOICES = [
        ('synced', '已同步'),
        ('missing', '缺失'),
        ('outdated', '过期'),
    ]
    
    TYPE_CHOICES = [
        ('folder', '文件夹'),
        ('file', '文件'),
    ]
    
    file_path = models.CharField('文件路径', max_length=512)
    file_type = models.CharField('文件类型', max_length=32)
    status = models.CharField('状态', max_length=32)
    size = models.BigIntegerField('文件大小', null=True, blank=True)
    md5_hash = models.CharField('MD5哈希', max_length=32, null=True, blank=True)
    last_modified = models.DateTimeField('最后修改时间', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'model_storage_file_status'
        verbose_name = '文件状态'
        verbose_name_plural = '文件状态'
        ordering = ('file_path',)
    
    def __str__(self):
        return f"{self.file_path}" 