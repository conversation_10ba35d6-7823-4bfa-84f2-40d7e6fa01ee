# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.views.generic import View
from django.conf import settings
from django.db import close_old_connections
from django_redis import get_redis_connection
from apps.exec.models import Transfer
from apps.account.utils import has_host_perm
from apps.host.models import Host
from apps.setting.utils import AppSetting
from libs import json_response, JsonParser, Argument, auth
from libs.utils import str_decode, human_seconds_time
from concurrent import futures
from threading import Thread
import subprocess
import tempfile
import uuid
import json
import time
import os
import shutil
import platform


# 检测操作系统
IS_WINDOWS = platform.system().lower() == 'windows'


class TransferView(View):
    @auth('exec.transfer.do')
    def get(self, request):
        records = Transfer.objects.filter(user=request.user)
        return json_response([x.to_view() for x in records])

    @auth('exec.transfer.do')
    def post(self, request):
        data = request.POST.get('data')
        form, error = JsonParser(
            Argument('host', required=False),
            Argument('dst_dir', help='请输入目标路径'),
            Argument('host_ids', type=list, filter=lambda x: len(x), help='请选择目标主机'),
            Argument('managed_files', type=list, required=False),
            Argument('remote_files', type=list, required=False),
        ).parse(data)
        if error is None:
            if not has_host_perm(request.user, form.host_ids):
                return json_response(error='无权访问主机，请联系管理员')
            host_id = None
            token = uuid.uuid4().hex
            base_dir = os.path.join(settings.TRANSFER_DIR, token)
            
            if form.host:
                host_id, path = json.loads(form.host)
                if not path.strip('/'):
                    return json_response(error='请输入正确的数据源路径')
                host = Host.objects.get(pk=host_id)
                with host.get_ssh() as ssh:
                    code, _ = ssh.exec_command_raw(f'[ -d {path} ]')
                    if code != 0:
                        return json_response(error='数据源路径必须为该主机上已存在的目录')
                os.makedirs(base_dir)
                
                # Windows环境下不支持sshfs，改用直接rsync同步
                if IS_WINDOWS:
                    # Windows下跳过sshfs挂载，直接在_do_sync中处理远程文件同步
                    pass
                else:
                    with tempfile.NamedTemporaryFile(mode='w') as fp:
                        fp.write(host.pkey or AppSetting.get('private_key'))
                        fp.flush()
                        target = f'{host.username}@{host.hostname}:{path}'
                        command = f'sshfs -o ro -o ssh_command="ssh -p {host.port} -i {fp.name}" {target} {base_dir}'
                        task = subprocess.run(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
                        if task.returncode != 0:
                            os.system(f'umount -f {base_dir} &> /dev/null ; rm -rf {base_dir}')
                            return json_response(error=task.stdout.decode())
            elif form.managed_files:
                os.makedirs(base_dir)
                files_dir = os.path.join(settings.BASE_DIR, 'files')
                
                for filename in form.managed_files:
                    src_path = os.path.join(files_dir, filename)
                    if not os.path.exists(src_path):
                        return json_response(error=f'文件 {filename} 不存在')
                    
                    dst_path = os.path.join(base_dir, filename)
                    shutil.copy2(src_path, dst_path)
            elif form.remote_files:
                os.makedirs(base_dir)
                
                # 处理远程文件：下载到本地临时目录
                from apps.file.remote_manager import RemoteFileManager
                
                for remote_file_info in form.remote_files:
                    try:
                        # 远程文件信息包含：filename, path, remote_credentials
                        filename = remote_file_info['filename']
                        remote_path = remote_file_info['path']
                        credentials = remote_file_info['credentials']
                        
                        # 创建远程文件管理器
                        remote_manager = RemoteFileManager(
                            credentials['remote_path'],
                            credentials.get('username'),
                            credentials.get('password'),
                            credentials.get('domain')
                        )
                        
                        if not remote_manager.connect():
                            return json_response(error=f'无法连接到远程文件夹: {credentials["remote_path"]}')
                        
                        # 构建远程文件完整路径
                        remote_file_path = os.path.join(credentials['remote_path'], remote_path).replace('/', '\\')
                        local_file_path = os.path.join(base_dir, filename)
                        
                        # 下载远程文件到本地
                        if not remote_manager.download_file(remote_file_path, local_file_path):
                            return json_response(error=f'下载远程文件失败: {filename}')
                        
                        remote_manager.disconnect()
                        
                    except Exception as e:
                        return json_response(error=f'处理远程文件 {filename} 失败: {str(e)}')
            else:
                os.makedirs(base_dir)
                index = 0
                while True:
                    file = request.FILES.get(f'file{index}')
                    if not file:
                        break
                    with open(os.path.join(base_dir, file.name), 'wb') as f:
                        for chunk in file.chunks():
                            f.write(chunk)
                    index += 1
            
            Transfer.objects.create(
                user=request.user,
                digest=token,
                host_id=host_id,
                src_dir=base_dir,
                dst_dir=form.dst_dir,
                host_ids=json.dumps(form.host_ids),
            )
            return json_response(token)
        return json_response(error=error)

    @auth('exec.transfer.do')
    def patch(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误')
        ).parse(request.body)
        if error is None:
            task = Transfer.objects.get(digest=form.token)
            Thread(target=_dispatch_sync, args=(task,)).start()
        return json_response(error=error)


def _dispatch_sync(task):
    rds = get_redis_connection()
    threads = []
    max_workers = max(10, os.cpu_count() * 5)
    with futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        for host in Host.objects.filter(id__in=json.loads(task.host_ids)):
            t = executor.submit(_do_sync, rds, task, host)
            t.token = task.digest
            t.key = host.id
            threads.append(t)
        for t in futures.as_completed(threads):
            exc = t.exception()
            if exc:
                rds.publish(
                    t.token,
                    json.dumps({'key': t.key, 'status': -1, 'data': f'\x1b[31mException: {exc}\x1b[0m'})
                )
    
    # 清理临时目录，Windows和Linux使用不同的命令
    if IS_WINDOWS:
        # Windows下直接删除目录
        if os.path.exists(task.src_dir):
            shutil.rmtree(task.src_dir, ignore_errors=True)
    else:
        if task.host_id:
            command = f'umount -f {task.src_dir} && rm -rf {task.src_dir}'
        else:
            command = f'rm -rf {task.src_dir}'
        subprocess.run(command, shell=True)
    close_old_connections()


def _do_sync(rds, task, host):
    token = task.digest
    rds.publish(token, json.dumps({'key': host.id, 'data': '\r\n\x1b[36m### Executing ...\x1b[0m\r\n'}))
    
    # Windows环境特殊处理
    if IS_WINDOWS:
        return _do_sync_windows(rds, task, host)
    
    with tempfile.NamedTemporaryFile(mode='w') as fp:
        fp.write(host.pkey or AppSetting.get('private_key'))
        fp.write('\n')
        fp.flush()

        flag = time.time()
        options = '-azv --progress' if task.host_id else '-rzv --progress'
        argument = f'{task.src_dir}/ {host.username}@{host.hostname}:{task.dst_dir}'
        command = f'rsync {options} -h -e "ssh -p {host.port} -o StrictHostKeyChecking=no -i {fp.name}" {argument}'
        task_proc = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        message = b''
        while True:
            output = task_proc.stdout.read(1)
            if not output:
                break
            if output in (b'\r', b'\n'):
                message += b'\r\n' if output == b'\n' else b'\r'
                message = str_decode(message)
                if 'rsync: command not found' in message:
                    data = '\r\n\x1b[31m检测到该主机未安装rsync，可通过批量执行/执行任务模块进行以下命令批量安装\x1b[0m'
                    data += '\r\nCentos/Redhat: yum install -y rsync'
                    data += '\r\nUbuntu/Debian: apt install -y rsync'
                    rds.publish(token, json.dumps({'key': host.id, 'data': data}))
                    break
                rds.publish(token, json.dumps({'key': host.id, 'data': message}))
                message = b''
            else:
                message += output
        status = task_proc.wait()
        if status == 0:
            human_time = human_seconds_time(time.time() - flag)
            rds.publish(token, json.dumps({'key': host.id, 'data': f'\r\n\x1b[32m** 分发完成，总耗时：{human_time} **\x1b[0m'}))
        rds.publish(token, json.dumps({'key': host.id, 'status': task_proc.wait()}))


def _do_sync_windows(rds, task, host):
    """Windows环境下的文件同步处理"""
    token = task.digest
    
    try:
        # 在Windows环境下，如果是远程主机文件源，先通过SSH下载到本地
        if task.host_id:
            source_host = Host.objects.get(pk=task.host_id)
            rds.publish(token, json.dumps({'key': host.id, 'data': '\r\n\x1b[33m### Windows环境：正在从源主机下载文件...\x1b[0m\r\n'}))
            
            # 直接使用SSH连接下载文件到本地临时目录
            with source_host.get_ssh() as source_ssh:
                # 这里需要实现递归文件下载逻辑
                # 由于Windows环境限制，建议提示用户手动处理
                rds.publish(token, json.dumps({'key': host.id, 'data': '\r\n\x1b[31m⚠️  Windows环境暂不支持远程主机到远程主机的文件分发\x1b[0m\r\n'}))
                rds.publish(token, json.dumps({'key': host.id, 'data': '\r\n\x1b[33m建议：\x1b[0m\r\n'}))
                rds.publish(token, json.dumps({'key': host.id, 'data': '1. 先将文件从源主机下载到本地\r\n'}))
                rds.publish(token, json.dumps({'key': host.id, 'data': '2. 然后使用本地文件上传功能分发\r\n'}))
                rds.publish(token, json.dumps({'key': host.id, 'status': -1}))
                return
        
        # 本地文件到远程主机的同步
        rds.publish(token, json.dumps({'key': host.id, 'data': '\r\n\x1b[33m### Windows环境：正在通过SSH传输文件...\x1b[0m\r\n'}))
        
        flag = time.time()
        total_files = 0
        transferred_files = 0
        
        # 统计文件数量
        for root, dirs, files in os.walk(task.src_dir):
            total_files += len(files)
        
        # 使用SSH/SFTP传输文件
        with host.get_ssh() as ssh:
            # 确保目标目录存在
            ssh.exec_command_raw(f'mkdir -p {task.dst_dir}')
            
            for root, dirs, files in os.walk(task.src_dir):
                rel_dir = os.path.relpath(root, task.src_dir)
                if rel_dir != '.':
                    remote_dir = f"{task.dst_dir}/{rel_dir}".replace('\\', '/')
                    ssh.exec_command_raw(f'mkdir -p {remote_dir}')
                
                for file in files:
                    local_file = os.path.join(root, file)
                    if rel_dir != '.':
                        remote_file = f"{task.dst_dir}/{rel_dir}/{file}".replace('\\', '/')
                    else:
                        remote_file = f"{task.dst_dir}/{file}"
                    
                    try:
                        # 创建进度回调函数
                        def progress_callback(transferred, total):
                            if total > 0:
                                percent = int((transferred / total) * 100)
                                rds.publish(token, json.dumps({
                                    'key': host.id, 
                                    'data': f'\r传输中: {file} ({percent}%)...'
                                }))
                        
                        ssh.put_file(local_file, remote_file, callback=progress_callback)
                        transferred_files += 1
                        
                        progress = int((transferred_files / total_files) * 100)
                        rds.publish(token, json.dumps({
                            'key': host.id, 
                            'data': f'\r\n✓ {file} 传输完成 ({transferred_files}/{total_files} - {progress}%)\r\n'
                        }))
                        
                    except Exception as e:
                        rds.publish(token, json.dumps({
                            'key': host.id, 
                            'data': f'\r\n\x1b[31m✗ {file} 传输失败: {e}\x1b[0m\r\n'
                        }))
        
        human_time = human_seconds_time(time.time() - flag)
        rds.publish(token, json.dumps({
            'key': host.id, 
            'data': f'\r\n\x1b[32m** 分发完成，总耗时：{human_time} **\x1b[0m'
        }))
        rds.publish(token, json.dumps({'key': host.id, 'status': 0}))
        
    except Exception as e:
        rds.publish(token, json.dumps({
            'key': host.id, 
            'data': f'\r\n\x1b[31mWindows环境同步失败: {e}\x1b[0m\r\n'
        }))
        rds.publish(token, json.dumps({'key': host.id, 'status': -1}))
