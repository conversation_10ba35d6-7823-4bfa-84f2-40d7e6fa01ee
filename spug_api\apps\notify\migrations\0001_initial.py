# Generated by Django 2.2.28 on 2025-06-04 15:41

from django.db import migrations, models
import libs.mixins
import libs.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Notify',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('source', models.CharField(choices=[('monitor', '监控中心'), ('schedule', '任务计划'), ('flag', '应用发布'), ('alert', '系统警告')], max_length=10)),
                ('type', models.Char<PERSON>ield(choices=[('1', '通知'), ('2', '待办')], max_length=2)),
                ('content', models.TextField(null=True)),
                ('unread', models.BooleanField(default=True)),
                ('link', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255, null=True)),
                ('created_at', models.<PERSON>r<PERSON><PERSON>(default=libs.utils.human_datetime, max_length=20)),
            ],
            options={
                'db_table': 'notifies',
                'ordering': ('-id',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
