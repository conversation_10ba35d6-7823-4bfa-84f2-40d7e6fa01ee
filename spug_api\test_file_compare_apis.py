#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试文件对比相关的API接口
"""

import requests
import json
import sys

def test_api_endpoint(url, params=None, description=""):
    """测试API端点"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"URL: {url}")
    if params:
        print(f"参数: {params}")
    print('='*60)
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("响应数据结构:")
                print(json.dumps(data, indent=2, ensure_ascii=False)[:1000] + "..." if len(str(data)) > 1000 else json.dumps(data, indent=2, ensure_ascii=False))
                return True
            except json.JSONDecodeError:
                print("响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:500]}...")
                return False
        else:
            print(f"请求失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保spug服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    base_url = "http://localhost:8000/api/model-storage"
    
    print("文件对比API接口测试")
    print("注意: 这些测试需要认证，可能返回401错误")
    
    # 测试用例
    test_cases = [
        {
            'url': f'{base_url}/lazy-load-tree/',
            'params': {'root': 'true'},
            'description': '本地目录根结构加载'
        },
        {
            'url': f'{base_url}/lazy-load-tree/',
            'params': {'path': '/HDD_Raid/SVN_MODEL_REPO/Model'},
            'description': '本地目录子节点加载'
        },
        {
            'url': f'{base_url}/file-tree-compare/',
            'params': {'first_level': 'true'},
            'description': '远程目录第一级结构'
        },
        {
            'url': f'{base_url}/remote-tree-detail/',
            'params': {'path': 'Model'},
            'description': '远程目录详细信息'
        },
        {
            'url': f'{base_url}/path-compare/',
            'params': {'path': '/HDD_Raid/SVN_MODEL_REPO/Model'},
            'description': '路径对比详情'
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        if test_api_endpoint(**test_case):
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 个接口可访问")
    print("注意: 401错误是正常的，因为需要认证")
    print("主要检查数据结构是否正确")
    print('='*60)

if __name__ == "__main__":
    main()
