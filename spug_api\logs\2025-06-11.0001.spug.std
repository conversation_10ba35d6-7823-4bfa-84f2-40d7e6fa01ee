2025-06-11 18:04:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 18:04:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 成功导入nb_log
2025-06-11 18:09:09  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 18:09:09  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 18:09:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - 

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    
2025-06-11 18:09:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：   https://funboost.readthedocs.io/zh-cn/latest/  
2025-06-11 18:09:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - 18:09:09  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/Documents/GitHub/spug 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/Documents/GitHub/spug 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/Documents/GitHub/spug/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    

2025-06-11 18:09:09  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:147"  -use_config_form_funboost_config_module-[print]- 分布式函数调度框架检测到 你的项目根目录 C:/Users/<USER>/Documents/GitHub/spug 和当前文件夹 C:/Users/<USER>/Documents/GitHub/spug/spug_api  下没有 funboost_config.py 文件，

 2025-06-11 18:09:09  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:179"  -_auto_creat_config_file_to_project_root_path-[print]- 在  C:\Users\<USER>\Documents\GitHub\spug 目录下自动生成了一个文件， 请刷新文件夹查看或修改 
 "C:\Users\<USER>\Documents\GitHub\spug\funboost_config.py:1" 文件
 2025-06-11 18:09:09 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 18:09:09 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 18:09:09 - apps.file.apps - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:25" - ready - ERROR - 启动远程文件夹缓存调度器失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 18:09:12 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\check.py", line 59, in handle
    self.check(
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 35, in <module>
    path('file/', include('apps.file.urls')),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\urls.py", line 6, in <module>
    from .views import *
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\views.py", line 9, in <module>
    from libs.decorators import permission_required_or_403, auth_required
ImportError: cannot import name 'permission_required_or_403' from 'libs.decorators' (C:\Users\<USER>\Documents\GitHub\spug\spug_api\libs\decorators.py)
2025-06-11 18:09:31  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 18:09:31  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 nb_log模块导入成功
2025-06-11 18:09:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - 

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    
2025-06-11 18:09:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：   https://funboost.readthedocs.io/zh-cn/latest/  
2025-06-11 18:09:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - 18:09:31  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/Documents/GitHub/spug 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录  的funboost_config.py文件，
    如果没有 /funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/Documents/GitHub/spug 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/Documents/GitHub/spug/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    

2025-06-11 18:09:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-11 18:09:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-11 18:09:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-11 18:09:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001F061646110>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\pkey.py:82: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.
  "cipher": algorithms.TripleDES,
C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\paramiko\transport.py:253: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from cryptography.hazmat.primitives.ciphers.algorithms in 48.0.0.
  "class": algorithms.TripleDES,
2025-06-11 18:09:32 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 18:09:32 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
funboost模块导入成功
2025-06-11 18:13:00  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 18:13:00  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 18:13:01 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 18:13:01 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 18:13:01 - apps.file.apps - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:25" - ready - ERROR - 启动远程文件夹缓存调度器失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 18:13:03 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\check.py", line 59, in handle
    self.check(
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 35, in <module>
    path('file/', include('apps.file.urls')),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\urls.py", line 6, in <module>
    from .views import *
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\views.py", line 9, in <module>
    from libs.decorators import permission_required_or_403, auth_required
ImportError: cannot import name 'permission_required_or_403' from 'libs.decorators' (C:\Users\<USER>\Documents\GitHub\spug\spug_api\libs\decorators.py)
2025-06-11 18:15:32  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 18:15:32  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 18:15:33 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 18:15:33 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 18:15:33 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:23" - ready - ERROR - 启动远程文件夹缓存调度器失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 18:15:35 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\check.py", line 59, in handle
    self.check(
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 35, in <module>
    path('file/', include('apps.file.urls')),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\urls.py", line 6, in <module>
    from .views import *
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\views.py", line 9, in <module>
    from libs.decorators import permission_required_or_403, auth_required
ImportError: cannot import name 'permission_required_or_403' from 'libs.decorators' (C:\Users\<USER>\Documents\GitHub\spug\spug_api\libs\decorators.py)
2025-06-11 18:16:23  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 18:16:23  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 18:16:23 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 18:16:23 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 18:16:23 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:23" - ready - ERROR - 启动远程文件夹缓存调度器失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 18:16:26 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\check.py", line 59, in handle
    self.check(
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 35, in <module>
    path('file/', include('apps.file.urls')),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\urls.py", line 6, in <module>
    from .views import *
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\views.py", line 9, in <module>
    from libs.decorators import auth_required
ImportError: cannot import name 'auth_required' from 'libs.decorators' (C:\Users\<USER>\Documents\GitHub\spug\spug_api\libs\decorators.py)
2025-06-11 19:27:33  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 19:27:33  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 19:27:34 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 19:27:34 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 19:27:34 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:23" - ready - ERROR - 启动远程文件夹缓存调度器失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 19:27:35 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\check.py", line 59, in handle
    self.check(
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 35, in <module>
    path('file/', include('apps.file.urls')),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\urls.py", line 6, in <module>
    from .views import *
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\views.py", line 9, in <module>
    from libs.utils import get_request_real_ip, human_datetime, human_time, RequestApiAgent
ImportError: cannot import name 'RequestApiAgent' from 'libs.utils' (C:\Users\<USER>\Documents\GitHub\spug\spug_api\libs\utils.py)
2025-06-11 19:28:20  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 19:28:20  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 19:28:20 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 19:28:20 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 19:28:20 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:24" - ready - ERROR - 加载远程文件夹缓存服务失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 19:28:26 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\check.py", line 59, in handle
    self.check(
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 35, in <module>
    path('file/', include('apps.file.urls')),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\urls.py", line 6, in <module>
    from .views import *
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\views.py", line 28, in <module>
    from apps.file.models import FileTransfer, FileTransferGroup, RemoteFolder
ImportError: cannot import name 'FileTransfer' from 'apps.file.models' (C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\models.py)
2025-06-11 19:29:39  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 19:29:39  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 19:29:39 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 19:29:39 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 19:29:39 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:24" - ready - ERROR - 加载远程文件夹缓存服务失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 19:29:47 - global_except_hook - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\global_except_hook.py:14" - global_except_hook - ERROR - Unhandled exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 24, in <module>
    main()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\manage.py", line 20, in main
    execute_from_command_line(sys.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 381, in execute_from_command_line
    utility.execute()
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\__init__.py", line 375, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 323, in run_from_argv
    self.execute(*args, **cmd_options)
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 364, in execute
    output = self.handle(*args, **options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\commands\check.py", line 59, in handle
    self.check(
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 387, in check
    all_issues = self._run_checks(
                 ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\management\base.py", line 377, in _run_checks
    return checks.run_checks(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\registry.py", line 72, in run_checks
    new_errors = check(app_configs=app_configs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 13, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\core\checks\urls.py", line 23, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 403, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 588, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\functional.py", line 80, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\resolvers.py", line 581, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\spug\urls.py", line 35, in <module>
    path('file/', include('apps.file.urls')),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\urls\conf.py", line 34, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\scoop\apps\python311\current\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\urls.py", line 9, in <module>
    path('transfer/', post_transfer),
                      ^^^^^^^^^^^^^
NameError: name 'post_transfer' is not defined
2025-06-11 19:31:16  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 19:31:16  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 19:31:17 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 19:31:17 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 19:31:17 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:24" - ready - ERROR - 加载远程文件夹缓存服务失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
System check identified no issues (0 silenced).
2025-06-11 19:31:24  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 19:31:24  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 19:31:24 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 19:31:24 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 19:31:25 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:24" - ready - ERROR - 加载远程文件夹缓存服务失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
2025-06-11 19:31:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 19:31:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 19:31:26 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 19:31:26 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 19:31:26 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:24" - ready - ERROR - 加载远程文件夹缓存服务失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
Watching for file changes with StatReloader
2025-06-11 19:31:26 - django.utils.autoreload - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\django\utils\autoreload.py:597" - run_with_reloader - INFO - Watching for file changes with StatReloader
Performing system checks...

System check identified no issues (0 silenced).
June 11, 2025 - 19:31:32
Django version 2.2.28, using settings 'spug.settings'
Starting ASGI/Channels version 2.3.1 development server at http://127.0.0.1:8000/
Quit the server with CTRL-BREAK.
2025-06-11 19:31:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:110" - run - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-11 19:31:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:119" - run - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-11 19:31:32 - daphne.server - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\daphne\server.py:157" - listen_error - CRITICAL - Listen failure: Couldn't listen on 127.0.0.1:8000: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。.
2025-06-11 19:35:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52"  -<module>-[print]- 
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         
 2025-06-11 19:35:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64"  -<module>-[print]- 
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        

        
 2025-06-11 19:35:27 - persistqueue.serializers.pickle - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\serializers\pickle.py:11" - <module> - INFO - Selected pickle protocol: '4'
2025-06-11 19:35:27 - persistqueue - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\persistqueue\__init__.py:35" - <module> - INFO - DBUtils may not be installed, install via 'pip install persist-queue[extra]'
2025-06-11 19:35:27 - root - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\remote_cache_service.py:43" - <module> - INFO - funboost已加载，将使用分布式加速方案
2025-06-11 19:35:27 - apps.file - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\apps\file\apps.py:24" - ready - ERROR - 加载远程文件夹缓存服务失败: cannot access local variable 'FUNBOOST_AVAILABLE' where it is not associated with a value
