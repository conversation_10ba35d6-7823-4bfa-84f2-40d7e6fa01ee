/**
 * 测试计划执行输出组件
 */
import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react';
import { PageHeader, Steps, Card, Tag, Progress } from 'antd';
import {
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  FileOutlined,
  CodeOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import style from './index.module.less';
import { http, X_TOKEN } from 'libs';
import gStore from 'gStore';
import ModernLogViewer from './ModernLogViewer';

const { Step } = Steps;

function TestPlanOutput(props) {
  const [execution, setExecution] = useState(null);
  const [steps, setSteps] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [logContent, setLogContent] = useState('🚀 测试计划执行器已启动\n等待执行指令...');

  useEffect(() => {

    // 获取执行详情
    fetchExecutionDetails();

    // 建立WebSocket连接
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const socket = new WebSocket(`${protocol}//${window.location.host}/api/ws/subscribe/${props.token}/?x-token=${X_TOKEN}`);
    
    socket.onopen = () => {
      setLogContent(prev => prev + '\n✅ WebSocket 已连接，准备接收执行结果...');
      socket.send('ok');
    }
    
    socket.onmessage = e => {
      if (e.data === 'pong') {
        socket.send('ping')
      } else {
        handleMessage(e.data)
      }
    }
    
    socket.onclose = () => {
      setLogContent(prev => prev + '\n❌ WebSocket 连接已断开');
    }

    return () => {
      socket && socket.close();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const fetchExecutionDetails = () => {
    console.log('[DEBUG] Fetching execution details for token:', props.token);
    http.get(`/api/exec/test-plan-executions/${props.token}/`)
      .then(res => {
        console.log('[DEBUG] Execution details response:', res);
        setExecution(res.data);
        // 根据测试计划生成步骤列表（新的步骤化结构）
        if (res.data.plan && res.data.plan.commands) {
          const planSteps = [];
          
          res.data.plan.commands.forEach((step, index) => {
            if (!step.enabled) return;
            
            const stepLabel = step.label || `步骤 ${index + 1}`;
            
            // 如果步骤有文件，先添加文件传输子步骤
            if (step.files && step.files.length > 0) {
              step.files.forEach((file, fileIndex) => {
                planSteps.push({
                  id: `step_${index}_file_${fileIndex}`,
                  type: 'file',
                  title: `${stepLabel} - 传输文件: ${file.name}`,
                  description: `${file.path || file.localPath} → ${file.targetPath}`,
                  status: 'wait',
                  parentStep: index
                });
              });
            }
            
            // 添加命令执行步骤
            if (step.command) {
              planSteps.push({
                id: `step_${index}_cmd`,
                type: 'command',
                title: stepLabel,
                description: step.command,
                status: 'wait',
                parentStep: index
              });
            }
          });
          
          setSteps(planSteps);
        }
      })
      .catch(err => {
        console.error('[DEBUG] 获取执行详情失败:', err);
        console.error('[DEBUG] Error details:', err.response);
      });
  };

  const handleMessage = (message) => {
    try {
      const data = JSON.parse(message);
      const logText = data.message || data.data || '';
      
      // 添加到日志内容
      setLogContent(prev => prev + '\n' + logText);
      
      // 根据消息类型更新步骤状态
      if (data.type === 'info' && logText.includes('开始执行')) {
        // 开始执行某个步骤
        updateStepStatus('process');
      } else if (data.type === 'success') {
        // 步骤执行成功
        updateStepStatus('finish');
      } else if (data.type === 'error') {
        // 步骤执行失败
        updateStepStatus('error');
      } else if (data.type === 'warning') {
        // 步骤有警告但继续执行
        updateStepStatus('warning');
      }
      
    } catch (e) {
      // 普通文本消息
      setLogContent(prev => prev + '\n' + message);
    }
  };

  const updateStepStatus = (status) => {
    setSteps(prev => {
      const newSteps = [...prev];
      if (newSteps[currentStep]) {
        newSteps[currentStep].status = status;
        if (status === 'finish' || status === 'error' || status === 'warning') {
          setCurrentStep(curr => Math.min(curr + 1, newSteps.length));
        }
      }
      return newSteps;
    });
  };

  const getStepIcon = (step) => {
    switch (step.status) {
      case 'process':
        return <LoadingOutlined />;
      case 'finish':
        return <CheckCircleOutlined />;
      case 'error':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getStepStatus = (step) => {
    switch (step.status) {
      case 'process':
        return 'process';
      case 'finish':
        return 'finish';
      case 'error':
        return 'error';
      case 'warning':
        return 'finish'; // 警告状态显示为完成，但图标不同
      default:
        return 'wait';
    }
  };



  const progress = steps.length > 0 ? Math.round((currentStep / steps.length) * 100) : 0;

  return (
    <div className={style.output}>
      <div className={style.side}>
        <PageHeader onBack={props.onBack} title="测试计划执行"/>
        
        {/* 执行概览卡片 */}
        {execution && (
          <Card 
            size="small" 
            style={{ margin: '16px 0' }}
            bodyStyle={{ padding: '12px 16px' }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: 4 }}>
                  {execution.plan_name}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  主机: {execution.host_name}
                </div>
              </div>
              <Tag color={
                execution.status === 'success' ? 'green' : 
                execution.status === 'completed' ? 'orange' :
                execution.status === 'failed' ? 'red' : 'blue'
              } style={{ fontSize: '12px' }}>
                {execution.status === 'success' ? '✅ 成功' : 
                 execution.status === 'completed' ? '⚠️ 完成(有警告)' :
                 execution.status === 'failed' ? '❌ 失败' : '🔄 执行中'}
              </Tag>
            </div>
            
            <Progress 
              percent={progress} 
              size="small" 
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              showInfo={false}
            />
            <div style={{ 
              fontSize: '12px', 
              color: '#666', 
              marginTop: 6,
              display: 'flex',
              justifyContent: 'space-between'
            }}>
              <span>进度: {currentStep}/{steps.length} 步骤</span>
              <span>{progress}%</span>
            </div>
          </Card>
        )}

        {/* 步骤进度 */}
        <Card 
          title="执行步骤" 
          size="small" 
          style={{ margin: '16px 0' }}
          bodyStyle={{ padding: '16px', maxHeight: '400px', overflowY: 'auto' }}
        >
          <Steps direction="vertical" size="small" current={currentStep}>
            {steps.map((step, index) => (
              <Step
                key={step.id}
                title={
                  <div style={{ fontSize: '13px' }}>
                    {step.title}
                  </div>
                }
                description={
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#999',
                    wordBreak: 'break-all',
                    marginTop: 4
                  }}>
                    {step.description}
                  </div>
                }
                status={getStepStatus(step)}
                icon={
                  step.status === 'warning' ? 
                    <WarningOutlined style={{ color: '#faad14', fontSize: '14px' }} /> :
                  step.status === 'error' ?
                    <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: '14px' }} /> :
                  step.status === 'finish' ?
                    <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '14px' }} /> :
                  step.status === 'process' ?
                    <LoadingOutlined style={{ color: '#1890ff', fontSize: '14px' }} /> :
                  step.type === 'file' ? 
                    <FileOutlined style={{ fontSize: '14px', color: '#722ed1' }} /> : 
                    <CodeOutlined style={{ fontSize: '14px', color: '#13c2c2' }} />
                }
              />
            ))}
          </Steps>
        </Card>
      </div>
      
      <div className={style.body}>
        <ModernLogViewer 
          content={logContent}
          title={`📋 执行日志 ${execution ? `- ${execution.plan_name}` : ''}`}
          showProgress={true}
          progress={progress}
          status={execution?.status || 'running'}
          autoScroll={true}
        />
      </div>
    </div>
  )
}

export default observer(TestPlanOutput) 