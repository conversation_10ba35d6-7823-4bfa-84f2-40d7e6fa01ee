# Generated by Django 2.2.28 on 2025-06-11 15:03

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RemoteFolder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='配置名称')),
                ('remote_path', models.CharField(max_length=500, verbose_name='远程路径')),
                ('username', models.CharField(blank=True, max_length=100, null=True, verbose_name='用户名')),
                ('password', models.CharField(blank=True, max_length=200, null=True, verbose_name='密码')),
                ('domain', models.CharField(blank=True, max_length=100, null=True, verbose_name='域名')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '远程文件夹',
                'verbose_name_plural': '远程文件夹',
                'db_table': 'remote_folders',
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
