/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React from 'react';
import {
  DashboardOutlined,
  DesktopOutlined,
  CloudServerOutlined,
  CodeOutlined,
  FlagOutlined,
  ScheduleOutlined,
  DeploymentUnitOutlined,
  MonitorOutlined,
  AlertOutlined,
  SettingOutlined,
  DatabaseOutlined
} from '@ant-design/icons';

import HomeIndex from './pages/home';
import DashboardIndex from './pages/dashboard';
import HostIndex from './pages/host';
import ExecTask from './pages/exec/task';
import ExecTemplate from './pages/exec/template';
import ExecTransfer from './pages/exec/transfer';
import FileManager from './pages/file/FileManager';
import FileEditor from './pages/file/FileEditor';
import CommandCards from './pages/exec/command-cards';
import TestPlanEditor from './pages/exec/command-cards/TestPlanEditor';
import ResultExtraction from './pages/exec/result-extraction';
import TestResults from './pages/exec/test-results';
import FileDetailCompare from './pages/model-storage/FileDetailCompare';
import DeployApp from './pages/deploy/app';
import DeployRepository from './pages/deploy/repository';
import DeployRequest from './pages/deploy/request';
import ScheduleIndex from './pages/schedule';
import ConfigEnvironment from './pages/config/environment';
import ConfigService from './pages/config/service';
import ConfigApp from './pages/config/app';
import ConfigTask from './pages/config/task';
import ConfigSetting from './pages/config/setting';
import MonitorIndex from './pages/monitor';
import AlarmIndex from './pages/alarm/alarm';
import AlarmGroup from './pages/alarm/group';
import AlarmContact from './pages/alarm/contact';
import SystemAccount from './pages/system/account';
import SystemRole from './pages/system/role';
import SystemSetting from './pages/system/setting';
import SystemLogin from './pages/system/login';
import WelcomeIndex from './pages/welcome/index';
import WelcomeInfo from './pages/welcome/info';
import GpuDashboard from './pages/host/gpu-dashboard';
import ModelStorageMonitor from './pages/model-storage';

export default [
  {icon: <DesktopOutlined/>, title: '工作台', path: '/home', component: HomeIndex},
  {
    icon: <DashboardOutlined/>,
    title: 'Dashboard',
    auth: 'dashboard.dashboard.view',
    path: '/dashboard',
    component: DashboardIndex
  },
  {icon: <CloudServerOutlined/>, title: '主机管理', auth: 'host.host.view', path: '/host', component: HostIndex},
  {
    icon: <CodeOutlined/>, title: '批量执行', auth: 'exec.task.do|exec.template.view|exec.transfer.do', child: [
      {title: '执行任务', auth: 'exec.task.do', path: '/exec/task', component: ExecTask},
      {title: '模板管理', auth: 'exec.template.view', path: '/exec/template', component: ExecTemplate},
      {title: '文件分发', auth: 'exec.transfer.do', path: '/exec/transfer', component: ExecTransfer},
      {title: '文件管理', auth: 'exec.transfer.do', path: '/file/manager', component: FileManager},
    ]
  },
  // {
  //   icon: <FlagOutlined/>, title: '应用发布', auth: 'deploy.app.view|deploy.repository.view|deploy.request.view', child: [
  //     {title: '发布配置', auth: 'deploy.app.view', path: '/deploy/app', component: DeployApp},
  //     {title: '构建仓库', auth: 'deploy.repository.view', path: '/deploy/repository', component: DeployRepository},
  //     {title: '发布申请', auth: 'deploy.request.view', path: '/deploy/request', component: DeployRequest},
  //   ]
  // },
  {
    icon: <DeploymentUnitOutlined/>, title: '脚本配置中心', auth: 'config.env.view|config.src.view|config.app.view|config.task.view|exec.command-cards.view', child: [
      {title: '测试计划', path: '/exec/command-cards', component: CommandCards},
      {title: '结果展示', path: '/exec/test-results', component: TestResults},
      {title: '配套管理', auth: 'config.env.view', path: '/config/environment', component: ConfigEnvironment},
      {title: '脚本配置', auth: 'config.src.view', path: '/config/service', component: ConfigService},
      {title: '应用配置', auth: 'config.app.view', path: '/config/app', component: ConfigApp},
      {title: '任务管理', auth: 'config.task.view', path: '/config/task', component: ConfigTask},

      {path: '/exec/test-plan/:id', component: TestPlanEditor},
      {path: '/config/setting/:type/:id', component: ConfigSetting},
    ]
  },
  {
    icon: <ScheduleOutlined/>,
    title: '任务计划',
    auth: 'schedule.schedule.view',
    path: '/schedule',
    component: ScheduleIndex
  },
  {icon: <MonitorOutlined/>, title: '监控中心', auth: 'monitor.monitor.view', path: '/monitor', component: MonitorIndex},
  {icon: <DatabaseOutlined/>, title: '大模型存储监控', path: '/model-storage', component: ModelStorageMonitor},
  {
    icon: <AlertOutlined/>, title: '报警中心', auth: 'alarm.alarm.view|alarm.contact.view|alarm.group.view', child: [
      {title: '报警历史', auth: 'alarm.alarm.view', path: '/alarm/alarm', component: AlarmIndex},
      {title: '报警联系人', auth: 'alarm.contact.view', path: '/alarm/contact', component: AlarmContact},
      {title: '报警联系组', auth: 'alarm.group.view', path: '/alarm/group', component: AlarmGroup},
    ]
  },
  {
    icon: <SettingOutlined/>, title: '系统管理', auth: "system.account.view|system.role.view|system.setting.view", child: [
      {title: '登录日志', auth: 'system.login.view', path: '/system/login', component: SystemLogin},
      {title: '账户管理', auth: 'system.account.view', path: '/system/account', component: SystemAccount},
      {title: '角色管理', auth: 'system.role.view', path: '/system/role', component: SystemRole},
      {title: '系统设置', auth: 'system.setting.view', path: '/system/setting', component: SystemSetting},
    ]
  },
  {path: '/welcome/index', component: WelcomeIndex},
  {path: '/welcome/info', component: WelcomeInfo},
  {path: '/file/editor', component: FileEditor},
  {path: '/host/gpu-dashboard/:id', component: GpuDashboard},
  {path: '/result-extraction', component: ResultExtraction},
  {path: '/model-storage/file-compare', component: FileDetailCompare},
]
