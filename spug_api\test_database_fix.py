#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库修复效果
"""

import os
import sys
import django
import tempfile

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    from apps.model_storage.models import ServerMetrics
    from apps.model_storage.file_utils import safe_file_ops
    from django.utils import timezone
    DJANGO_AVAILABLE = True
except Exception as e:
    print(f"Django环境不可用: {e}")
    DJANGO_AVAILABLE = False

def test_temp_directory():
    """测试临时目录创建"""
    print("测试临时目录...")
    
    try:
        if DJANGO_AVAILABLE:
            temp_dir = safe_file_ops.temp_dir
            print(f"临时目录路径: {temp_dir}")
            print(f"目录是否存在: {os.path.exists(temp_dir)}")
            
            if os.path.exists(temp_dir):
                print(f"目录权限: 可读={os.access(temp_dir, os.R_OK)}, 可写={os.access(temp_dir, os.W_OK)}")
                
                # 测试文件创建
                test_file = safe_file_ops.get_safe_file_path('test.json', 'test')
                print(f"测试文件路径: {test_file}")
                
                try:
                    safe_file_ops.safe_json_write(test_file, {'test': 'data'})
                    print("✅ 文件写入成功")
                    
                    data = safe_file_ops.safe_json_read(test_file)
                    print(f"✅ 文件读取成功: {data}")
                    
                    # 清理测试文件
                    if os.path.exists(test_file):
                        os.remove(test_file)
                        print("✅ 测试文件清理完成")
                        
                except Exception as e:
                    print(f"❌ 文件操作失败: {e}")
            else:
                print("❌ 临时目录不存在")
        else:
            # 不使用Django，直接测试系统临时目录
            system_temp = tempfile.gettempdir()
            test_dir = os.path.join(system_temp, 'spug_model_storage')
            print(f"系统临时目录: {system_temp}")
            print(f"测试目录: {test_dir}")
            
            # 尝试创建目录
            os.makedirs(test_dir, exist_ok=True)
            print(f"目录创建成功: {os.path.exists(test_dir)}")
            
    except Exception as e:
        print(f"❌ 临时目录测试失败: {e}")

def test_database_operations():
    """测试数据库操作"""
    if not DJANGO_AVAILABLE:
        print("跳过数据库测试（Django不可用）")
        return
    
    print("\n测试数据库操作...")
    
    try:
        # 测试创建记录
        test_metrics = {
            'cpu_usage': 10.5,
            'memory_usage': 20.3,
            'disk_usage': 30.1,
            'network_upload': '1.5MB/s',
            'network_download': '2.3MB/s',
            'network_total': '3.8MB/s',
            'timestamp': timezone.now().isoformat()
        }
        
        # 创建测试记录
        metric = ServerMetrics.objects.create(**test_metrics)
        print(f"✅ 数据库记录创建成功: ID={metric.id}")
        
        # 查询记录
        count = ServerMetrics.objects.count()
        print(f"✅ 数据库记录总数: {count}")
        
        # 删除测试记录
        metric.delete()
        print("✅ 测试记录清理完成")
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")

def test_network_cache():
    """测试网络缓存功能"""
    print("\n测试网络IO缓存...")
    
    try:
        if DJANGO_AVAILABLE:
            from apps.model_storage.file_utils import network_io_tracker
            
            # 测试保存网络数据
            import time
            timestamp = time.time()
            success = network_io_tracker.save_network_data(1000, 2000, timestamp)
            print(f"网络数据保存: {'成功' if success else '失败'}")
            
            # 测试读取网络数据
            data = network_io_tracker.load_network_data()
            if data:
                print(f"✅ 网络数据读取成功: {data}")
            else:
                print("⚠️  网络数据读取为空")
                
        else:
            print("跳过网络缓存测试（Django不可用）")
            
    except Exception as e:
        print(f"❌ 网络缓存测试失败: {e}")

def main():
    """主测试函数"""
    print("数据库修复效果测试")
    print("=" * 50)
    
    test_temp_directory()
    test_database_operations()
    test_network_cache()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("\n建议:")
    print("1. 如果临时目录测试失败，检查权限设置")
    print("2. 如果数据库测试失败，检查数据库连接")
    print("3. 网络缓存警告在首次运行时是正常的")

if __name__ == "__main__":
    main()
