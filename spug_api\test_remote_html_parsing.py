#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试远程HTML解析功能
"""

import os
import sys
import django
import requests

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

django.setup()

from apps.model_storage.config import network_config

def test_remote_connection():
    """测试远程连接和HTML解析"""
    print("=" * 60)
    print("测试远程连接和HTML解析")
    print("=" * 60)
    
    remote_url = network_config.get_remote_url()
    auth = network_config.get_auth_credentials()
    
    print(f"远程URL: {remote_url}")
    print(f"认证用户: {auth[0]}")
    
    try:
        # 发送请求
        response = requests.get(remote_url, timeout=15, auth=auth)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            html_content = response.text
            print(f"HTML内容长度: {len(html_content)} 字符")
            
            # 显示HTML内容的前500个字符
            print("\nHTML内容预览:")
            print("-" * 40)
            print(html_content[:500])
            print("-" * 40)
            
            # 测试解析
            from apps.model_storage.views import FileTreeCompareView
            view = FileTreeCompareView()
            files = view.parse_remote_html(html_content, remote_url)
            
            print(f"\n解析结果: 找到 {len(files)} 个项目")
            for i, file_info in enumerate(files[:10]):  # 只显示前10个
                print(f"{i+1}. {file_info['name']} ({file_info['type']})")
            
            # 检查是否找到了预期的目录
            expected_dirs = ['DataSet', 'Model', 'Vendor']
            found_dirs = [f['name'] for f in files if f['type'] == 'folder']
            
            print(f"\n找到的目录: {found_dirs}")
            print(f"预期的目录: {expected_dirs}")
            
            missing_dirs = [d for d in expected_dirs if d not in found_dirs]
            if missing_dirs:
                print(f"❌ 缺少目录: {missing_dirs}")
                return False
            else:
                print("✅ 所有预期目录都找到了")
                return True
                
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_api_endpoint():
    """测试API端点"""
    print("\n" + "=" * 60)
    print("测试API端点")
    print("=" * 60)
    
    try:
        # 这里需要模拟一个请求
        from django.test import RequestFactory
        from apps.model_storage.views import FileTreeCompareView
        
        factory = RequestFactory()
        request = factory.get('/api/model-storage/file-tree-compare/?first_level=true')
        
        view = FileTreeCompareView()
        response = view.get(request)
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            import json
            data = json.loads(response.content.decode('utf-8'))
            print(f"返回数据类型: {type(data)}")
            print(f"数据长度: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                print("前几个项目:")
                for i, item in enumerate(data[:3]):
                    print(f"{i+1}. {item}")
                return True
            else:
                print("❌ 返回数据为空或格式不正确")
                return False
        else:
            print(f"❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("远程HTML解析功能测试")
    print("=" * 60)
    
    tests = [
        ("远程连接测试", test_remote_connection),
        ("API端点测试", test_api_endpoint),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
