import { observable, computed } from 'mobx';
import http from 'libs/http';
import { message } from 'antd';

class Store {
  @observable results = [];
  @observable loading = false;
  @observable filters = {};

  // 获取测试结果列表
  fetchResults = () => {
    this.loading = true;
    const params = {};
    
    if (this.filters.planName) {
      params.plan_name = this.filters.planName;
    }
    if (this.filters.taskName) {
      params.task_name = this.filters.taskName;
    }
    if (this.filters.dateRange && this.filters.dateRange.length === 2) {
      params.start_date = this.filters.dateRange[0].format('YYYY-MM-DD');
      params.end_date = this.filters.dateRange[1].format('YYYY-MM-DD');
    }

    return http.get('/api/exec/test-results/', { params })
      .then(res => {
        this.results = res.data || [];
      })
      .catch(err => {
        message.error('获取测试结果失败');
        console.error('获取测试结果失败:', err);
      })
      .finally(() => {
        this.loading = false;
      });
  };

  // 获取单个测试结果详情
  fetchResultDetail = (id) => {
    return http.get(`/api/exec/test-results/${id}/`)
      .then(res => res)
      .catch(err => {
        message.error('获取结果详情失败');
        throw err;
      });
  };

  // 更新测试结果
  updateResult = (id, data) => {
    return http.put(`/api/exec/test-results/${id}/`, data)
      .then(() => {
        this.fetchResults(); // 重新加载数据
      })
      .catch(err => {
        message.error('更新失败');
        console.error('更新失败:', err);
        throw err;
      });
  };

  // 删除测试结果
  deleteResult = (id) => {
    return http.delete(`/api/exec/test-results/${id}/`)
      .then(() => {
        message.success('删除成功');
        this.fetchResults(); // 重新加载数据
      })
      .catch(err => {
        message.error('删除失败');
        console.error('删除失败:', err);
      });
  };

  // 设置筛选条件
  setFilters = (filters) => {
    this.filters = { ...filters };
  };

  // 计算平均成功率
  @computed get avgSuccessRate() {
    if (this.results.length === 0) return 0;
    const total = this.results.reduce((sum, item) => sum + (item.success_rate || 0), 0);
    return Number((total / this.results.length).toFixed(1));
  }

  // 计算平均AI置信度
  @computed get avgAIConfidence() {
    if (this.results.length === 0) return 0;
    const total = this.results.reduce((sum, item) => sum + ((item.ai_confidence || 0) * 100), 0);
    return Number((total / this.results.length).toFixed(1));
  }

  // 计算总确认指标数
  @computed get totalConfirmedMetrics() {
    return this.results.reduce((sum, item) => sum + (item.confirmed_metrics || 0), 0);
  }

  // 按计划名称分组的结果统计
  @computed get resultsByPlan() {
    const grouped = {};
    this.results.forEach(result => {
      const planName = result.plan_name;
      if (!grouped[planName]) {
        grouped[planName] = {
          planName,
          count: 0,
          avgSuccessRate: 0,
          avgConfidence: 0,
          totalMetrics: 0,
          confirmedMetrics: 0,
          results: []
        };
      }
      grouped[planName].count++;
      grouped[planName].results.push(result);
      grouped[planName].totalMetrics += result.total_metrics || 0;
      grouped[planName].confirmedMetrics += result.confirmed_metrics || 0;
    });

    // 计算每个计划的平均值
    Object.keys(grouped).forEach(planName => {
      const group = grouped[planName];
      group.avgSuccessRate = Number(
        (group.results.reduce((sum, r) => sum + (r.success_rate || 0), 0) / group.count).toFixed(1)
      );
      group.avgConfidence = Number(
        (group.results.reduce((sum, r) => sum + ((r.ai_confidence || 0) * 100), 0) / group.count).toFixed(1)
      );
    });

    return Object.values(grouped);
  }

  // 按任务名称分组的结果统计  
  @computed get resultsByTask() {
    const grouped = {};
    this.results.forEach(result => {
      const taskName = result.task_name || '未关联任务';
      if (!grouped[taskName]) {
        grouped[taskName] = {
          taskName,
          count: 0,
          avgSuccessRate: 0,
          avgConfidence: 0,
          totalMetrics: 0,
          confirmedMetrics: 0,
          results: []
        };
      }
      grouped[taskName].count++;
      grouped[taskName].results.push(result);
      grouped[taskName].totalMetrics += result.total_metrics || 0;
      grouped[taskName].confirmedMetrics += result.confirmed_metrics || 0;
    });

    // 计算每个任务的平均值
    Object.keys(grouped).forEach(taskName => {
      const group = grouped[taskName];
      group.avgSuccessRate = Number(
        (group.results.reduce((sum, r) => sum + (r.success_rate || 0), 0) / group.count).toFixed(1)
      );
      group.avgConfidence = Number(
        (group.results.reduce((sum, r) => sum + ((r.ai_confidence || 0) * 100), 0) / group.count).toFixed(1)
      );
    });

    return Object.values(grouped);
  }

  // 时间序列数据 - 用于图表展示
  @computed get timeSeriesData() {
    // 按日期分组
    const grouped = {};
    this.results.forEach(result => {
      const date = result.created_at.split(' ')[0]; // 获取日期部分
      if (!grouped[date]) {
        grouped[date] = {
          date,
          count: 0,
          avgSuccessRate: 0,
          avgConfidence: 0,
          results: []
        };
      }
      grouped[date].count++;
      grouped[date].results.push(result);
    });

    // 计算每天的平均值并排序
    const timeSeriesArray = Object.keys(grouped)
      .map(date => {
        const group = grouped[date];
        group.avgSuccessRate = Number(
          (group.results.reduce((sum, r) => sum + (r.success_rate || 0), 0) / group.count).toFixed(1)
        );
        group.avgConfidence = Number(
          (group.results.reduce((sum, r) => sum + ((r.ai_confidence || 0) * 100), 0) / group.count).toFixed(1)
        );
        return group;
      })
      .sort((a, b) => a.date.localeCompare(b.date));

    return timeSeriesArray;
  }
}

export default new Store(); 