/**
 * 测试计划执行历史记录管理
 */
import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { 
  Table, 
  Card, 
  Tag, 
  Button, 
  Modal, 
  message, 
  Tooltip,
  Space,
  Popconfirm
} from 'antd';
import { 
  EyeOutlined, 
  DownloadOutlined, 
  DeleteOutlined,
  ReloadOutlined,
  ExperimentOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import moment from 'moment';
import ModernLogViewer from './ModernLogViewer';

function ExecutionHistory() {
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [logContent, setLogContent] = useState('');
  const [replayModalVisible, setReplayModalVisible] = useState(false);

  useEffect(() => {
    fetchRecords();
  }, []);

  const fetchRecords = () => {
    setLoading(true);
    http.get('/api/exec/test-plan-execution-history/')
      .then(res => {
        setRecords(res.data || []);
      })
      .catch(err => {
        message.error('获取执行记录失败');
        console.error('获取执行记录失败:', err);
      })
      .finally(() => setLoading(false));
  };

  const handleViewLog = (record) => {
    if (!record.log_file) {
      message.warning('该记录没有日志文件');
      return;
    }
    
    setSelectedRecord(record);
    setLogModalVisible(true);
    
    // 获取日志内容
    http.get('/api/file/manager/edit/', { params: { filename: record.log_file } })
      .then(res => {
        setLogContent(res.content || '日志文件为空');
      })
      .catch(err => {
        setLogContent('读取日志文件失败: ' + (err.response?.data?.error || err.message || '未知错误'));
      });
  };

  const handleDownloadLog = (record) => {
    if (!record.log_file) {
      message.warning('该记录没有日志文件');
      return;
    }
    
    // 创建下载链接
    const downloadUrl = `/api/file/download/${encodeURIComponent(record.log_file)}/`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${record.plan_name}_${record.start_time.replace(/[:\s]/g, '_')}.log`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExtractResults = (record) => {
    if (!record.log_file) {
      message.warning('该记录没有日志文件');
      return;
    }
    
    // 构建智能结果收集页面的URL
    const extractUrl = `/result-extraction?execution_id=${record.id}&plan_name=${encodeURIComponent(record.plan_name)}&log_file=${encodeURIComponent(record.log_file)}`;
    
    // 在新标签页中打开智能结果收集界面
    window.open(extractUrl, '_blank');
  };

  const handleDelete = (record) => {
    http.delete(`/api/exec/test-plan-execution-history/${record.id}/`)
      .then(() => {
        message.success('删除成功');
        fetchRecords();
      })
      .catch(err => {
        message.error('删除失败: ' + (err.message || '未知错误'));
      });
  };

  const getStatusTag = (status) => {
    const statusMap = {
      'running': { color: 'blue', text: '🔄 执行中' },
      'success': { color: 'green', text: '✅ 成功' },
      'completed': { color: 'orange', text: '⚠️ 完成(有警告)' },
      'failed': { color: 'red', text: '❌ 失败' }
    };
    
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: '执行记录ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id) => <Tag color="blue">#{id}</Tag>,
    },
    {
      title: '测试计划',
      dataIndex: 'plan_name',
      key: 'plan_name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '执行状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => getStatusTag(status),
    },
    {
      title: '执行用户',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 100,
    },
    {
      title: '主机数量',
      dataIndex: 'host_count',
      key: 'host_count',
      width: 80,
      render: (count) => <Tag>{count}</Tag>,
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 150,
      render: (time) => moment(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: 'end_time',
      width: 150,
      render: (time) => time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '执行时长',
      key: 'duration',
      width: 100,
      render: (_, record) => {
        if (!record.end_time) return '-';
        const start = moment(record.start_time);
        const end = moment(record.end_time);
        const duration = moment.duration(end.diff(start));
        return `${Math.floor(duration.asMinutes())}分${duration.seconds()}秒`;
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="智能提取结果">
            <Button
              type="text"
              size="small"
              icon={<ExperimentOutlined />}
              onClick={() => handleExtractResults(record)}
              disabled={!record.log_file || record.status === 'running'}
              style={{ color: '#722ed1' }}
            />
          </Tooltip>
          <Tooltip title="查看日志">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewLog(record)}
              disabled={!record.log_file}
            />
          </Tooltip>
          <Tooltip title="重放执行">
            <Button
              type="text"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => {
                setSelectedRecord(record);
                setReplayModalVisible(true);
              }}
              style={{ color: '#1890ff' }}
            />
          </Tooltip>
          <Tooltip title="下载日志">
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadLog(record)}
              disabled={!record.log_file}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条执行记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除记录">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="测试计划执行记录"
      extra={
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={fetchRecords}
          loading={loading}
        >
          刷新
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={records}
        rowKey="id"
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        scroll={{ x: 1200 }}
      />

      {/* 日志查看模态框 */}
      <Modal
        title={`执行日志 - ${selectedRecord?.plan_name}`}
        visible={logModalVisible}
        onCancel={() => setLogModalVisible(false)}
        width="90%"
        style={{ top: 20 }}
        bodyStyle={{ padding: '12px', height: '75vh' }}
        footer={[
          <Button key="extract" icon={<ExperimentOutlined />} onClick={() => {
            setLogModalVisible(false);
            handleExtractResults(selectedRecord);
          }}>
            智能提取
          </Button>,
          <Button key="download" icon={<DownloadOutlined />} onClick={() => handleDownloadLog(selectedRecord)}>
            下载日志
          </Button>,
          <Button key="close" onClick={() => setLogModalVisible(false)}>
            关闭
          </Button>,
        ]}
      >
        <ModernLogViewer 
          content={logContent}
          title={`${selectedRecord?.plan_name} - 执行日志`}
          enableSearch={true}
          enableDownload={true}
        />
      </Modal>

      {/* 重放执行模态框 */}
      <Modal
        title={`重放执行 - ${selectedRecord?.plan_name}`}
        visible={replayModalVisible}
        onCancel={() => setReplayModalVisible(false)}
        width="600px"
        footer={[
          <Button key="cancel" onClick={() => setReplayModalVisible(false)}>
            取消
          </Button>,
          <Button 
            key="replay" 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={() => {
              // 重新执行测试计划
              if (selectedRecord) {
                http.post('/api/exec/test-plan-executions/', {
                  plan_id: selectedRecord.plan_id,
                  host_ids: JSON.parse(selectedRecord.host_ids || '[]')
                })
                .then(res => {
                  message.success('执行已开始，即将跳转到执行页面');
                  setReplayModalVisible(false);
                  
                  // 跳转到执行页面
                  window.location.href = `/exec/task?token=${res.token || res.execution_id}`;
                })
                .catch(err => {
                  message.error('执行失败: ' + (err.message || '未知错误'));
                });
              }
            }}
          >
            开始执行
          </Button>,
        ]}
      >
        <div style={{ padding: '16px 0' }}>
          <p>您正在重新执行以下测试计划:</p>
          <div style={{ 
            background: '#f5f5f5', 
            padding: '16px', 
            borderRadius: '4px',
            marginBottom: '16px'
          }}>
            <div><strong>测试计划:</strong> {selectedRecord?.plan_name}</div>
            <div><strong>主机数量:</strong> {selectedRecord?.host_count}</div>
            <div><strong>上次执行:</strong> {selectedRecord?.start_time}</div>
            <div><strong>上次状态:</strong> {selectedRecord?.status}</div>
          </div>
          <p>点击"开始执行"将使用相同的配置重新执行此测试计划。</p>
        </div>
      </Modal>
    </Card>
  );
}

export default observer(ExecutionHistory); 