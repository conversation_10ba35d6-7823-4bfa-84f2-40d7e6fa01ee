from django.urls import path
from . import views

urlpatterns = [
    # 服务器监控
    path('server-metrics/', views.ServerMetricsView.as_view(), name='server_metrics'),
    
    # Git风格文件对比功能
    path('file-compare/', views.FileCompareView.as_view(), name='file_compare'),
    path('file-tree-compare/', views.FileTreeCompareView.as_view(), name='file_tree_compare'),
    path('check-differences/', views.CheckDifferencesView.as_view(), name='check_differences'),
    path('sync-single-file/', views.SyncSingleFileView.as_view(), name='sync_single_file'),
    path('batch-sync/', views.BatchSyncView.as_view(), name='batch_sync'),
    
    # 性能优化API
    path('lazy-load-tree/', views.LazyLoadTreeView.as_view(), name='lazy_load_tree'),
    path('async-scan/', views.AsyncScanView.as_view(), name='async_scan'),
    path('load-directory/', views.LoadModelDirectoryView.as_view(), name='load_directory'),
    
    # 新增：详细对比相关API
    path('remote-tree-detail/', views.RemoteTreeDetailView.as_view(), name='remote_tree_detail'),
    path('path-compare/', views.PathCompareView.as_view(), name='path_compare'),
    
    # 原有API（保持兼容性）
    path('file-tree/', views.FileTreeView.as_view(), name='file_tree'),
    path('release-plans/', views.ReleasePlanView.as_view(), name='release_plans'),
    path('svn-compare/', views.SvnCompareView.as_view(), name='svn_compare'),
    path('check-missing/', views.CheckMissingView.as_view(), name='check_missing'),
] 