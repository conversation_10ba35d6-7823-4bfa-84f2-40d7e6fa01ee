#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def migrate_database():
    """为exec_test_plans表添加variables字段"""
    db_path = 'spug_api/db.sqlite3'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(exec_test_plans)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'variables' in columns:
            print("variables字段已存在，跳过迁移")
            return True
        
        # 添加variables字段
        cursor.execute("ALTER TABLE exec_test_plans ADD COLUMN variables TEXT DEFAULT '[]'")
        conn.commit()
        
        print("成功为exec_test_plans表添加variables字段")
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(exec_test_plans)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'variables' in columns:
            print("验证成功：variables字段已添加")
        else:
            print("验证失败：variables字段未找到")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

if __name__ == '__main__':
    migrate_database() 