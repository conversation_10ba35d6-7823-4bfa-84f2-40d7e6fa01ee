{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\spug\\\\spug_web\\\\src\\\\pages\\\\model-storage\\\\FileDetailCompare.js\";\nimport React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';\nimport { Card, Tree, Spin, Alert, Tag, Button, Input, Space, Divider, Row, Col } from 'antd';\nimport { FolderOutlined, FileOutlined, ReloadOutlined, SearchOutlined, SyncOutlined, ExclamationCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, MinusCircleOutlined, FullscreenOutlined, GlobalOutlined, FolderOpenOutlined } from '@ant-design/icons';\nimport { http } from '../../libs';\nimport styles from './FileDetailCompare.module.less';\nconst {\n  Search\n} = Input;\nexport default function FileDetailCompare() {\n  const [loading, setLoading] = useState(false);\n  const [localTreeData, setLocalTreeData] = useState([]);\n  const [remoteTreeData, setRemoteTreeData] = useState([]);\n  const [expandedKeys, setExpandedKeys] = useState(['local_root', 'remote_root']);\n  const [searchValue, setSearchValue] = useState('');\n  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');\n  const searchTimerRef = useRef(null);\n  const [selectedPath, setSelectedPath] = useState('');\n  const [compareDetails, setCompareDetails] = useState(null);\n  const [error, setError] = useState(null);\n  console.log('FileDetailCompare组件已渲染 - 全屏版本');\n\n  // 获取配置参数 - 从URL参数或者默认值\n  const getConfigFromUrl = () => {\n    const urlParams = new URLSearchParams(window.location.search);\n    return {\n      basePath: urlParams.get('basePath') || '/HDD_Raid/SVN_MODEL_REPO',\n      remoteUrl: urlParams.get('remoteUrl') || 'http://10.63.30.93/GPU_MODEL_REPO/01.DEV/'\n    };\n  };\n  const config = getConfigFromUrl();\n\n  // 状态图标映射\n  const statusIcons = {\n    synced: /*#__PURE__*/React.createElement(CheckCircleOutlined, {\n      style: {\n        color: '#52c41a'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 13\n      }\n    }),\n    modified: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n      style: {\n        color: '#faad14'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 15\n      }\n    }),\n    missing: /*#__PURE__*/React.createElement(CloseCircleOutlined, {\n      style: {\n        color: '#f5222d'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 14\n      }\n    }),\n    added: /*#__PURE__*/React.createElement(CheckCircleOutlined, {\n      style: {\n        color: '#1890ff'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 12\n      }\n    }),\n    conflict: /*#__PURE__*/React.createElement(MinusCircleOutlined, {\n      style: {\n        color: '#722ed1'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 15\n      }\n    })\n  };\n\n  // 状态标签映射\n  const statusTags = {\n    synced: /*#__PURE__*/React.createElement(Tag, {\n      color: \"success\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 13\n      }\n    }, \"\\u5DF2\\u540C\\u6B65\"),\n    modified: /*#__PURE__*/React.createElement(Tag, {\n      color: \"warning\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 15\n      }\n    }, \"\\u5DF2\\u4FEE\\u6539\"),\n    missing: /*#__PURE__*/React.createElement(Tag, {\n      color: \"error\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 14\n      }\n    }, \"\\u7F3A\\u5931\"),\n    added: /*#__PURE__*/React.createElement(Tag, {\n      color: \"processing\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 12\n      }\n    }, \"\\u65B0\\u589E\"),\n    conflict: /*#__PURE__*/React.createElement(Tag, {\n      color: \"purple\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 15\n      }\n    }, \"\\u51B2\\u7A81\")\n  };\n  useEffect(() => {\n    // 延迟加载，避免阻塞UI\n    const timer = setTimeout(() => {\n      loadInitialData();\n    }, 100);\n    return () => clearTimeout(timer);\n  }, []);\n  const loadInitialData = useCallback(async () => {\n    console.log('开始加载初始数据...');\n    setLoading(true);\n    setError(null);\n    try {\n      console.log('发起API请求...');\n\n      // 先显示加载中的状态\n      setLocalTreeData([{\n        title: '📁 本地仓库 (加载中...)',\n        key: 'local_loading',\n        icon: /*#__PURE__*/React.createElement(FolderOpenOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }\n        }),\n        children: [{\n          title: '⏳ 正在扫描本地文件...',\n          key: 'local_loading_msg',\n          isLeaf: true\n        }]\n      }]);\n      setRemoteTreeData([{\n        title: '🌐 远程仓库 (加载中...)',\n        key: 'remote_loading',\n        icon: /*#__PURE__*/React.createElement(GlobalOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }\n        }),\n        children: [{\n          title: '⏳ 正在获取远程数据...',\n          key: 'remote_loading_msg',\n          isLeaf: true\n        }]\n      }]);\n\n      // 渐进式加载：先加载本地数据（通常更快）\n      console.log('加载本地数据...');\n      try {\n        const localResponse = await http.get('/api/model-storage/lazy-load-tree/', {\n          params: {\n            path: config.basePath,\n            root: true\n          }\n        });\n        console.log('本地API响应:', localResponse);\n        const localTree = buildLocalTree(localResponse);\n        console.log('构建的本地树:', localTree);\n        setLocalTreeData(localTree);\n        console.log('本地数据设置完成');\n      } catch (localError) {\n        console.error('本地数据加载失败:', localError);\n        setLocalTreeData([{\n          title: '📁 本地仓库 (加载失败)',\n          key: 'local_error',\n          icon: /*#__PURE__*/React.createElement(FolderOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }\n          }),\n          children: [{\n            title: `❌ ${localError.message || '加载失败，请重试'}`,\n            key: 'local_error_msg',\n            isLeaf: true\n          }]\n        }]);\n      }\n\n      // 然后加载远程数据（只加载第一级目录，提高速度）\n      console.log('加载远程数据...');\n      try {\n        const remoteResponse = await http.get('/api/model-storage/file-tree-compare/', {\n          params: {\n            first_level: true\n          }\n        });\n        console.log('远程API响应:', remoteResponse);\n        const remoteTree = buildRemoteTree(remoteResponse);\n        console.log('构建的远程树:', remoteTree);\n        setRemoteTreeData(remoteTree);\n        console.log('远程数据设置完成');\n      } catch (remoteError) {\n        console.error('远程数据加载失败:', remoteError);\n        setRemoteTreeData([{\n          title: '🌐 远程仓库 (加载失败)',\n          key: 'remote_error',\n          icon: /*#__PURE__*/React.createElement(GlobalOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }\n          }),\n          children: [{\n            title: `❌ ${remoteError.message || '网络错误，请重试'}`,\n            key: 'remote_error_msg',\n            isLeaf: true\n          }]\n        }]);\n      }\n    } catch (error) {\n      console.error('加载数据失败:', error);\n      setError(error.message || '加载数据失败');\n\n      // 显示错误信息但不阻塞界面\n      setLocalTreeData([{\n        title: '📁 本地仓库 (加载失败)',\n        key: 'local_error',\n        icon: /*#__PURE__*/React.createElement(FolderOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }\n        }),\n        children: [{\n          title: '❌ 加载失败，请点击刷新重试',\n          key: 'local_error_msg',\n          isLeaf: true\n        }]\n      }]);\n      setRemoteTreeData([{\n        title: '🌐 远程仓库 (加载失败)',\n        key: 'remote_error',\n        icon: /*#__PURE__*/React.createElement(GlobalOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }\n        }),\n        children: [{\n          title: '❌ 网络错误，请检查连接后重试',\n          key: 'remote_error_msg',\n          isLeaf: true\n        }]\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  }, [config.basePath]);\n  const buildLocalTree = response => {\n    console.log('构建本地树，完整响应数据:', response);\n    const baseTree = [{\n      title: '📁 本地仓库',\n      key: 'local_root',\n      icon: /*#__PURE__*/React.createElement(FolderOpenOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }\n      }),\n      children: []\n    }];\n\n    // 处理不同的API响应格式\n    let data = response;\n    if (response && response.data) {\n      data = response.data;\n    }\n    if (!data || !data.children || !Array.isArray(data.children)) {\n      console.log('本地数据为空或格式不正确，使用默认结构');\n      baseTree[0].children = [{\n        title: '❌ 无数据或加载失败',\n        key: 'local_no_data',\n        icon: /*#__PURE__*/React.createElement(FolderOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 17\n          }\n        }),\n        isLeaf: true,\n        data: {\n          name: '无数据',\n          source: 'local'\n        }\n      }];\n      return baseTree;\n    }\n\n    // 限制初始加载的节点数量，避免一次性渲染过多\n    const maxInitialNodes = 20;\n    const limitedChildren = data.children.slice(0, maxInitialNodes);\n    console.log(`本地数据总数: ${data.children.length}, 限制显示: ${limitedChildren.length}`);\n    console.log('第一条本地数据示例:', limitedChildren[0]);\n    baseTree[0].children = limitedChildren.map((item, index) => {\n      const title = item.title || item.name || `本地项目${index + 1}`;\n      const type = item.type || (item.isLeaf === false ? 'folder' : 'file');\n      return {\n        title: `${type === 'folder' ? '📁' : '📄'} ${title}`,\n        key: `local_${item.key || index}`,\n        icon: type === 'folder' ? /*#__PURE__*/React.createElement(FolderOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 35\n          }\n        }) : /*#__PURE__*/React.createElement(FileOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 56\n          }\n        }),\n        isLeaf: item.isLeaf !== false && type !== 'folder',\n        data: {\n          ...item,\n          ...(item.data || {}),\n          source: 'local',\n          type: type\n        },\n        // 初始不展开子节点，减少渲染负担\n        children: item.children && item.children.length > 0 || type === 'folder' ? [] : undefined\n      };\n    });\n\n    // 如果有更多数据，添加\"加载更多\"节点\n    if (data.children.length > maxInitialNodes) {\n      baseTree[0].children.push({\n        title: `📋 还有 ${data.children.length - maxInitialNodes} 个项目...`,\n        key: 'local_load_more',\n        icon: /*#__PURE__*/React.createElement(FolderOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }\n        }),\n        isLeaf: true,\n        data: {\n          name: '加载更多',\n          source: 'local',\n          isLoadMore: true\n        }\n      });\n    }\n    return baseTree;\n  };\n  const buildRemoteTree = response => {\n    console.log('构建远程树，完整响应数据:', response);\n    const baseTree = [{\n      title: '🌐 远程仓库',\n      key: 'remote_root',\n      icon: /*#__PURE__*/React.createElement(GlobalOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }\n      }),\n      children: []\n    }];\n\n    // 处理不同的API响应格式\n    let data = response;\n    if (response && response.data) {\n      data = response.data;\n    }\n\n    // 如果data是数组，直接使用\n    if (Array.isArray(data)) {\n      console.log('API返回数组格式数据，长度:', data.length);\n    }\n    // 如果data有children属性，使用children\n    else if (data && Array.isArray(data.children)) {\n      console.log('API返回对象格式数据，children长度:', data.children.length);\n      data = data.children;\n    }\n    // 如果data是对象但不是数组，尝试转换为数组\n    else if (data && typeof data === 'object' && !Array.isArray(data)) {\n      console.log('API返回对象格式，尝试转换为数组:', Object.keys(data));\n      data = Object.values(data).filter(item => item && typeof item === 'object');\n    } else {\n      console.log('远程数据格式不正确，使用默认结构');\n      data = [];\n    }\n    if (!Array.isArray(data) || data.length === 0) {\n      console.log('远程数据为空，使用默认结构');\n      baseTree[0].children = [{\n        title: '❌ 无数据或加载失败',\n        key: 'remote_no_data',\n        icon: /*#__PURE__*/React.createElement(FolderOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 17\n          }\n        }),\n        isLeaf: true,\n        data: {\n          name: '无数据',\n          source: 'remote'\n        }\n      }];\n      return baseTree;\n    }\n\n    // 限制初始加载的节点数量\n    const maxInitialNodes = 20;\n    const limitedData = data.slice(0, maxInitialNodes);\n    console.log(`远程数据总数: ${data.length}, 限制显示: ${limitedData.length}`);\n    console.log('第一条数据示例:', limitedData[0]);\n    baseTree[0].children = limitedData.map((item, index) => {\n      var _item$data;\n      // 处理不同的数据格式\n      const title = item.title || item.name || item.path || `项目${index + 1}`;\n      const type = item.type || (item.isLeaf === false ? 'folder' : 'file');\n      const status = item.status || ((_item$data = item.data) === null || _item$data === void 0 ? void 0 : _item$data.status) || 'unknown';\n      return {\n        title: /*#__PURE__*/React.createElement(\"span\", {\n          style: {\n            display: 'inline-flex',\n            alignItems: 'center'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 11\n          }\n        }, statusIcons[status] || (type === 'folder' ? '📁' : '📄'), /*#__PURE__*/React.createElement(\"span\", {\n          style: {\n            marginLeft: 4\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }\n        }, title)),\n        key: `remote_${item.key || item.path || index}`,\n        icon: type === 'folder' ? /*#__PURE__*/React.createElement(FolderOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 35\n          }\n        }) : /*#__PURE__*/React.createElement(FileOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 56\n          }\n        }),\n        isLeaf: item.isLeaf !== false && type !== 'folder',\n        data: {\n          ...item,\n          ...(item.data || {}),\n          source: 'remote',\n          type: type,\n          status: status\n        },\n        // 初始不展开子节点\n        children: item.children && item.children.length > 0 || type === 'folder' ? [] : undefined\n      };\n    });\n\n    // 如果有更多数据，添加\"加载更多\"节点\n    if (data.length > maxInitialNodes) {\n      baseTree[0].children.push({\n        title: `📋 还有 ${data.length - maxInitialNodes} 个项目...`,\n        key: 'remote_load_more',\n        icon: /*#__PURE__*/React.createElement(FolderOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }\n        }),\n        isLeaf: true,\n        data: {\n          name: '加载更多',\n          source: 'remote',\n          isLoadMore: true\n        }\n      });\n    }\n    return baseTree;\n  };\n  const onLoadData = async treeNode => {\n    const {\n      key,\n      data\n    } = treeNode;\n    if (!(data === null || data === void 0 ? void 0 : data.path) || treeNode.children) {\n      return;\n    }\n    try {\n      let response;\n      if (key === 'model_root') {\n        var _response$data;\n        // 加载Model目录下的所有模型\n        response = await http.get('/api/model-storage/load-directory/', {\n          params: {\n            directory: 'Model'\n          }\n        });\n        const children = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.map(item => ({\n          title: item.title,\n          key: item.key,\n          icon: item.type === 'folder' ? /*#__PURE__*/React.createElement(FolderOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 42\n            }\n          }) : /*#__PURE__*/React.createElement(FileOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 63\n            }\n          }),\n          isLeaf: item.isLeaf,\n          data: {\n            ...item.data,\n            source: 'remote'\n          },\n          children: item.children\n        }))) || [];\n        treeNode.children = children;\n      } else if (key === 'vendor_root') {\n        var _response$data2;\n        // 加载Vendor目录下的所有厂商\n        response = await http.get('/api/model-storage/load-directory/', {\n          params: {\n            directory: 'Vendor'\n          }\n        });\n        const children = ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.map(item => ({\n          title: item.title,\n          key: item.key,\n          icon: item.type === 'folder' ? /*#__PURE__*/React.createElement(FolderOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 42\n            }\n          }) : /*#__PURE__*/React.createElement(FileOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 63\n            }\n          }),\n          isLeaf: item.isLeaf,\n          data: {\n            ...item.data,\n            source: 'remote'\n          },\n          children: item.children\n        }))) || [];\n        treeNode.children = children;\n      } else if (key.startsWith('local_')) {\n        var _response$data$childr;\n        // 加载本地子目录\n        response = await http.get('/api/model-storage/lazy-load-tree/', {\n          params: {\n            path: data.path,\n            depth: 1\n          }\n        });\n        const children = ((_response$data$childr = response.data.children) === null || _response$data$childr === void 0 ? void 0 : _response$data$childr.map(item => ({\n          title: `${item.type === 'folder' ? '📁' : '📄'} ${item.title}`,\n          key: `local_${item.key}`,\n          icon: item.type === 'folder' ? /*#__PURE__*/React.createElement(FolderOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 42\n            }\n          }) : /*#__PURE__*/React.createElement(FileOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 63\n            }\n          }),\n          isLeaf: item.isLeaf,\n          data: {\n            ...item,\n            source: 'local'\n          }\n        }))) || [];\n        treeNode.children = children;\n      } else if (key.startsWith('remote_')) {\n        var _response$data3;\n        // 加载远程子目录\n        response = await http.get('/api/model-storage/remote-tree-detail/', {\n          params: {\n            path: data.path\n          }\n        });\n        const children = ((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.map(item => ({\n          title: `${statusIcons[item.status] || (item.type === 'folder' ? '📁' : '📄')} ${item.name}`,\n          key: `remote_${item.path.replace(/[^a-zA-Z0-9]/g, '_')}`,\n          icon: item.type === 'folder' ? /*#__PURE__*/React.createElement(FolderOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 42\n            }\n          }) : /*#__PURE__*/React.createElement(FileOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 63\n            }\n          }),\n          isLeaf: item.type === 'file',\n          data: {\n            ...item,\n            source: 'remote'\n          }\n        }))) || [];\n        treeNode.children = children;\n      }\n\n      // 更新树数据\n      if (key.startsWith('local_')) {\n        setLocalTreeData([...localTreeData]);\n      } else {\n        setRemoteTreeData([...remoteTreeData]);\n      }\n    } catch (error) {\n      console.error('加载子节点失败:', error);\n    }\n  };\n  const onSelect = async (selectedKeys, info) => {\n    if (!selectedKeys.length) return;\n    const {\n      node\n    } = info;\n    const {\n      data\n    } = node;\n    setSelectedPath((data === null || data === void 0 ? void 0 : data.path) || '');\n\n    // 如果选择的是文件夹，加载详细对比信息\n    if ((data === null || data === void 0 ? void 0 : data.type) === 'folder' && (data === null || data === void 0 ? void 0 : data.path)) {\n      try {\n        const response = await http.get('/api/model-storage/path-compare/', {\n          params: {\n            path: data.path\n          }\n        });\n        setCompareDetails(response.data);\n      } catch (error) {\n        console.error('加载对比详情失败:', error);\n      }\n    }\n  };\n  const onExpand = expandedKeys => {\n    setExpandedKeys(expandedKeys);\n  };\n  const handleRefresh = () => {\n    loadInitialData();\n  };\n  const handleSync = async path => {\n    try {\n      await http.post('/api/model-storage/sync-single-file/', {\n        file_path: path\n      });\n      // 刷新数据\n      loadInitialData();\n    } catch (error) {\n      console.error('同步失败:', error);\n    }\n  };\n  const renderCompareDetails = () => {\n    var _compareDetails$local, _compareDetails$local2, _compareDetails$local3, _compareDetails$local4, _compareDetails$remot, _compareDetails$remot2, _compareDetails$remot3, _compareDetails$remot4, _compareDetails$local5;\n    if (!compareDetails) return null;\n    return /*#__PURE__*/React.createElement(Card, {\n      title: \"\\u8BE6\\u7EC6\\u5BF9\\u6BD4\\u4FE1\\u606F\",\n      size: \"small\",\n      className: styles.compareDetails,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(Row, {\n      gutter: 16,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(Col, {\n      span: 12,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }\n    }, /*#__PURE__*/React.createElement(Card, {\n      size: \"small\",\n      title: \"\\u672C\\u5730\\u72B6\\u6001\",\n      bordered: false,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: styles.statusInfo,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 15\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 20\n      }\n    }, \"\\u8DEF\\u5F84:\"), \" \", ((_compareDetails$local = compareDetails.local) === null || _compareDetails$local === void 0 ? void 0 : _compareDetails$local.path) || '不存在'), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 20\n      }\n    }, \"\\u5927\\u5C0F:\"), \" \", ((_compareDetails$local2 = compareDetails.local) === null || _compareDetails$local2 === void 0 ? void 0 : _compareDetails$local2.size) || '-'), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 20\n      }\n    }, \"\\u4FEE\\u6539\\u65F6\\u95F4:\"), \" \", ((_compareDetails$local3 = compareDetails.local) === null || _compareDetails$local3 === void 0 ? void 0 : _compareDetails$local3.lastModified) || '-'), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 20\n      }\n    }, \"\\u72B6\\u6001:\"), \" \", statusTags[(_compareDetails$local4 = compareDetails.local) === null || _compareDetails$local4 === void 0 ? void 0 : _compareDetails$local4.status] || statusTags.missing)))), /*#__PURE__*/React.createElement(Col, {\n      span: 12,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 11\n      }\n    }, /*#__PURE__*/React.createElement(Card, {\n      size: \"small\",\n      title: \"\\u8FDC\\u7A0B\\u72B6\\u6001\",\n      bordered: false,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: styles.statusInfo,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 15\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 20\n      }\n    }, \"\\u8DEF\\u5F84:\"), \" \", ((_compareDetails$remot = compareDetails.remote) === null || _compareDetails$remot === void 0 ? void 0 : _compareDetails$remot.path) || '不存在'), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 20\n      }\n    }, \"\\u5927\\u5C0F:\"), \" \", ((_compareDetails$remot2 = compareDetails.remote) === null || _compareDetails$remot2 === void 0 ? void 0 : _compareDetails$remot2.size) || '-'), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 20\n      }\n    }, \"\\u4FEE\\u6539\\u65F6\\u95F4:\"), \" \", ((_compareDetails$remot3 = compareDetails.remote) === null || _compareDetails$remot3 === void 0 ? void 0 : _compareDetails$remot3.lastModified) || '-'), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 20\n      }\n    }, \"\\u72B6\\u6001:\"), \" \", statusTags[(_compareDetails$remot4 = compareDetails.remote) === null || _compareDetails$remot4 === void 0 ? void 0 : _compareDetails$remot4.status] || statusTags.missing))))), /*#__PURE__*/React.createElement(Divider, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: styles.diffSummary,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(\"h4\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 11\n      }\n    }, \"\\u5DEE\\u5F02\\u603B\\u7ED3:\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 11\n      }\n    }, compareDetails.diff_summary), ((_compareDetails$local5 = compareDetails.local) === null || _compareDetails$local5 === void 0 ? void 0 : _compareDetails$local5.status) !== 'synced' && /*#__PURE__*/React.createElement(Button, {\n      type: \"primary\",\n      icon: /*#__PURE__*/React.createElement(SyncOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 21\n        }\n      }),\n      onClick: () => handleSync(selectedPath),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 13\n      }\n    }, \"\\u540C\\u6B65\\u6587\\u4EF6\")));\n  };\n  const filterTreeData = (data, searchValue) => {\n    if (!searchValue) return data;\n    return data.map(node => {\n      const isMatch = node.title.toLowerCase().includes(searchValue.toLowerCase());\n      const filteredChildren = node.children ? filterTreeData(node.children, searchValue) : [];\n      if (isMatch || filteredChildren.length > 0) {\n        return {\n          ...node,\n          children: filteredChildren\n        };\n      }\n      return null;\n    }).filter(Boolean);\n  };\n\n  // 防抖搜索效果\n  useEffect(() => {\n    if (searchTimerRef.current) {\n      clearTimeout(searchTimerRef.current);\n    }\n    searchTimerRef.current = setTimeout(() => {\n      setDebouncedSearchValue(searchValue);\n    }, 300); // 300ms防抖\n\n    return () => {\n      if (searchTimerRef.current) {\n        clearTimeout(searchTimerRef.current);\n      }\n    };\n  }, [searchValue]);\n\n  // 使用 useMemo 缓存过滤结果，避免每次渲染都重新计算\n  const filteredLocalTree = useMemo(() => {\n    console.log('重新计算本地树过滤结果');\n    return filterTreeData(localTreeData, debouncedSearchValue);\n  }, [localTreeData, debouncedSearchValue]);\n  const filteredRemoteTree = useMemo(() => {\n    console.log('重新计算远程树过滤结果');\n    return filterTreeData(remoteTreeData, debouncedSearchValue);\n  }, [remoteTreeData, debouncedSearchValue]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.fileDetailCompareFullscreen,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Alert, {\n    message: loading ? \"🔄 正在加载数据...\" : \"🚀 文件详细对比功能 - 全屏版本\",\n    description: loading ? \"正在获取本地和远程仓库数据，请稍候...\" : `对比: ${config.basePath} ↔ ${config.remoteUrl}`,\n    type: loading ? \"warning\" : \"info\",\n    style: {\n      marginBottom: 16\n    },\n    showIcon: true,\n    action: /*#__PURE__*/React.createElement(Space, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 11\n      }\n    }, !loading && /*#__PURE__*/React.createElement(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/React.createElement(FullscreenOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 23\n        }\n      }),\n      onClick: () => {\n        var _document$documentEle, _document$documentEle2;\n        return (_document$documentEle = (_document$documentEle2 = document.documentElement).requestFullscreen) === null || _document$documentEle === void 0 ? void 0 : _document$documentEle.call(_document$documentEle2);\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 15\n      }\n    }, \"\\u5168\\u5C4F\")),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(Card, {\n    title: /*#__PURE__*/React.createElement(Space, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 11\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 13\n      }\n    }, \"\\u6587\\u4EF6\\u8BE6\\u7EC6\\u5BF9\\u6BD4\"), loading && /*#__PURE__*/React.createElement(Spin, {\n      size: \"small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 25\n      }\n    })),\n    extra: /*#__PURE__*/React.createElement(Space, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 11\n      }\n    }, /*#__PURE__*/React.createElement(Search, {\n      placeholder: \"\\u641C\\u7D22\\u6587\\u4EF6\\u6216\\u76EE\\u5F55\",\n      allowClear: true,\n      value: searchValue,\n      onChange: e => setSearchValue(e.target.value),\n      style: {\n        width: 250\n      },\n      prefix: /*#__PURE__*/React.createElement(SearchOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 23\n        }\n      }),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 13\n      }\n    }), /*#__PURE__*/React.createElement(Button, {\n      icon: /*#__PURE__*/React.createElement(ReloadOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 21\n        }\n      }),\n      onClick: handleRefresh,\n      loading: loading,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 13\n      }\n    }, \"\\u5237\\u65B0\")),\n    className: styles.fullscreenCard,\n    bodyStyle: {\n      height: 'calc(100vh - 200px)',\n      padding: '16px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }\n  }, error && /*#__PURE__*/React.createElement(Alert, {\n    message: \"\\u52A0\\u8F7D\\u5931\\u8D25\",\n    description: error,\n    type: \"error\",\n    style: {\n      marginBottom: 16\n    },\n    action: /*#__PURE__*/React.createElement(Button, {\n      size: \"small\",\n      type: \"primary\",\n      onClick: loadInitialData,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 15\n      }\n    }, \"\\u91CD\\u8BD5\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(Row, {\n    gutter: 16,\n    style: {\n      height: '100%'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Col, {\n    span: 12,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    size: \"small\",\n    title: /*#__PURE__*/React.createElement(Space, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(FolderOpenOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 19\n      }\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 19\n      }\n    }, \"\\u672C\\u5730\\u4ED3\\u5E93\"), /*#__PURE__*/React.createElement(Tag, {\n      color: \"blue\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 19\n      }\n    }, config.basePath)),\n    bordered: false,\n    className: styles.treeCard,\n    bodyStyle: {\n      height: 'calc(100vh - 300px)',\n      overflow: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 658,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Spin, {\n    spinning: loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Tree, {\n    loadData: onLoadData,\n    treeData: filteredLocalTree,\n    onSelect: onSelect,\n    onExpand: onExpand,\n    expandedKeys: expandedKeys.filter(key => key.startsWith('local_')),\n    showIcon: true,\n    height: \"100%\",\n    className: styles.localTree,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 17\n    }\n  })))), /*#__PURE__*/React.createElement(Col, {\n    span: 12,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Card, {\n    size: \"small\",\n    title: /*#__PURE__*/React.createElement(Space, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(GlobalOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 19\n      }\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 691,\n        columnNumber: 19\n      }\n    }, \"\\u8FDC\\u7A0B\\u4ED3\\u5E93\"), /*#__PURE__*/React.createElement(Tag, {\n      color: \"orange\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 19\n      }\n    }, config.remoteUrl)),\n    bordered: false,\n    className: styles.treeCard,\n    bodyStyle: {\n      height: 'calc(100vh - 300px)',\n      overflow: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Spin, {\n    spinning: loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 699,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Tree, {\n    loadData: onLoadData,\n    treeData: filteredRemoteTree,\n    onSelect: onSelect,\n    onExpand: onExpand,\n    expandedKeys: expandedKeys.filter(key => key.startsWith('remote_')),\n    showIcon: true,\n    height: \"100%\",\n    className: styles.remoteTree,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 17\n    }\n  }))))), selectedPath && /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.selectedPath,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Alert, {\n    message: `已选择: ${selectedPath}`,\n    type: \"info\",\n    showIcon: true,\n    style: {\n      marginTop: 16\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 13\n    }\n  })), renderCompareDetails()));\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useRef", "Card", "Tree", "Spin", "<PERSON><PERSON>", "Tag", "<PERSON><PERSON>", "Input", "Space", "Divider", "Row", "Col", "FolderOutlined", "FileOutlined", "ReloadOutlined", "SearchOutlined", "SyncOutlined", "ExclamationCircleOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "MinusCircleOutlined", "FullscreenOutlined", "GlobalOutlined", "FolderOpenOutlined", "http", "styles", "Search", "FileDetailCompare", "loading", "setLoading", "localTreeData", "setLocalTreeData", "remoteTreeData", "setRemoteTreeData", "expandedKeys", "setExpandedKeys", "searchValue", "setSearchValue", "debouncedSearchValue", "setDebouncedSearchValue", "searchTimerRef", "<PERSON><PERSON><PERSON>", "setSelectedPath", "compareDetails", "setCompareDetails", "error", "setError", "console", "log", "getConfigFromUrl", "urlParams", "URLSearchParams", "window", "location", "search", "basePath", "get", "remoteUrl", "config", "statusIcons", "synced", "createElement", "style", "color", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "modified", "missing", "added", "conflict", "statusTags", "timer", "setTimeout", "loadInitialData", "clearTimeout", "title", "key", "icon", "children", "<PERSON><PERSON><PERSON><PERSON>", "localResponse", "params", "path", "root", "localTree", "buildLocalTree", "localError", "message", "remoteResponse", "first_level", "remoteTree", "buildRemoteTree", "remoteError", "response", "baseTree", "data", "Array", "isArray", "name", "source", "maxInitialNodes", "limitedChildren", "slice", "length", "map", "item", "index", "type", "undefined", "push", "isLoadMore", "Object", "keys", "values", "filter", "limitedData", "_item$data", "status", "display", "alignItems", "marginLeft", "onLoadData", "treeNode", "_response$data", "directory", "_response$data2", "startsWith", "_response$data$childr", "depth", "_response$data3", "replace", "onSelect", "<PERSON><PERSON><PERSON><PERSON>", "info", "node", "onExpand", "handleRefresh", "handleSync", "post", "file_path", "renderCompareDetails", "_compareDetails$local", "_compareDetails$local2", "_compareDetails$local3", "_compareDetails$local4", "_compareDetails$remot", "_compareDetails$remot2", "_compareDetails$remot3", "_compareDetails$remot4", "_compareDetails$local5", "size", "className", "gutter", "span", "bordered", "statusInfo", "local", "lastModified", "remote", "diffSum<PERSON><PERSON>", "diff_summary", "onClick", "filterTreeData", "isMatch", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Boolean", "current", "filteredLocalTree", "filteredRemoteTree", "fileDetailCompareFullscreen", "description", "marginBottom", "showIcon", "action", "_document$documentEle", "_document$documentEle2", "document", "documentElement", "requestFullscreen", "call", "extra", "placeholder", "allowClear", "value", "onChange", "e", "target", "width", "prefix", "fullscreenCard", "bodyStyle", "height", "padding", "treeCard", "overflow", "spinning", "loadData", "treeData", "marginTop"], "sources": ["C:/Users/<USER>/Documents/GitHub/spug/spug_web/src/pages/model-storage/FileDetailCompare.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';\nimport { \n  Card, \n  Tree, \n  Spin, \n  Alert, \n  Tag, \n  Button, \n  Input, \n  Space,\n  Divider,\n  Row,\n  Col\n} from 'antd';\nimport { \n  FolderOutlined, \n  FileOutlined, \n  ReloadOutlined,\n  SearchOutlined,\n  SyncOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  MinusCircleOutlined,\n  FullscreenOutlined,\n  GlobalOutlined,\n  FolderOpenOutlined\n} from '@ant-design/icons';\nimport { http } from '../../libs';\nimport styles from './FileDetailCompare.module.less';\n\nconst { Search } = Input;\n\nexport default function FileDetailCompare() {\n  const [loading, setLoading] = useState(false);\n  const [localTreeData, setLocalTreeData] = useState([]);\n  const [remoteTreeData, setRemoteTreeData] = useState([]);\n  const [expandedKeys, setExpandedKeys] = useState(['local_root', 'remote_root']);\n  const [searchValue, setSearchValue] = useState('');\n  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');\n  const searchTimerRef = useRef(null);\n  const [selectedPath, setSelectedPath] = useState('');\n  const [compareDetails, setCompareDetails] = useState(null);\n  const [error, setError] = useState(null);\n\n  console.log('FileDetailCompare组件已渲染 - 全屏版本');\n  \n  // 获取配置参数 - 从URL参数或者默认值\n  const getConfigFromUrl = () => {\n    const urlParams = new URLSearchParams(window.location.search);\n    return {\n      basePath: urlParams.get('basePath') || '/HDD_Raid/SVN_MODEL_REPO',\n      remoteUrl: urlParams.get('remoteUrl') || 'http://10.63.30.93/GPU_MODEL_REPO/01.DEV/'\n    };\n  };\n  \n  const config = getConfigFromUrl();\n\n  // 状态图标映射\n  const statusIcons = {\n    synced: <CheckCircleOutlined style={{ color: '#52c41a' }} />,\n    modified: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,\n    missing: <CloseCircleOutlined style={{ color: '#f5222d' }} />,\n    added: <CheckCircleOutlined style={{ color: '#1890ff' }} />,\n    conflict: <MinusCircleOutlined style={{ color: '#722ed1' }} />\n  };\n\n  // 状态标签映射\n  const statusTags = {\n    synced: <Tag color=\"success\">已同步</Tag>,\n    modified: <Tag color=\"warning\">已修改</Tag>,\n    missing: <Tag color=\"error\">缺失</Tag>,\n    added: <Tag color=\"processing\">新增</Tag>,\n    conflict: <Tag color=\"purple\">冲突</Tag>\n  };\n\n  useEffect(() => {\n    // 延迟加载，避免阻塞UI\n    const timer = setTimeout(() => {\n      loadInitialData();\n    }, 100);\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  const loadInitialData = useCallback(async () => {\n    console.log('开始加载初始数据...');\n    setLoading(true);\n    setError(null);\n    \n    try {\n      console.log('发起API请求...');\n      \n      // 先显示加载中的状态\n      setLocalTreeData([{\n        title: '📁 本地仓库 (加载中...)',\n        key: 'local_loading',\n        icon: <FolderOpenOutlined />,\n        children: [{\n          title: '⏳ 正在扫描本地文件...',\n          key: 'local_loading_msg',\n          isLeaf: true\n        }]\n      }]);\n      \n      setRemoteTreeData([{\n        title: '🌐 远程仓库 (加载中...)',\n        key: 'remote_loading',\n        icon: <GlobalOutlined />,\n        children: [{\n          title: '⏳ 正在获取远程数据...',\n          key: 'remote_loading_msg',\n          isLeaf: true\n        }]\n      }]);\n\n      // 渐进式加载：先加载本地数据（通常更快）\n      console.log('加载本地数据...');\n      try {\n        const localResponse = await http.get('/api/model-storage/lazy-load-tree/', {\n          params: { path: config.basePath, root: true }\n        });\n        console.log('本地API响应:', localResponse);\n        \n        const localTree = buildLocalTree(localResponse);\n        console.log('构建的本地树:', localTree);\n        setLocalTreeData(localTree);\n        console.log('本地数据设置完成');\n      } catch (localError) {\n        console.error('本地数据加载失败:', localError);\n        setLocalTreeData([{\n          title: '📁 本地仓库 (加载失败)',\n          key: 'local_error',\n          icon: <FolderOutlined />,\n          children: [{\n            title: `❌ ${localError.message || '加载失败，请重试'}`,\n            key: 'local_error_msg',\n            isLeaf: true\n          }]\n        }]);\n      }\n\n      // 然后加载远程数据（只加载第一级目录，提高速度）\n      console.log('加载远程数据...');\n      try {\n        const remoteResponse = await http.get('/api/model-storage/file-tree-compare/', {\n          params: { first_level: true }\n        });\n        console.log('远程API响应:', remoteResponse);\n        \n        const remoteTree = buildRemoteTree(remoteResponse);\n        console.log('构建的远程树:', remoteTree);\n        setRemoteTreeData(remoteTree);\n        console.log('远程数据设置完成');\n      } catch (remoteError) {\n        console.error('远程数据加载失败:', remoteError);\n        setRemoteTreeData([{\n          title: '🌐 远程仓库 (加载失败)',\n          key: 'remote_error',\n          icon: <GlobalOutlined />,\n          children: [{\n            title: `❌ ${remoteError.message || '网络错误，请重试'}`,\n            key: 'remote_error_msg',\n            isLeaf: true\n          }]\n        }]);\n      }\n\n    } catch (error) {\n      console.error('加载数据失败:', error);\n      setError(error.message || '加载数据失败');\n      \n      // 显示错误信息但不阻塞界面\n      setLocalTreeData([{\n        title: '📁 本地仓库 (加载失败)',\n        key: 'local_error',\n        icon: <FolderOutlined />,\n        children: [{\n          title: '❌ 加载失败，请点击刷新重试',\n          key: 'local_error_msg',\n          isLeaf: true\n        }]\n      }]);\n      \n      setRemoteTreeData([{\n        title: '🌐 远程仓库 (加载失败)',\n        key: 'remote_error',\n        icon: <GlobalOutlined />,\n        children: [{\n          title: '❌ 网络错误，请检查连接后重试',\n          key: 'remote_error_msg',\n          isLeaf: true\n        }]\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  }, [config.basePath]);\n\n  const buildLocalTree = (response) => {\n    console.log('构建本地树，完整响应数据:', response);\n    \n    const baseTree = [{\n      title: '📁 本地仓库',\n      key: 'local_root',\n      icon: <FolderOpenOutlined />,\n      children: []\n    }];\n\n    // 处理不同的API响应格式\n    let data = response;\n    if (response && response.data) {\n      data = response.data;\n    }\n\n    if (!data || !data.children || !Array.isArray(data.children)) {\n      console.log('本地数据为空或格式不正确，使用默认结构');\n      baseTree[0].children = [\n        {\n          title: '❌ 无数据或加载失败',\n          key: 'local_no_data',\n          icon: <FolderOutlined />,\n          isLeaf: true,\n          data: { name: '无数据', source: 'local' }\n        }\n      ];\n      return baseTree;\n    }\n\n    // 限制初始加载的节点数量，避免一次性渲染过多\n    const maxInitialNodes = 20;\n    const limitedChildren = data.children.slice(0, maxInitialNodes);\n    \n    console.log(`本地数据总数: ${data.children.length}, 限制显示: ${limitedChildren.length}`);\n    console.log('第一条本地数据示例:', limitedChildren[0]);\n\n    baseTree[0].children = limitedChildren.map((item, index) => {\n      const title = item.title || item.name || `本地项目${index + 1}`;\n      const type = item.type || (item.isLeaf === false ? 'folder' : 'file');\n      \n      return {\n        title: `${type === 'folder' ? '📁' : '📄'} ${title}`,\n        key: `local_${item.key || index}`,\n        icon: type === 'folder' ? <FolderOutlined /> : <FileOutlined />,\n        isLeaf: item.isLeaf !== false && type !== 'folder',\n        data: { \n          ...item, \n          ...(item.data || {}), \n          source: 'local',\n          type: type \n        },\n        // 初始不展开子节点，减少渲染负担\n        children: (item.children && item.children.length > 0) || type === 'folder' ? [] : undefined\n      };\n    });\n\n    // 如果有更多数据，添加\"加载更多\"节点\n    if (data.children.length > maxInitialNodes) {\n      baseTree[0].children.push({\n        title: `📋 还有 ${data.children.length - maxInitialNodes} 个项目...`,\n        key: 'local_load_more',\n        icon: <FolderOutlined />,\n        isLeaf: true,\n        data: { name: '加载更多', source: 'local', isLoadMore: true }\n      });\n    }\n\n    return baseTree;\n  };\n\n  const buildRemoteTree = (response) => {\n    console.log('构建远程树，完整响应数据:', response);\n    \n    const baseTree = [{\n      title: '🌐 远程仓库',\n      key: 'remote_root', \n      icon: <GlobalOutlined />,\n      children: []\n    }];\n\n    // 处理不同的API响应格式\n    let data = response;\n    if (response && response.data) {\n      data = response.data;\n    }\n    \n    // 如果data是数组，直接使用\n    if (Array.isArray(data)) {\n      console.log('API返回数组格式数据，长度:', data.length);\n    } \n    // 如果data有children属性，使用children\n    else if (data && Array.isArray(data.children)) {\n      console.log('API返回对象格式数据，children长度:', data.children.length);\n      data = data.children;\n    }\n    // 如果data是对象但不是数组，尝试转换为数组\n    else if (data && typeof data === 'object' && !Array.isArray(data)) {\n      console.log('API返回对象格式，尝试转换为数组:', Object.keys(data));\n      data = Object.values(data).filter(item => item && typeof item === 'object');\n    }\n    else {\n      console.log('远程数据格式不正确，使用默认结构');\n      data = [];\n    }\n\n    if (!Array.isArray(data) || data.length === 0) {\n      console.log('远程数据为空，使用默认结构');\n      baseTree[0].children = [\n        {\n          title: '❌ 无数据或加载失败',\n          key: 'remote_no_data',\n          icon: <FolderOutlined />,\n          isLeaf: true,\n          data: { name: '无数据', source: 'remote' }\n        }\n      ];\n      return baseTree;\n    }\n\n    // 限制初始加载的节点数量\n    const maxInitialNodes = 20;\n    const limitedData = data.slice(0, maxInitialNodes);\n    \n    console.log(`远程数据总数: ${data.length}, 限制显示: ${limitedData.length}`);\n    console.log('第一条数据示例:', limitedData[0]);\n\n    baseTree[0].children = limitedData.map((item, index) => {\n      // 处理不同的数据格式\n      const title = item.title || item.name || item.path || `项目${index + 1}`;\n      const type = item.type || (item.isLeaf === false ? 'folder' : 'file');\n      const status = item.status || item.data?.status || 'unknown';\n      \n      return {\n        title: (\n          <span style={{ display: 'inline-flex', alignItems: 'center' }}>\n            {statusIcons[status] || (type === 'folder' ? '📁' : '📄')}\n            <span style={{ marginLeft: 4 }}>{title}</span>\n          </span>\n        ),\n        key: `remote_${item.key || item.path || index}`,\n        icon: type === 'folder' ? <FolderOutlined /> : <FileOutlined />,\n        isLeaf: item.isLeaf !== false && type !== 'folder',\n        data: { \n          ...item, \n          ...(item.data || {}), \n          source: 'remote',\n          type: type,\n          status: status \n        },\n        // 初始不展开子节点\n        children: (item.children && item.children.length > 0) || type === 'folder' ? [] : undefined\n      };\n    });\n\n    // 如果有更多数据，添加\"加载更多\"节点\n    if (data.length > maxInitialNodes) {\n      baseTree[0].children.push({\n        title: `📋 还有 ${data.length - maxInitialNodes} 个项目...`,\n        key: 'remote_load_more',\n        icon: <FolderOutlined />,\n        isLeaf: true,\n        data: { name: '加载更多', source: 'remote', isLoadMore: true }\n      });\n    }\n\n    return baseTree;\n  };\n\n  const onLoadData = async (treeNode) => {\n    const { key, data } = treeNode;\n    \n    if (!data?.path || treeNode.children) {\n      return;\n    }\n\n    try {\n      let response;\n      if (key === 'model_root') {\n        // 加载Model目录下的所有模型\n        response = await http.get('/api/model-storage/load-directory/', {\n          params: { directory: 'Model' }\n        });\n        \n        const children = response.data?.map(item => ({\n          title: item.title,\n          key: item.key,\n          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,\n          isLeaf: item.isLeaf,\n          data: { ...item.data, source: 'remote' },\n          children: item.children\n        })) || [];\n        \n        treeNode.children = children;\n      } else if (key === 'vendor_root') {\n        // 加载Vendor目录下的所有厂商\n        response = await http.get('/api/model-storage/load-directory/', {\n          params: { directory: 'Vendor' }\n        });\n        \n        const children = response.data?.map(item => ({\n          title: item.title,\n          key: item.key,\n          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,\n          isLeaf: item.isLeaf,\n          data: { ...item.data, source: 'remote' },\n          children: item.children\n        })) || [];\n        \n        treeNode.children = children;\n      } else if (key.startsWith('local_')) {\n        // 加载本地子目录\n        response = await http.get('/api/model-storage/lazy-load-tree/', {\n          params: { path: data.path, depth: 1 }\n        });\n        \n        const children = response.data.children?.map(item => ({\n          title: `${item.type === 'folder' ? '📁' : '📄'} ${item.title}`,\n          key: `local_${item.key}`,\n          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,\n          isLeaf: item.isLeaf,\n          data: { ...item, source: 'local' }\n        })) || [];\n        \n        treeNode.children = children;\n      } else if (key.startsWith('remote_')) {\n        // 加载远程子目录\n        response = await http.get('/api/model-storage/remote-tree-detail/', {\n          params: { path: data.path }\n        });\n        \n        const children = response.data?.map(item => ({\n          title: `${statusIcons[item.status] || (item.type === 'folder' ? '📁' : '📄')} ${item.name}`,\n          key: `remote_${item.path.replace(/[^a-zA-Z0-9]/g, '_')}`,\n          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,\n          isLeaf: item.type === 'file',\n          data: { ...item, source: 'remote' }\n        })) || [];\n        \n        treeNode.children = children;\n      }\n\n      // 更新树数据\n      if (key.startsWith('local_')) {\n        setLocalTreeData([...localTreeData]);\n      } else {\n        setRemoteTreeData([...remoteTreeData]);\n      }\n\n    } catch (error) {\n      console.error('加载子节点失败:', error);\n    }\n  };\n\n  const onSelect = async (selectedKeys, info) => {\n    if (!selectedKeys.length) return;\n    \n    const { node } = info;\n    const { data } = node;\n    \n    setSelectedPath(data?.path || '');\n    \n    // 如果选择的是文件夹，加载详细对比信息\n    if (data?.type === 'folder' && data?.path) {\n      try {\n        const response = await http.get('/api/model-storage/path-compare/', {\n          params: { path: data.path }\n        });\n        setCompareDetails(response.data);\n      } catch (error) {\n        console.error('加载对比详情失败:', error);\n      }\n    }\n  };\n\n  const onExpand = (expandedKeys) => {\n    setExpandedKeys(expandedKeys);\n  };\n\n  const handleRefresh = () => {\n    loadInitialData();\n  };\n\n  const handleSync = async (path) => {\n    try {\n      await http.post('/api/model-storage/sync-single-file/', { file_path: path });\n      // 刷新数据\n      loadInitialData();\n    } catch (error) {\n      console.error('同步失败:', error);\n    }\n  };\n\n  const renderCompareDetails = () => {\n    if (!compareDetails) return null;\n\n    return (\n      <Card title=\"详细对比信息\" size=\"small\" className={styles.compareDetails}>\n        <Row gutter={16}>\n          <Col span={12}>\n            <Card size=\"small\" title=\"本地状态\" bordered={false}>\n              <div className={styles.statusInfo}>\n                <p><strong>路径:</strong> {compareDetails.local?.path || '不存在'}</p>\n                <p><strong>大小:</strong> {compareDetails.local?.size || '-'}</p>\n                <p><strong>修改时间:</strong> {compareDetails.local?.lastModified || '-'}</p>\n                <p><strong>状态:</strong> {statusTags[compareDetails.local?.status] || statusTags.missing}</p>\n              </div>\n            </Card>\n          </Col>\n          <Col span={12}>\n            <Card size=\"small\" title=\"远程状态\" bordered={false}>\n              <div className={styles.statusInfo}>\n                <p><strong>路径:</strong> {compareDetails.remote?.path || '不存在'}</p>\n                <p><strong>大小:</strong> {compareDetails.remote?.size || '-'}</p>\n                <p><strong>修改时间:</strong> {compareDetails.remote?.lastModified || '-'}</p>\n                <p><strong>状态:</strong> {statusTags[compareDetails.remote?.status] || statusTags.missing}</p>\n              </div>\n            </Card>\n          </Col>\n        </Row>\n        <Divider />\n        <div className={styles.diffSummary}>\n          <h4>差异总结:</h4>\n          <p>{compareDetails.diff_summary}</p>\n          {compareDetails.local?.status !== 'synced' && (\n            <Button \n              type=\"primary\" \n              icon={<SyncOutlined />}\n              onClick={() => handleSync(selectedPath)}\n            >\n              同步文件\n            </Button>\n          )}\n        </div>\n      </Card>\n    );\n  };\n\n  const filterTreeData = (data, searchValue) => {\n    if (!searchValue) return data;\n    \n    return data.map(node => {\n      const isMatch = node.title.toLowerCase().includes(searchValue.toLowerCase());\n      const filteredChildren = node.children ? filterTreeData(node.children, searchValue) : [];\n      \n      if (isMatch || filteredChildren.length > 0) {\n        return {\n          ...node,\n          children: filteredChildren\n        };\n      }\n      return null;\n    }).filter(Boolean);\n  };\n\n  // 防抖搜索效果\n  useEffect(() => {\n    if (searchTimerRef.current) {\n      clearTimeout(searchTimerRef.current);\n    }\n    \n    searchTimerRef.current = setTimeout(() => {\n      setDebouncedSearchValue(searchValue);\n    }, 300); // 300ms防抖\n    \n    return () => {\n      if (searchTimerRef.current) {\n        clearTimeout(searchTimerRef.current);\n      }\n    };\n  }, [searchValue]);\n\n  // 使用 useMemo 缓存过滤结果，避免每次渲染都重新计算\n  const filteredLocalTree = useMemo(() => {\n    console.log('重新计算本地树过滤结果');\n    return filterTreeData(localTreeData, debouncedSearchValue);\n  }, [localTreeData, debouncedSearchValue]);\n  \n  const filteredRemoteTree = useMemo(() => {\n    console.log('重新计算远程树过滤结果');\n    return filterTreeData(remoteTreeData, debouncedSearchValue);\n  }, [remoteTreeData, debouncedSearchValue]);\n\n  return (\n    <div className={styles.fileDetailCompareFullscreen}>\n      <Alert\n        message={loading ? \"🔄 正在加载数据...\" : \"🚀 文件详细对比功能 - 全屏版本\"}\n        description={loading ? \n          \"正在获取本地和远程仓库数据，请稍候...\" : \n          `对比: ${config.basePath} ↔ ${config.remoteUrl}`\n        }\n        type={loading ? \"warning\" : \"info\"}\n        style={{ marginBottom: 16 }}\n        showIcon\n        action={\n          <Space>\n            {!loading && (\n              <Button \n                size=\"small\" \n                icon={<FullscreenOutlined />}\n                onClick={() => document.documentElement.requestFullscreen?.()}\n              >\n                全屏\n              </Button>\n            )}\n          </Space>\n        }\n      />\n      \n      <Card \n        title={\n          <Space>\n            <span>文件详细对比</span>\n            {loading && <Spin size=\"small\" />}\n          </Space>\n        }\n        extra={\n          <Space>\n            <Search\n              placeholder=\"搜索文件或目录\"\n              allowClear\n              value={searchValue}\n              onChange={(e) => setSearchValue(e.target.value)}\n              style={{ width: 250 }}\n              prefix={<SearchOutlined />}\n            />\n            <Button \n              icon={<ReloadOutlined />} \n              onClick={handleRefresh}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Space>\n        }\n        className={styles.fullscreenCard}\n        bodyStyle={{ height: 'calc(100vh - 200px)', padding: '16px' }}\n      >\n        {error && (\n          <Alert\n            message=\"加载失败\"\n            description={error}\n            type=\"error\"\n            style={{ marginBottom: 16 }}\n            action={\n              <Button \n                size=\"small\" \n                type=\"primary\" \n                onClick={loadInitialData}\n              >\n                重试\n              </Button>\n            }\n          />\n        )}\n        \n        <Row gutter={16} style={{ height: '100%' }}>\n          <Col span={12}>\n            <Card \n              size=\"small\" \n              title={\n                <Space>\n                  <FolderOpenOutlined />\n                  <span>本地仓库</span>\n                  <Tag color=\"blue\">{config.basePath}</Tag>\n                </Space>\n              }\n              bordered={false}\n              className={styles.treeCard}\n              bodyStyle={{ height: 'calc(100vh - 300px)', overflow: 'auto' }}\n            >\n              <Spin spinning={loading}>\n                <Tree\n                  loadData={onLoadData}\n                  treeData={filteredLocalTree}\n                  onSelect={onSelect}\n                  onExpand={onExpand}\n                  expandedKeys={expandedKeys.filter(key => key.startsWith('local_'))}\n                  showIcon\n                  height=\"100%\"\n                  className={styles.localTree}\n                />\n              </Spin>\n            </Card>\n          </Col>\n          <Col span={12}>\n            <Card \n              size=\"small\" \n              title={\n                <Space>\n                  <GlobalOutlined />\n                  <span>远程仓库</span>\n                  <Tag color=\"orange\">{config.remoteUrl}</Tag>\n                </Space>\n              }\n              bordered={false}\n              className={styles.treeCard}\n              bodyStyle={{ height: 'calc(100vh - 300px)', overflow: 'auto' }}\n            >\n              <Spin spinning={loading}>\n                <Tree\n                  loadData={onLoadData}\n                  treeData={filteredRemoteTree}\n                  onSelect={onSelect}\n                  onExpand={onExpand}\n                  expandedKeys={expandedKeys.filter(key => key.startsWith('remote_'))}\n                  showIcon\n                  height=\"100%\"\n                  className={styles.remoteTree}\n                />\n              </Spin>\n            </Card>\n          </Col>\n        </Row>\n        \n        {selectedPath && (\n          <div className={styles.selectedPath}>\n            <Alert\n              message={`已选择: ${selectedPath}`}\n              type=\"info\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          </div>\n        )}\n        \n        {renderCompareDetails()}\n      </Card>\n    </div>\n  );\n} "], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAChF,SACEC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,YAAY,EACZC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,kBAAkB,QACb,mBAAmB;AAC1B,SAASC,IAAI,QAAQ,YAAY;AACjC,OAAOC,MAAM,MAAM,iCAAiC;AAEpD,MAAM;EAAEC;AAAO,CAAC,GAAGnB,KAAK;AAExB,eAAe,SAASoB,iBAAiBA,CAAA,EAAG;EAC1C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;EAC/E,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM4C,cAAc,GAAGxC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAExCmD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;EAE5C;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,OAAO;MACLC,QAAQ,EAAEL,SAAS,CAACM,GAAG,CAAC,UAAU,CAAC,IAAI,0BAA0B;MACjEC,SAAS,EAAEP,SAAS,CAACM,GAAG,CAAC,WAAW,CAAC,IAAI;IAC3C,CAAC;EACH,CAAC;EAED,MAAME,MAAM,GAAGT,gBAAgB,CAAC,CAAC;;EAEjC;EACA,MAAMU,WAAW,GAAG;IAClBC,MAAM,eAAEjE,KAAA,CAAAkE,aAAA,CAAC3C,mBAAmB;MAAC4C,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC5DC,QAAQ,eAAE3E,KAAA,CAAAkE,aAAA,CAAC5C,yBAAyB;MAAC6C,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IACpEE,OAAO,eAAE5E,KAAA,CAAAkE,aAAA,CAAC1C,mBAAmB;MAAC2C,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC7DG,KAAK,eAAE7E,KAAA,CAAAkE,aAAA,CAAC3C,mBAAmB;MAAC4C,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC3DI,QAAQ,eAAE9E,KAAA,CAAAkE,aAAA,CAACzC,mBAAmB;MAAC0C,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE;EAC/D,CAAC;;EAED;EACA,MAAMK,UAAU,GAAG;IACjBd,MAAM,eAAEjE,KAAA,CAAAkE,aAAA,CAACxD,GAAG;MAAC0D,KAAK,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,oBAAQ,CAAC;IACtCC,QAAQ,eAAE3E,KAAA,CAAAkE,aAAA,CAACxD,GAAG;MAAC0D,KAAK,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,oBAAQ,CAAC;IACxCE,OAAO,eAAE5E,KAAA,CAAAkE,aAAA,CAACxD,GAAG;MAAC0D,KAAK,EAAC,OAAO;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAAO,CAAC;IACpCG,KAAK,eAAE7E,KAAA,CAAAkE,aAAA,CAACxD,GAAG;MAAC0D,KAAK,EAAC,YAAY;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAAO,CAAC;IACvCI,QAAQ,eAAE9E,KAAA,CAAAkE,aAAA,CAACxD,GAAG;MAAC0D,KAAK,EAAC,QAAQ;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAAO;EACvC,CAAC;EAEDxE,SAAS,CAAC,MAAM;IACd;IACA,MAAM8E,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BC,eAAe,CAAC,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMC,YAAY,CAACH,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAG/E,WAAW,CAAC,YAAY;IAC9CiD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BnB,UAAU,CAAC,IAAI,CAAC;IAChBiB,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACAjB,gBAAgB,CAAC,CAAC;QAChBgD,KAAK,EAAE,kBAAkB;QACzBC,GAAG,EAAE,eAAe;QACpBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACtC,kBAAkB;UAAAyC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAC5Ba,QAAQ,EAAE,CAAC;UACTH,KAAK,EAAE,eAAe;UACtBC,GAAG,EAAE,mBAAmB;UACxBG,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC,CAAC;MAEHlD,iBAAiB,CAAC,CAAC;QACjB8C,KAAK,EAAE,kBAAkB;QACzBC,GAAG,EAAE,gBAAgB;QACrBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACvC,cAAc;UAAA0C,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACxBa,QAAQ,EAAE,CAAC;UACTH,KAAK,EAAE,eAAe;UACtBC,GAAG,EAAE,oBAAoB;UACzBG,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC,CAAC;;MAEH;MACApC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB,IAAI;QACF,MAAMoC,aAAa,GAAG,MAAM5D,IAAI,CAACgC,GAAG,CAAC,oCAAoC,EAAE;UACzE6B,MAAM,EAAE;YAAEC,IAAI,EAAE5B,MAAM,CAACH,QAAQ;YAAEgC,IAAI,EAAE;UAAK;QAC9C,CAAC,CAAC;QACFxC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoC,aAAa,CAAC;QAEtC,MAAMI,SAAS,GAAGC,cAAc,CAACL,aAAa,CAAC;QAC/CrC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwC,SAAS,CAAC;QACjCzD,gBAAgB,CAACyD,SAAS,CAAC;QAC3BzC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB,CAAC,CAAC,OAAO0C,UAAU,EAAE;QACnB3C,OAAO,CAACF,KAAK,CAAC,WAAW,EAAE6C,UAAU,CAAC;QACtC3D,gBAAgB,CAAC,CAAC;UAChBgD,KAAK,EAAE,gBAAgB;UACvBC,GAAG,EAAE,aAAa;UAClBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACjD,cAAc;YAAAoD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC;UACxBa,QAAQ,EAAE,CAAC;YACTH,KAAK,EAAE,KAAKW,UAAU,CAACC,OAAO,IAAI,UAAU,EAAE;YAC9CX,GAAG,EAAE,iBAAiB;YACtBG,MAAM,EAAE;UACV,CAAC;QACH,CAAC,CAAC,CAAC;MACL;;MAEA;MACApC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB,IAAI;QACF,MAAM4C,cAAc,GAAG,MAAMpE,IAAI,CAACgC,GAAG,CAAC,uCAAuC,EAAE;UAC7E6B,MAAM,EAAE;YAAEQ,WAAW,EAAE;UAAK;QAC9B,CAAC,CAAC;QACF9C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE4C,cAAc,CAAC;QAEvC,MAAME,UAAU,GAAGC,eAAe,CAACH,cAAc,CAAC;QAClD7C,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE8C,UAAU,CAAC;QAClC7D,iBAAiB,CAAC6D,UAAU,CAAC;QAC7B/C,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOgD,WAAW,EAAE;QACpBjD,OAAO,CAACF,KAAK,CAAC,WAAW,EAAEmD,WAAW,CAAC;QACvC/D,iBAAiB,CAAC,CAAC;UACjB8C,KAAK,EAAE,gBAAgB;UACvBC,GAAG,EAAE,cAAc;UACnBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACvC,cAAc;YAAA0C,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC;UACxBa,QAAQ,EAAE,CAAC;YACTH,KAAK,EAAE,KAAKiB,WAAW,CAACL,OAAO,IAAI,UAAU,EAAE;YAC/CX,GAAG,EAAE,kBAAkB;YACvBG,MAAM,EAAE;UACV,CAAC;QACH,CAAC,CAAC,CAAC;MACL;IAEF,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BC,QAAQ,CAACD,KAAK,CAAC8C,OAAO,IAAI,QAAQ,CAAC;;MAEnC;MACA5D,gBAAgB,CAAC,CAAC;QAChBgD,KAAK,EAAE,gBAAgB;QACvBC,GAAG,EAAE,aAAa;QAClBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACjD,cAAc;UAAAoD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACxBa,QAAQ,EAAE,CAAC;UACTH,KAAK,EAAE,gBAAgB;UACvBC,GAAG,EAAE,iBAAiB;UACtBG,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC,CAAC;MAEHlD,iBAAiB,CAAC,CAAC;QACjB8C,KAAK,EAAE,gBAAgB;QACvBC,GAAG,EAAE,cAAc;QACnBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACvC,cAAc;UAAA0C,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACxBa,QAAQ,EAAE,CAAC;UACTH,KAAK,EAAE,iBAAiB;UACxBC,GAAG,EAAE,kBAAkB;UACvBG,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRtD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAAC6B,MAAM,CAACH,QAAQ,CAAC,CAAC;EAErB,MAAMkC,cAAc,GAAIQ,QAAQ,IAAK;IACnClD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiD,QAAQ,CAAC;IAEtC,MAAMC,QAAQ,GAAG,CAAC;MAChBnB,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,YAAY;MACjBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACtC,kBAAkB;QAAAyC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;MAC5Ba,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA,IAAIiB,IAAI,GAAGF,QAAQ;IACnB,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;MAC7BA,IAAI,GAAGF,QAAQ,CAACE,IAAI;IACtB;IAEA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACjB,QAAQ,IAAI,CAACkB,KAAK,CAACC,OAAO,CAACF,IAAI,CAACjB,QAAQ,CAAC,EAAE;MAC5DnC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClCkD,QAAQ,CAAC,CAAC,CAAC,CAAChB,QAAQ,GAAG,CACrB;QACEH,KAAK,EAAE,YAAY;QACnBC,GAAG,EAAE,eAAe;QACpBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACjD,cAAc;UAAAoD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACxBc,MAAM,EAAE,IAAI;QACZgB,IAAI,EAAE;UAAEG,IAAI,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ;MACvC,CAAC,CACF;MACD,OAAOL,QAAQ;IACjB;;IAEA;IACA,MAAMM,eAAe,GAAG,EAAE;IAC1B,MAAMC,eAAe,GAAGN,IAAI,CAACjB,QAAQ,CAACwB,KAAK,CAAC,CAAC,EAAEF,eAAe,CAAC;IAE/DzD,OAAO,CAACC,GAAG,CAAC,WAAWmD,IAAI,CAACjB,QAAQ,CAACyB,MAAM,WAAWF,eAAe,CAACE,MAAM,EAAE,CAAC;IAC/E5D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEyD,eAAe,CAAC,CAAC,CAAC,CAAC;IAE7CP,QAAQ,CAAC,CAAC,CAAC,CAAChB,QAAQ,GAAGuB,eAAe,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAC1D,MAAM/B,KAAK,GAAG8B,IAAI,CAAC9B,KAAK,IAAI8B,IAAI,CAACP,IAAI,IAAI,OAAOQ,KAAK,GAAG,CAAC,EAAE;MAC3D,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI,KAAKF,IAAI,CAAC1B,MAAM,KAAK,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;MAErE,OAAO;QACLJ,KAAK,EAAE,GAAGgC,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,IAAIhC,KAAK,EAAE;QACpDC,GAAG,EAAE,SAAS6B,IAAI,CAAC7B,GAAG,IAAI8B,KAAK,EAAE;QACjC7B,IAAI,EAAE8B,IAAI,KAAK,QAAQ,gBAAGpH,KAAA,CAAAkE,aAAA,CAACjD,cAAc;UAAAoD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC,gBAAG1E,KAAA,CAAAkE,aAAA,CAAChD,YAAY;UAAAmD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAC/Dc,MAAM,EAAE0B,IAAI,CAAC1B,MAAM,KAAK,KAAK,IAAI4B,IAAI,KAAK,QAAQ;QAClDZ,IAAI,EAAE;UACJ,GAAGU,IAAI;UACP,IAAIA,IAAI,CAACV,IAAI,IAAI,CAAC,CAAC,CAAC;UACpBI,MAAM,EAAE,OAAO;UACfQ,IAAI,EAAEA;QACR,CAAC;QACD;QACA7B,QAAQ,EAAG2B,IAAI,CAAC3B,QAAQ,IAAI2B,IAAI,CAAC3B,QAAQ,CAACyB,MAAM,GAAG,CAAC,IAAKI,IAAI,KAAK,QAAQ,GAAG,EAAE,GAAGC;MACpF,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,IAAIb,IAAI,CAACjB,QAAQ,CAACyB,MAAM,GAAGH,eAAe,EAAE;MAC1CN,QAAQ,CAAC,CAAC,CAAC,CAAChB,QAAQ,CAAC+B,IAAI,CAAC;QACxBlC,KAAK,EAAE,SAASoB,IAAI,CAACjB,QAAQ,CAACyB,MAAM,GAAGH,eAAe,SAAS;QAC/DxB,GAAG,EAAE,iBAAiB;QACtBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACjD,cAAc;UAAAoD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACxBc,MAAM,EAAE,IAAI;QACZgB,IAAI,EAAE;UAAEG,IAAI,EAAE,MAAM;UAAEC,MAAM,EAAE,OAAO;UAAEW,UAAU,EAAE;QAAK;MAC1D,CAAC,CAAC;IACJ;IAEA,OAAOhB,QAAQ;EACjB,CAAC;EAED,MAAMH,eAAe,GAAIE,QAAQ,IAAK;IACpClD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiD,QAAQ,CAAC;IAEtC,MAAMC,QAAQ,GAAG,CAAC;MAChBnB,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,aAAa;MAClBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACvC,cAAc;QAAA0C,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;MACxBa,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA,IAAIiB,IAAI,GAAGF,QAAQ;IACnB,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;MAC7BA,IAAI,GAAGF,QAAQ,CAACE,IAAI;IACtB;;IAEA;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MACvBpD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEmD,IAAI,CAACQ,MAAM,CAAC;IAC7C;IACA;IAAA,KACK,IAAIR,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAACjB,QAAQ,CAAC,EAAE;MAC7CnC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmD,IAAI,CAACjB,QAAQ,CAACyB,MAAM,CAAC;MAC5DR,IAAI,GAAGA,IAAI,CAACjB,QAAQ;IACtB;IACA;IAAA,KACK,IAAIiB,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MACjEpD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmE,MAAM,CAACC,IAAI,CAACjB,IAAI,CAAC,CAAC;MACpDA,IAAI,GAAGgB,MAAM,CAACE,MAAM,CAAClB,IAAI,CAAC,CAACmB,MAAM,CAACT,IAAI,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,CAAC;IAC7E,CAAC,MACI;MACH9D,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BmD,IAAI,GAAG,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAIA,IAAI,CAACQ,MAAM,KAAK,CAAC,EAAE;MAC7C5D,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5BkD,QAAQ,CAAC,CAAC,CAAC,CAAChB,QAAQ,GAAG,CACrB;QACEH,KAAK,EAAE,YAAY;QACnBC,GAAG,EAAE,gBAAgB;QACrBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACjD,cAAc;UAAAoD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACxBc,MAAM,EAAE,IAAI;QACZgB,IAAI,EAAE;UAAEG,IAAI,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAS;MACxC,CAAC,CACF;MACD,OAAOL,QAAQ;IACjB;;IAEA;IACA,MAAMM,eAAe,GAAG,EAAE;IAC1B,MAAMe,WAAW,GAAGpB,IAAI,CAACO,KAAK,CAAC,CAAC,EAAEF,eAAe,CAAC;IAElDzD,OAAO,CAACC,GAAG,CAAC,WAAWmD,IAAI,CAACQ,MAAM,WAAWY,WAAW,CAACZ,MAAM,EAAE,CAAC;IAClE5D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuE,WAAW,CAAC,CAAC,CAAC,CAAC;IAEvCrB,QAAQ,CAAC,CAAC,CAAC,CAAChB,QAAQ,GAAGqC,WAAW,CAACX,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAAA,IAAAU,UAAA;MACtD;MACA,MAAMzC,KAAK,GAAG8B,IAAI,CAAC9B,KAAK,IAAI8B,IAAI,CAACP,IAAI,IAAIO,IAAI,CAACvB,IAAI,IAAI,KAAKwB,KAAK,GAAG,CAAC,EAAE;MACtE,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI,KAAKF,IAAI,CAAC1B,MAAM,KAAK,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;MACrE,MAAMsC,MAAM,GAAGZ,IAAI,CAACY,MAAM,MAAAD,UAAA,GAAIX,IAAI,CAACV,IAAI,cAAAqB,UAAA,uBAATA,UAAA,CAAWC,MAAM,KAAI,SAAS;MAE5D,OAAO;QACL1C,KAAK,eACHpF,KAAA,CAAAkE,aAAA;UAAMC,KAAK,EAAE;YAAE4D,OAAO,EAAE,aAAa;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA3D,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAC3DV,WAAW,CAAC8D,MAAM,CAAC,KAAKV,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,eACzDpH,KAAA,CAAAkE,aAAA;UAAMC,KAAK,EAAE;YAAE8D,UAAU,EAAE;UAAE,CAAE;UAAA5D,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAEU,KAAY,CACzC,CACP;QACDC,GAAG,EAAE,UAAU6B,IAAI,CAAC7B,GAAG,IAAI6B,IAAI,CAACvB,IAAI,IAAIwB,KAAK,EAAE;QAC/C7B,IAAI,EAAE8B,IAAI,KAAK,QAAQ,gBAAGpH,KAAA,CAAAkE,aAAA,CAACjD,cAAc;UAAAoD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC,gBAAG1E,KAAA,CAAAkE,aAAA,CAAChD,YAAY;UAAAmD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QAC/Dc,MAAM,EAAE0B,IAAI,CAAC1B,MAAM,KAAK,KAAK,IAAI4B,IAAI,KAAK,QAAQ;QAClDZ,IAAI,EAAE;UACJ,GAAGU,IAAI;UACP,IAAIA,IAAI,CAACV,IAAI,IAAI,CAAC,CAAC,CAAC;UACpBI,MAAM,EAAE,QAAQ;UAChBQ,IAAI,EAAEA,IAAI;UACVU,MAAM,EAAEA;QACV,CAAC;QACD;QACAvC,QAAQ,EAAG2B,IAAI,CAAC3B,QAAQ,IAAI2B,IAAI,CAAC3B,QAAQ,CAACyB,MAAM,GAAG,CAAC,IAAKI,IAAI,KAAK,QAAQ,GAAG,EAAE,GAAGC;MACpF,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,IAAIb,IAAI,CAACQ,MAAM,GAAGH,eAAe,EAAE;MACjCN,QAAQ,CAAC,CAAC,CAAC,CAAChB,QAAQ,CAAC+B,IAAI,CAAC;QACxBlC,KAAK,EAAE,SAASoB,IAAI,CAACQ,MAAM,GAAGH,eAAe,SAAS;QACtDxB,GAAG,EAAE,kBAAkB;QACvBC,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACjD,cAAc;UAAAoD,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;QACxBc,MAAM,EAAE,IAAI;QACZgB,IAAI,EAAE;UAAEG,IAAI,EAAE,MAAM;UAAEC,MAAM,EAAE,QAAQ;UAAEW,UAAU,EAAE;QAAK;MAC3D,CAAC,CAAC;IACJ;IAEA,OAAOhB,QAAQ;EACjB,CAAC;EAED,MAAM2B,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACrC,MAAM;MAAE9C,GAAG;MAAEmB;IAAK,CAAC,GAAG2B,QAAQ;IAE9B,IAAI,EAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEb,IAAI,KAAIwC,QAAQ,CAAC5C,QAAQ,EAAE;MACpC;IACF;IAEA,IAAI;MACF,IAAIe,QAAQ;MACZ,IAAIjB,GAAG,KAAK,YAAY,EAAE;QAAA,IAAA+C,cAAA;QACxB;QACA9B,QAAQ,GAAG,MAAMzE,IAAI,CAACgC,GAAG,CAAC,oCAAoC,EAAE;UAC9D6B,MAAM,EAAE;YAAE2C,SAAS,EAAE;UAAQ;QAC/B,CAAC,CAAC;QAEF,MAAM9C,QAAQ,GAAG,EAAA6C,cAAA,GAAA9B,QAAQ,CAACE,IAAI,cAAA4B,cAAA,uBAAbA,cAAA,CAAenB,GAAG,CAACC,IAAI,KAAK;UAC3C9B,KAAK,EAAE8B,IAAI,CAAC9B,KAAK;UACjBC,GAAG,EAAE6B,IAAI,CAAC7B,GAAG;UACbC,IAAI,EAAE4B,IAAI,CAACE,IAAI,KAAK,QAAQ,gBAAGpH,KAAA,CAAAkE,aAAA,CAACjD,cAAc;YAAAoD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC,gBAAG1E,KAAA,CAAAkE,aAAA,CAAChD,YAAY;YAAAmD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC;UACpEc,MAAM,EAAE0B,IAAI,CAAC1B,MAAM;UACnBgB,IAAI,EAAE;YAAE,GAAGU,IAAI,CAACV,IAAI;YAAEI,MAAM,EAAE;UAAS,CAAC;UACxCrB,QAAQ,EAAE2B,IAAI,CAAC3B;QACjB,CAAC,CAAC,CAAC,KAAI,EAAE;QAET4C,QAAQ,CAAC5C,QAAQ,GAAGA,QAAQ;MAC9B,CAAC,MAAM,IAAIF,GAAG,KAAK,aAAa,EAAE;QAAA,IAAAiD,eAAA;QAChC;QACAhC,QAAQ,GAAG,MAAMzE,IAAI,CAACgC,GAAG,CAAC,oCAAoC,EAAE;UAC9D6B,MAAM,EAAE;YAAE2C,SAAS,EAAE;UAAS;QAChC,CAAC,CAAC;QAEF,MAAM9C,QAAQ,GAAG,EAAA+C,eAAA,GAAAhC,QAAQ,CAACE,IAAI,cAAA8B,eAAA,uBAAbA,eAAA,CAAerB,GAAG,CAACC,IAAI,KAAK;UAC3C9B,KAAK,EAAE8B,IAAI,CAAC9B,KAAK;UACjBC,GAAG,EAAE6B,IAAI,CAAC7B,GAAG;UACbC,IAAI,EAAE4B,IAAI,CAACE,IAAI,KAAK,QAAQ,gBAAGpH,KAAA,CAAAkE,aAAA,CAACjD,cAAc;YAAAoD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC,gBAAG1E,KAAA,CAAAkE,aAAA,CAAChD,YAAY;YAAAmD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC;UACpEc,MAAM,EAAE0B,IAAI,CAAC1B,MAAM;UACnBgB,IAAI,EAAE;YAAE,GAAGU,IAAI,CAACV,IAAI;YAAEI,MAAM,EAAE;UAAS,CAAC;UACxCrB,QAAQ,EAAE2B,IAAI,CAAC3B;QACjB,CAAC,CAAC,CAAC,KAAI,EAAE;QAET4C,QAAQ,CAAC5C,QAAQ,GAAGA,QAAQ;MAC9B,CAAC,MAAM,IAAIF,GAAG,CAACkD,UAAU,CAAC,QAAQ,CAAC,EAAE;QAAA,IAAAC,qBAAA;QACnC;QACAlC,QAAQ,GAAG,MAAMzE,IAAI,CAACgC,GAAG,CAAC,oCAAoC,EAAE;UAC9D6B,MAAM,EAAE;YAAEC,IAAI,EAAEa,IAAI,CAACb,IAAI;YAAE8C,KAAK,EAAE;UAAE;QACtC,CAAC,CAAC;QAEF,MAAMlD,QAAQ,GAAG,EAAAiD,qBAAA,GAAAlC,QAAQ,CAACE,IAAI,CAACjB,QAAQ,cAAAiD,qBAAA,uBAAtBA,qBAAA,CAAwBvB,GAAG,CAACC,IAAI,KAAK;UACpD9B,KAAK,EAAE,GAAG8B,IAAI,CAACE,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,IAAIF,IAAI,CAAC9B,KAAK,EAAE;UAC9DC,GAAG,EAAE,SAAS6B,IAAI,CAAC7B,GAAG,EAAE;UACxBC,IAAI,EAAE4B,IAAI,CAACE,IAAI,KAAK,QAAQ,gBAAGpH,KAAA,CAAAkE,aAAA,CAACjD,cAAc;YAAAoD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC,gBAAG1E,KAAA,CAAAkE,aAAA,CAAChD,YAAY;YAAAmD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC;UACpEc,MAAM,EAAE0B,IAAI,CAAC1B,MAAM;UACnBgB,IAAI,EAAE;YAAE,GAAGU,IAAI;YAAEN,MAAM,EAAE;UAAQ;QACnC,CAAC,CAAC,CAAC,KAAI,EAAE;QAETuB,QAAQ,CAAC5C,QAAQ,GAAGA,QAAQ;MAC9B,CAAC,MAAM,IAAIF,GAAG,CAACkD,UAAU,CAAC,SAAS,CAAC,EAAE;QAAA,IAAAG,eAAA;QACpC;QACApC,QAAQ,GAAG,MAAMzE,IAAI,CAACgC,GAAG,CAAC,wCAAwC,EAAE;UAClE6B,MAAM,EAAE;YAAEC,IAAI,EAAEa,IAAI,CAACb;UAAK;QAC5B,CAAC,CAAC;QAEF,MAAMJ,QAAQ,GAAG,EAAAmD,eAAA,GAAApC,QAAQ,CAACE,IAAI,cAAAkC,eAAA,uBAAbA,eAAA,CAAezB,GAAG,CAACC,IAAI,KAAK;UAC3C9B,KAAK,EAAE,GAAGpB,WAAW,CAACkD,IAAI,CAACY,MAAM,CAAC,KAAKZ,IAAI,CAACE,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACP,IAAI,EAAE;UAC3FtB,GAAG,EAAE,UAAU6B,IAAI,CAACvB,IAAI,CAACgD,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE;UACxDrD,IAAI,EAAE4B,IAAI,CAACE,IAAI,KAAK,QAAQ,gBAAGpH,KAAA,CAAAkE,aAAA,CAACjD,cAAc;YAAAoD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC,gBAAG1E,KAAA,CAAAkE,aAAA,CAAChD,YAAY;YAAAmD,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC;UACpEc,MAAM,EAAE0B,IAAI,CAACE,IAAI,KAAK,MAAM;UAC5BZ,IAAI,EAAE;YAAE,GAAGU,IAAI;YAAEN,MAAM,EAAE;UAAS;QACpC,CAAC,CAAC,CAAC,KAAI,EAAE;QAETuB,QAAQ,CAAC5C,QAAQ,GAAGA,QAAQ;MAC9B;;MAEA;MACA,IAAIF,GAAG,CAACkD,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC5BnG,gBAAgB,CAAC,CAAC,GAAGD,aAAa,CAAC,CAAC;MACtC,CAAC,MAAM;QACLG,iBAAiB,CAAC,CAAC,GAAGD,cAAc,CAAC,CAAC;MACxC;IAEF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC;EAED,MAAM0F,QAAQ,GAAG,MAAAA,CAAOC,YAAY,EAAEC,IAAI,KAAK;IAC7C,IAAI,CAACD,YAAY,CAAC7B,MAAM,EAAE;IAE1B,MAAM;MAAE+B;IAAK,CAAC,GAAGD,IAAI;IACrB,MAAM;MAAEtC;IAAK,CAAC,GAAGuC,IAAI;IAErBhG,eAAe,CAAC,CAAAyD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEb,IAAI,KAAI,EAAE,CAAC;;IAEjC;IACA,IAAI,CAAAa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,IAAI,MAAK,QAAQ,KAAIZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEb,IAAI,GAAE;MACzC,IAAI;QACF,MAAMW,QAAQ,GAAG,MAAMzE,IAAI,CAACgC,GAAG,CAAC,kCAAkC,EAAE;UAClE6B,MAAM,EAAE;YAAEC,IAAI,EAAEa,IAAI,CAACb;UAAK;QAC5B,CAAC,CAAC;QACF1C,iBAAiB,CAACqD,QAAQ,CAACE,IAAI,CAAC;MAClC,CAAC,CAAC,OAAOtD,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF;EACF,CAAC;EAED,MAAM8F,QAAQ,GAAIzG,YAAY,IAAK;IACjCC,eAAe,CAACD,YAAY,CAAC;EAC/B,CAAC;EAED,MAAM0G,aAAa,GAAGA,CAAA,KAAM;IAC1B/D,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMgE,UAAU,GAAG,MAAOvD,IAAI,IAAK;IACjC,IAAI;MACF,MAAM9D,IAAI,CAACsH,IAAI,CAAC,sCAAsC,EAAE;QAAEC,SAAS,EAAEzD;MAAK,CAAC,CAAC;MAC5E;MACAT,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMmG,oBAAoB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACjC,IAAI,CAAC9G,cAAc,EAAE,OAAO,IAAI;IAEhC,oBACEhD,KAAA,CAAAkE,aAAA,CAAC5D,IAAI;MAAC8E,KAAK,EAAC,sCAAQ;MAAC2E,IAAI,EAAC,OAAO;MAACC,SAAS,EAAElI,MAAM,CAACkB,cAAe;MAAAqB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACjE1E,KAAA,CAAAkE,aAAA,CAACnD,GAAG;MAACkJ,MAAM,EAAE,EAAG;MAAA5F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACd1E,KAAA,CAAAkE,aAAA,CAAClD,GAAG;MAACkJ,IAAI,EAAE,EAAG;MAAA7F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACZ1E,KAAA,CAAAkE,aAAA,CAAC5D,IAAI;MAACyJ,IAAI,EAAC,OAAO;MAAC3E,KAAK,EAAC,0BAAM;MAAC+E,QAAQ,EAAE,KAAM;MAAA9F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9C1E,KAAA,CAAAkE,aAAA;MAAK8F,SAAS,EAAElI,MAAM,CAACsI,UAAW;MAAA/F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChC1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,eAAW,CAAC,KAAC,EAAC,EAAA4E,qBAAA,GAAAtG,cAAc,CAACqH,KAAK,cAAAf,qBAAA,uBAApBA,qBAAA,CAAsB3D,IAAI,KAAI,KAAS,CAAC,eACjE3F,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,eAAW,CAAC,KAAC,EAAC,EAAA6E,sBAAA,GAAAvG,cAAc,CAACqH,KAAK,cAAAd,sBAAA,uBAApBA,sBAAA,CAAsBQ,IAAI,KAAI,GAAO,CAAC,eAC/D/J,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,2BAAa,CAAC,KAAC,EAAC,EAAA8E,sBAAA,GAAAxG,cAAc,CAACqH,KAAK,cAAAb,sBAAA,uBAApBA,sBAAA,CAAsBc,YAAY,KAAI,GAAO,CAAC,eACzEtK,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,eAAW,CAAC,KAAC,EAACK,UAAU,EAAA0E,sBAAA,GAACzG,cAAc,CAACqH,KAAK,cAAAZ,sBAAA,uBAApBA,sBAAA,CAAsB3B,MAAM,CAAC,IAAI/C,UAAU,CAACH,OAAW,CACxF,CACD,CACH,CAAC,eACN5E,KAAA,CAAAkE,aAAA,CAAClD,GAAG;MAACkJ,IAAI,EAAE,EAAG;MAAA7F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACZ1E,KAAA,CAAAkE,aAAA,CAAC5D,IAAI;MAACyJ,IAAI,EAAC,OAAO;MAAC3E,KAAK,EAAC,0BAAM;MAAC+E,QAAQ,EAAE,KAAM;MAAA9F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9C1E,KAAA,CAAAkE,aAAA;MAAK8F,SAAS,EAAElI,MAAM,CAACsI,UAAW;MAAA/F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChC1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,eAAW,CAAC,KAAC,EAAC,EAAAgF,qBAAA,GAAA1G,cAAc,CAACuH,MAAM,cAAAb,qBAAA,uBAArBA,qBAAA,CAAuB/D,IAAI,KAAI,KAAS,CAAC,eAClE3F,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,eAAW,CAAC,KAAC,EAAC,EAAAiF,sBAAA,GAAA3G,cAAc,CAACuH,MAAM,cAAAZ,sBAAA,uBAArBA,sBAAA,CAAuBI,IAAI,KAAI,GAAO,CAAC,eAChE/J,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,2BAAa,CAAC,KAAC,EAAC,EAAAkF,sBAAA,GAAA5G,cAAc,CAACuH,MAAM,cAAAX,sBAAA,uBAArBA,sBAAA,CAAuBU,YAAY,KAAI,GAAO,CAAC,eAC1EtK,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAAG1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAQ,eAAW,CAAC,KAAC,EAACK,UAAU,EAAA8E,sBAAA,GAAC7G,cAAc,CAACuH,MAAM,cAAAV,sBAAA,uBAArBA,sBAAA,CAAuB/B,MAAM,CAAC,IAAI/C,UAAU,CAACH,OAAW,CACzF,CACD,CACH,CACF,CAAC,eACN5E,KAAA,CAAAkE,aAAA,CAACpD,OAAO;MAAAuD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eACX1E,KAAA,CAAAkE,aAAA;MAAK8F,SAAS,EAAElI,MAAM,CAAC0I,WAAY;MAAAnG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACjC1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,2BAAS,CAAC,eACd1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI1B,cAAc,CAACyH,YAAgB,CAAC,EACnC,EAAAX,sBAAA,GAAA9G,cAAc,CAACqH,KAAK,cAAAP,sBAAA,uBAApBA,sBAAA,CAAsBhC,MAAM,MAAK,QAAQ,iBACxC9H,KAAA,CAAAkE,aAAA,CAACvD,MAAM;MACLyG,IAAI,EAAC,SAAS;MACd9B,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAAC7C,YAAY;QAAAgD,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAE;MACvBgG,OAAO,EAAEA,CAAA,KAAMxB,UAAU,CAACpG,YAAY,CAAE;MAAAuB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACzC,0BAEO,CAEP,CACD,CAAC;EAEX,CAAC;EAED,MAAMiG,cAAc,GAAGA,CAACnE,IAAI,EAAE/D,WAAW,KAAK;IAC5C,IAAI,CAACA,WAAW,EAAE,OAAO+D,IAAI;IAE7B,OAAOA,IAAI,CAACS,GAAG,CAAC8B,IAAI,IAAI;MACtB,MAAM6B,OAAO,GAAG7B,IAAI,CAAC3D,KAAK,CAACyF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrI,WAAW,CAACoI,WAAW,CAAC,CAAC,CAAC;MAC5E,MAAME,gBAAgB,GAAGhC,IAAI,CAACxD,QAAQ,GAAGoF,cAAc,CAAC5B,IAAI,CAACxD,QAAQ,EAAE9C,WAAW,CAAC,GAAG,EAAE;MAExF,IAAImI,OAAO,IAAIG,gBAAgB,CAAC/D,MAAM,GAAG,CAAC,EAAE;QAC1C,OAAO;UACL,GAAG+B,IAAI;UACPxD,QAAQ,EAAEwF;QACZ,CAAC;MACH;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACpD,MAAM,CAACqD,OAAO,CAAC;EACpB,CAAC;;EAED;EACA9K,SAAS,CAAC,MAAM;IACd,IAAI2C,cAAc,CAACoI,OAAO,EAAE;MAC1B9F,YAAY,CAACtC,cAAc,CAACoI,OAAO,CAAC;IACtC;IAEApI,cAAc,CAACoI,OAAO,GAAGhG,UAAU,CAAC,MAAM;MACxCrC,uBAAuB,CAACH,WAAW,CAAC;IACtC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACX,IAAII,cAAc,CAACoI,OAAO,EAAE;QAC1B9F,YAAY,CAACtC,cAAc,CAACoI,OAAO,CAAC;MACtC;IACF,CAAC;EACH,CAAC,EAAE,CAACxI,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMyI,iBAAiB,GAAG9K,OAAO,CAAC,MAAM;IACtCgD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,OAAOsH,cAAc,CAACxI,aAAa,EAAEQ,oBAAoB,CAAC;EAC5D,CAAC,EAAE,CAACR,aAAa,EAAEQ,oBAAoB,CAAC,CAAC;EAEzC,MAAMwI,kBAAkB,GAAG/K,OAAO,CAAC,MAAM;IACvCgD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,OAAOsH,cAAc,CAACtI,cAAc,EAAEM,oBAAoB,CAAC;EAC7D,CAAC,EAAE,CAACN,cAAc,EAAEM,oBAAoB,CAAC,CAAC;EAE1C,oBACE3C,KAAA,CAAAkE,aAAA;IAAK8F,SAAS,EAAElI,MAAM,CAACsJ,2BAA4B;IAAA/G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjD1E,KAAA,CAAAkE,aAAA,CAACzD,KAAK;IACJuF,OAAO,EAAE/D,OAAO,GAAG,cAAc,GAAG,oBAAqB;IACzDoJ,WAAW,EAAEpJ,OAAO,GAClB,sBAAsB,GACtB,OAAO8B,MAAM,CAACH,QAAQ,MAAMG,MAAM,CAACD,SAAS,EAC7C;IACDsD,IAAI,EAAEnF,OAAO,GAAG,SAAS,GAAG,MAAO;IACnCkC,KAAK,EAAE;MAAEmH,YAAY,EAAE;IAAG,CAAE;IAC5BC,QAAQ;IACRC,MAAM,eACJxL,KAAA,CAAAkE,aAAA,CAACrD,KAAK;MAAAwD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACH,CAACzC,OAAO,iBACPjC,KAAA,CAAAkE,aAAA,CAACvD,MAAM;MACLoJ,IAAI,EAAC,OAAO;MACZzE,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAACxC,kBAAkB;QAAA2C,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAE;MAC7BgG,OAAO,EAAEA,CAAA;QAAA,IAAAe,qBAAA,EAAAC,sBAAA;QAAA,QAAAD,qBAAA,GAAM,CAAAC,sBAAA,GAAAC,QAAQ,CAACC,eAAe,EAACC,iBAAiB,cAAAJ,qBAAA,uBAA1CA,qBAAA,CAAAK,IAAA,CAAAJ,sBAA6C,CAAC;MAAA,CAAC;MAAArH,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC/D,cAEO,CAEL,CACR;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACF,CAAC,eAEF1E,KAAA,CAAAkE,aAAA,CAAC5D,IAAI;IACH8E,KAAK,eACHpF,KAAA,CAAAkE,aAAA,CAACrD,KAAK;MAAAwD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACJ1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAM,sCAAY,CAAC,EAClBzC,OAAO,iBAAIjC,KAAA,CAAAkE,aAAA,CAAC1D,IAAI;MAACuJ,IAAI,EAAC,OAAO;MAAA1F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAC3B,CACR;IACDqH,KAAK,eACH/L,KAAA,CAAAkE,aAAA,CAACrD,KAAK;MAAAwD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACJ1E,KAAA,CAAAkE,aAAA,CAACnC,MAAM;MACLiK,WAAW,EAAC,4CAAS;MACrBC,UAAU;MACVC,KAAK,EAAEzJ,WAAY;MACnB0J,QAAQ,EAAGC,CAAC,IAAK1J,cAAc,CAAC0J,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;MAChD/H,KAAK,EAAE;QAAEmI,KAAK,EAAE;MAAI,CAAE;MACtBC,MAAM,eAAEvM,KAAA,CAAAkE,aAAA,CAAC9C,cAAc;QAAAiD,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAE;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAC5B,CAAC,eACF1E,KAAA,CAAAkE,aAAA,CAACvD,MAAM;MACL2E,IAAI,eAAEtF,KAAA,CAAAkE,aAAA,CAAC/C,cAAc;QAAAkD,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAE;MACzBgG,OAAO,EAAEzB,aAAc;MACvBhH,OAAO,EAAEA,OAAQ;MAAAoC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAClB,cAEO,CACH,CACR;IACDsF,SAAS,EAAElI,MAAM,CAAC0K,cAAe;IACjCC,SAAS,EAAE;MAAEC,MAAM,EAAE,qBAAqB;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAtI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE7DxB,KAAK,iBACJlD,KAAA,CAAAkE,aAAA,CAACzD,KAAK;IACJuF,OAAO,EAAC,0BAAM;IACdqF,WAAW,EAAEnI,KAAM;IACnBkE,IAAI,EAAC,OAAO;IACZjD,KAAK,EAAE;MAAEmH,YAAY,EAAE;IAAG,CAAE;IAC5BE,MAAM,eACJxL,KAAA,CAAAkE,aAAA,CAACvD,MAAM;MACLoJ,IAAI,EAAC,OAAO;MACZ3C,IAAI,EAAC,SAAS;MACdsD,OAAO,EAAExF,eAAgB;MAAAb,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC1B,cAEO,CACT;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACF,CACF,eAED1E,KAAA,CAAAkE,aAAA,CAACnD,GAAG;IAACkJ,MAAM,EAAE,EAAG;IAAC9F,KAAK,EAAE;MAAEuI,MAAM,EAAE;IAAO,CAAE;IAAArI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzC1E,KAAA,CAAAkE,aAAA,CAAClD,GAAG;IAACkJ,IAAI,EAAE,EAAG;IAAA7F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACZ1E,KAAA,CAAAkE,aAAA,CAAC5D,IAAI;IACHyJ,IAAI,EAAC,OAAO;IACZ3E,KAAK,eACHpF,KAAA,CAAAkE,aAAA,CAACrD,KAAK;MAAAwD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACJ1E,KAAA,CAAAkE,aAAA,CAACtC,kBAAkB;MAAAyC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eACtB1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAM,0BAAU,CAAC,eACjB1E,KAAA,CAAAkE,aAAA,CAACxD,GAAG;MAAC0D,KAAK,EAAC,MAAM;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAEX,MAAM,CAACH,QAAc,CACnC,CACR;IACDuG,QAAQ,EAAE,KAAM;IAChBH,SAAS,EAAElI,MAAM,CAAC8K,QAAS;IAC3BH,SAAS,EAAE;MAAEC,MAAM,EAAE,qBAAqB;MAAEG,QAAQ,EAAE;IAAO,CAAE;IAAAxI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/D1E,KAAA,CAAAkE,aAAA,CAAC1D,IAAI;IAACsM,QAAQ,EAAE7K,OAAQ;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB1E,KAAA,CAAAkE,aAAA,CAAC3D,IAAI;IACHwM,QAAQ,EAAE7E,UAAW;IACrB8E,QAAQ,EAAE9B,iBAAkB;IAC5BtC,QAAQ,EAAEA,QAAS;IACnBI,QAAQ,EAAEA,QAAS;IACnBzG,YAAY,EAAEA,YAAY,CAACoF,MAAM,CAACtC,GAAG,IAAIA,GAAG,CAACkD,UAAU,CAAC,QAAQ,CAAC,CAAE;IACnEgD,QAAQ;IACRmB,MAAM,EAAC,MAAM;IACb1C,SAAS,EAAElI,MAAM,CAAC+D,SAAU;IAAAxB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC7B,CACG,CACF,CACH,CAAC,eACN1E,KAAA,CAAAkE,aAAA,CAAClD,GAAG;IAACkJ,IAAI,EAAE,EAAG;IAAA7F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACZ1E,KAAA,CAAAkE,aAAA,CAAC5D,IAAI;IACHyJ,IAAI,EAAC,OAAO;IACZ3E,KAAK,eACHpF,KAAA,CAAAkE,aAAA,CAACrD,KAAK;MAAAwD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACJ1E,KAAA,CAAAkE,aAAA,CAACvC,cAAc;MAAA0C,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eAClB1E,KAAA,CAAAkE,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAM,0BAAU,CAAC,eACjB1E,KAAA,CAAAkE,aAAA,CAACxD,GAAG;MAAC0D,KAAK,EAAC,QAAQ;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAEX,MAAM,CAACD,SAAe,CACtC,CACR;IACDqG,QAAQ,EAAE,KAAM;IAChBH,SAAS,EAAElI,MAAM,CAAC8K,QAAS;IAC3BH,SAAS,EAAE;MAAEC,MAAM,EAAE,qBAAqB;MAAEG,QAAQ,EAAE;IAAO,CAAE;IAAAxI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/D1E,KAAA,CAAAkE,aAAA,CAAC1D,IAAI;IAACsM,QAAQ,EAAE7K,OAAQ;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB1E,KAAA,CAAAkE,aAAA,CAAC3D,IAAI;IACHwM,QAAQ,EAAE7E,UAAW;IACrB8E,QAAQ,EAAE7B,kBAAmB;IAC7BvC,QAAQ,EAAEA,QAAS;IACnBI,QAAQ,EAAEA,QAAS;IACnBzG,YAAY,EAAEA,YAAY,CAACoF,MAAM,CAACtC,GAAG,IAAIA,GAAG,CAACkD,UAAU,CAAC,SAAS,CAAC,CAAE;IACpEgD,QAAQ;IACRmB,MAAM,EAAC,MAAM;IACb1C,SAAS,EAAElI,MAAM,CAACqE,UAAW;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC9B,CACG,CACF,CACH,CACF,CAAC,EAEL5B,YAAY,iBACX9C,KAAA,CAAAkE,aAAA;IAAK8F,SAAS,EAAElI,MAAM,CAACgB,YAAa;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC1E,KAAA,CAAAkE,aAAA,CAACzD,KAAK;IACJuF,OAAO,EAAE,QAAQlD,YAAY,EAAG;IAChCsE,IAAI,EAAC,MAAM;IACXmE,QAAQ;IACRpH,KAAK,EAAE;MAAE8I,SAAS,EAAE;IAAG,CAAE;IAAA5I,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC1B,CACE,CACN,EAEA2E,oBAAoB,CAAC,CAClB,CACH,CAAC;AAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module"}