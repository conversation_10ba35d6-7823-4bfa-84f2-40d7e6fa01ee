# -*- coding: utf-8 -*-
"""
Model Storage 模块配置
"""

import os
from django.conf import settings

# 文件路径配置
class FilePathConfig:
    """文件路径配置类"""
    
    @staticmethod
    def get_temp_dir():
        """获取临时目录"""
        return getattr(settings, 'MODEL_STORAGE_TEMP_DIR', 
                      os.path.join(settings.BASE_DIR, 'storage', 'temp', 'model_storage'))
    
    @staticmethod
    def get_cache_dir():
        """获取缓存目录"""
        return getattr(settings, 'MODEL_STORAGE_CACHE_DIR',
                      os.path.join(settings.BASE_DIR, 'storage', 'cache', 'model_storage'))
    
    @staticmethod
    def get_log_dir():
        """获取日志目录"""
        return getattr(settings, 'MODEL_STORAGE_LOG_DIR',
                      os.path.join(settings.BASE_DIR, 'logs', 'model_storage'))

# 网络配置
class NetworkConfig:
    """网络配置类"""
    
    @staticmethod
    def get_remote_url():
        """获取远程仓库URL"""
        return getattr(settings, 'MODEL_STORAGE_REMOTE_URL', 
                      "http://10.63.30.93/GPU_MODEL_REPO/01.DEV/")
    
    @staticmethod
    def get_auth_credentials():
        """获取认证凭据"""
        username = getattr(settings, 'MODEL_STORAGE_USERNAME', 'sys49169')
        password = getattr(settings, 'MODEL_STORAGE_PASSWORD', 'Aa123,.,.') 
        return (username, password)
    
    @staticmethod
    def get_timeout():
        """获取网络超时时间"""
        return getattr(settings, 'MODEL_STORAGE_TIMEOUT', 15)
    
    @staticmethod
    def get_retry_config():
        """获取重试配置"""
        return {
            'max_retries': getattr(settings, 'MODEL_STORAGE_MAX_RETRIES', 3),
            'base_delay': getattr(settings, 'MODEL_STORAGE_RETRY_DELAY', 1.0),
            'max_delay': getattr(settings, 'MODEL_STORAGE_MAX_RETRY_DELAY', 30.0)
        }

# 数据库配置
class DatabaseConfig:
    """数据库配置类"""
    
    @staticmethod
    def get_retry_config():
        """获取数据库重试配置"""
        return {
            'max_retries': getattr(settings, 'MODEL_STORAGE_DB_MAX_RETRIES', 3),
            'base_delay': getattr(settings, 'MODEL_STORAGE_DB_RETRY_DELAY', 0.1),
            'max_delay': getattr(settings, 'MODEL_STORAGE_DB_MAX_RETRY_DELAY', 5.0)
        }
    
    @staticmethod
    def get_cleanup_config():
        """获取数据清理配置"""
        return {
            'metrics_retention_days': getattr(settings, 'MODEL_STORAGE_METRICS_RETENTION_DAYS', 7),
            'cache_retention_hours': getattr(settings, 'MODEL_STORAGE_CACHE_RETENTION_HOURS', 24)
        }

# 监控配置
class MonitoringConfig:
    """监控配置类"""
    
    @staticmethod
    def get_metrics_config():
        """获取指标配置"""
        return {
            'collection_interval': getattr(settings, 'MODEL_STORAGE_METRICS_INTERVAL', 60),
            'enable_network_monitoring': getattr(settings, 'MODEL_STORAGE_ENABLE_NETWORK_MONITORING', True),
            'enable_disk_monitoring': getattr(settings, 'MODEL_STORAGE_ENABLE_DISK_MONITORING', True),
            'enable_memory_monitoring': getattr(settings, 'MODEL_STORAGE_ENABLE_MEMORY_MONITORING', True)
        }
    
    @staticmethod
    def get_alert_config():
        """获取告警配置"""
        return {
            'cpu_threshold': getattr(settings, 'MODEL_STORAGE_CPU_THRESHOLD', 80.0),
            'memory_threshold': getattr(settings, 'MODEL_STORAGE_MEMORY_THRESHOLD', 85.0),
            'disk_threshold': getattr(settings, 'MODEL_STORAGE_DISK_THRESHOLD', 90.0)
        }

# 线程池配置
class ThreadPoolConfig:
    """线程池配置类"""
    
    @staticmethod
    def get_max_workers():
        """获取最大工作线程数"""
        return getattr(settings, 'MODEL_STORAGE_MAX_WORKERS', 3)
    
    @staticmethod
    def get_timeout():
        """获取线程池超时时间"""
        return getattr(settings, 'MODEL_STORAGE_THREAD_TIMEOUT', 30)

# 文件扫描配置
class ScanConfig:
    """文件扫描配置类"""
    
    @staticmethod
    def get_max_files():
        """获取最大文件扫描数量"""
        return getattr(settings, 'MODEL_STORAGE_MAX_SCAN_FILES', 1000)
    
    @staticmethod
    def get_max_depth():
        """获取最大扫描深度"""
        return getattr(settings, 'MODEL_STORAGE_MAX_SCAN_DEPTH', 3)
    
    @staticmethod
    def get_important_extensions():
        """获取重要文件扩展名"""
        return getattr(settings, 'MODEL_STORAGE_IMPORTANT_EXTENSIONS', 
                      ['.bin', '.safetensors', '.json', '.txt', '.md', '.py', '.yaml', '.yml'])
    
    @staticmethod
    def get_scan_timeout():
        """获取扫描超时时间"""
        return getattr(settings, 'MODEL_STORAGE_SCAN_TIMEOUT', 300)

# 安全配置
class SecurityConfig:
    """安全配置类"""
    
    @staticmethod
    def get_allowed_paths():
        """获取允许访问的路径"""
        return getattr(settings, 'MODEL_STORAGE_ALLOWED_PATHS', [
            '/HDD_Raid/SVN_MODEL_REPO',
            '/tmp/model_storage',
            os.path.join(settings.BASE_DIR, 'storage')
        ])
    
    @staticmethod
    def get_file_size_limit():
        """获取文件大小限制（字节）"""
        return getattr(settings, 'MODEL_STORAGE_FILE_SIZE_LIMIT', 100 * 1024 * 1024)  # 100MB
    
    @staticmethod
    def is_path_allowed(path):
        """检查路径是否被允许"""
        abs_path = os.path.abspath(path)
        allowed_paths = SecurityConfig.get_allowed_paths()
        
        for allowed_path in allowed_paths:
            if abs_path.startswith(os.path.abspath(allowed_path)):
                return True
        return False

# 导出配置实例
file_path_config = FilePathConfig()
network_config = NetworkConfig()
database_config = DatabaseConfig()
monitoring_config = MonitoringConfig()
thread_pool_config = ThreadPoolConfig()
scan_config = ScanConfig()
security_config = SecurityConfig()
